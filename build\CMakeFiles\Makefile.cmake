# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.27

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeCInformation.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeCXXInformation.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeCommonLanguageInclude.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeDependentOption.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeGenericSystem.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeInitializeConfigs.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeLanguageInformation.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeRCInformation.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeSystemSpecificInformation.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeSystemSpecificInitialize.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Compiler/GNU-C.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Compiler/GNU-CXX.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Compiler/GNU.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-GNU-C-ABI.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-GNU-C.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-GNU-CXX.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-GNU.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-Initialize.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-windres.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows.cmake"
  "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/WindowsPaths.cmake"
  "CMakeFiles/3.27.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.27.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.27.3/CMakeRCCompiler.cmake"
  "CMakeFiles/3.27.3/CMakeSystem.cmake"
  "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/fix/quickfix-ffi/CMakeLists.txt"
  "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/fix/quickfix-ffi/examples/CMakeLists.txt"
  "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/fix/quickfix-ffi/quickfix-bind/CMakeLists.txt"
  "C:/Users/<USER>/rainerix/vcpkg/scripts/buildsystems/vcpkg.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "quickfix-bind/CMakeFiles/CMakeDirectoryInformation.cmake"
  "examples/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "quickfix-bind/CMakeFiles/quickfixbind.dir/DependInfo.cmake"
  "examples/CMakeFiles/demo_basic_binding.dir/DependInfo.cmake"
  )
