{"artifacts": [{"path": "examples/demo_basic_binding.exe"}, {"path": "examples/demo_basic_binding.pdb"}], "backtrace": 2, "backtraceGraph": {"commands": ["_add_executable", "add_executable", "target_link_libraries", "include_directories"], "files": ["C:/Users/<USER>/rainerix/vcpkg/scripts/buildsystems/vcpkg.cmake", "examples/CMakeLists.txt", "quickfix-bind/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"command": 0, "file": 0, "line": 598, "parent": 1}, {"command": 2, "file": 1, "line": 4, "parent": 0}, {"file": 2}, {"command": 2, "file": 2, "line": 10, "parent": 4}, {"command": 3, "file": 1, "line": 1, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g"}], "includes": [{"backtrace": 6, "path": "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/fix/quickfix-ffi/examples/../quickfix-bind/include"}], "language": "C", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 3, "id": "quickfixbind::@4cf7d16ebaee8de20a4a"}], "id": "demo_basic_binding::@714eac15f71a94114fdf", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 3, "fragment": "..\\quickfix-bind\\libquickfixbind.a", "role": "libraries"}, {"backtrace": 5, "fragment": "-l<PERSON>ckfixd", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "demo_basic_binding", "nameOnDisk": "demo_basic_binding.exe", "paths": {"build": "examples", "source": "examples"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "examples/demo_basic_binding.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}