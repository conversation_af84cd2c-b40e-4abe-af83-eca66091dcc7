use axum::{
    body::Body,
    http::{Request, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
};
use std::net::IpAddr;
use std::str::FromStr;
use tracing::*;

use crate::service::AllowMethod;

// Configuration for the IP allow list
#[derive(<PERSON><PERSON>, Debug)]
pub struct AllowList {
    allowed_ips: Vec<String>,
    allowmethod: i32, //1: ip 2:domain
}

impl AllowList {
    pub fn new(allowed_ips: Vec<&str>, allowmethod: i32) -> Self {
        let parsed_ips = allowed_ips.into_iter().map(|ip| ip.to_string()).collect();
        AllowList {
            allowed_ips: parsed_ips,
            allowmethod,
        }
    }

    pub fn is_allowed(&self, ip: &String) -> bool {
        self.allowed_ips.len() <= 0 || self.allowed_ips.contains(&ip)
    }
}

// Middleware function to check the client's IP
pub async fn ip_allow_list_middleware(req: Request<Body>, next: Next) -> Result<Response, Response> {
    // Get the IP allow list from the request extensions
    let allow_list = req
        .extensions()
        .get::<AllowList>()
        .ok_or_else(|| (StatusCode::INTERNAL_SERVER_ERROR, "IP allow list not configured").into_response())?;

    let allow_method = allow_list.allowmethod;
    let ip_or_domain: String; // = "".to_string();
    if allow_method == AllowMethod::IP as i32 {
        // Extract the client's IP address
        let clientip = get_client_ip(&req).ok_or_else(|| (StatusCode::BAD_REQUEST, "Could not determine client IP").into_response())?;
        info!("current client ip is:{}", clientip);
        ip_or_domain = clientip.to_string();
    } else {
        let host = req.headers().get("host").and_then(|h| h.to_str().ok()).unwrap_or("");
        let domain = host.split(':').next().unwrap_or("");
        info!("current host is:{}", domain);
        ip_or_domain = domain.to_string();
    }
    // Check if the IP is allowed
    // info!("{:#?}", allow_list);
    if allow_list.is_allowed(&ip_or_domain.to_string()) {
        // IP is allowed, proceed to the next handler
        info!("{} is allowed", ip_or_domain);
        Ok(next.run(req).await)
    } else {
        // IP is not allowed, return 403 Forbidden
        Err((StatusCode::FORBIDDEN, format!("{} is not allowed", ip_or_domain)).into_response())
    }
}

// Helper function to extract the client's IP address
fn get_client_ip<B>(req: &Request<B>) -> Option<IpAddr> {
    // First, check the X-Forwarded-For header (for proxies)
    if let Some(forwarded_for) = req.headers().get("X-Forwarded-For") {
        let forwarded_for = forwarded_for.to_str().ok()?;
        // X-Forwarded-For may contain multiple IPs; take the first one
        let first_ip = forwarded_for.split(',').next()?.trim();
        return IpAddr::from_str(first_ip).ok();
    }

    // Fallback to the remote address from the connection
    req.extensions()
        .get::<axum::extract::ConnectInfo<std::net::SocketAddr>>()
        .map(|info| info.0.ip())
}
