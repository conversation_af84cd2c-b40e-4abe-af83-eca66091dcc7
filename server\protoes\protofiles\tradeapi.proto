
// 交易API协议定义
// 该协议用于交易所柜台与客户端之间的通信,通信方式采用socket TCP/IP连接
// 该协议采用protobuf序列化方式进行数据传输，前面4字节传输数据长度，最多传送1GB数据
// 处理时，先读取固定4字节长度，然后根据长度读取数据包，采用big-endian字节序

syntax = "proto3";
package tradeapi;

// 消息头
message TAMessageHeader{
    string begin_string = 1;
    int32 message_type = 2; 
    string sender_compid = 3;//发送方ID
    int64 sending_time = 4;//发送时间 时间戳 毫秒级
    string client_id=5;
}


//返回
message TAMessageResp {
    StatusCode error_code = 1;
    string error_msg =2;
}

// ---------------------------- 登录请求 -----------------------------
message TALogonReq {
    string password = 1;
    string username = 2;
    string client_id = 3;
}

//----------------------------------下单请求----------------------------------
message TAEntrustOrderReq {
    string cl_ord_id = 1;//Unique identifier of the order as assigned by institution. 客户委托订单号
    int32 account = 2; //用户编号
    string handl_inst = 3;//1：dma，3=manual order，默认1
    string symbol = 4; //证券代码
    SecurityExchange security_exchange = 5; //交易所 XSHG XSHE XHKG
    OrderDirection side =6; // 1=买  2=卖
    int64 transact_time = 7; //交易时间，时间戳,毫秒
    int32 order_qty = 8;
    PriceType ord_type = 9; // 1:市价 2:限价
    string price = 10;//Required for limit order type
    optional string currency = 11; //币种 optional
}

// TAEntrustWithdrawReq 撤单请求数据类型
message TAEntrustWithdrawReq {
    string orig_cl_ord_id =1;//ClOrdID of the previous order,柜台返回的订单号
    string cl_ord_id =2;//Unique identifier of replacement order as assigned by institution 原客户委托订单号
    int64 transaction_time =3;//时间戳 毫秒级
    int32 account = 4; //用户编号
}

//  订单响应
message TAOrderExecResp {
    string cl_ord_id = 1;  //原客户委托订单号
    string orig_cl_ord_id = 2; //柜台订单号
    string exec_id =3;//执行成交编号
    int32 cum_qty=4;//成交总数
    int32 last_shares=5;//最近一笔成交量
    string exec_trans_type=6; //执行类型，默认填0
    string exec_type=7;//订单类型 1：市价，2：限价
    OrderExecStatus order_status =8; //订单状态， 0：已报，1：拒绝，2：部成，3：已成，4：部撤，5：已撤
    string account=9;
    string symbol=10;
    SecurityExchange security_exchange=11;
    OrderDirection side=12;
    int64 transaction_time =13;//时间戳 毫秒级
    string mark=14;//备注
    string last_px=15;//最后一笔成交价格
}
// ----- 从客户端发起 ----------
message TAMessageReqPack{
    TAMessageHeader header=1;
    TALogonReq logon=2;
    TAEntrustOrderReq order=3;
    TAEntrustWithdrawReq cancelorder=4;
}

// ----- 从服务器发起 -----------
message TAMessageRespPack{
    TAMessageHeader header=1;
    TAOrderExecResp orderexec=2;
    TAMessageResp resp=3;
}

enum OrderDirection {
    Unknown = 0;
    BUY = 1;
    SELL = 2;
}
enum PriceType {
    UnKnown = 0;
    MARKET = 1;
    LIMIT = 2;
}

enum MessageType {
    Undefined = 0;
    LOGON = 1;
    PLACE_ORDER = 2;
    CANCEL_ORDER = 3;
    ORDER_EXEC = 4;
    ORDER_STATUS = 5;
    RESP=99;
}

enum StatusCode {
    OK = 0;
    Error = 1;
}

//交易所 XSHG XSHE XHKG
enum SecurityExchange{
    Unknowns = 0;
    XSHG = 1;//上交所
    XSHE =2;//深交所
    XHKG=3;//港交所
}

enum OrderExecStatus{
    Reported=0;
    Refuse=1;
    Partial_Transaction=2;
    Already_Formed=3;
    Department_Withdrawal=4;
    Already_Withdrawn=5;
}