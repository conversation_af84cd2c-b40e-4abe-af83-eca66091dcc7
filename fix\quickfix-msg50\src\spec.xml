<fix type='FIXT' major='1' minor='1' servicepack='0'>
 <header>
  <field name='BeginString' required='Y'/>
  <field name='BodyLength' required='Y'/>
  <field name='MsgType' required='Y'/>
  <field name='ApplVerID' required='N'/>
  <field name='ApplExtID' required='N'/>
  <field name='CstmApplVerID' required='N'/>
  <field name='SenderCompID' required='Y'/>
  <field name='TargetCompID' required='Y'/>
  <field name='OnBehalfOfCompID' required='N'/>
  <field name='DeliverToCompID' required='N'/>
  <field name='SecureDataLen' required='N'/>
  <field name='SecureData' required='N'/>
  <field name='MsgSeqNum' required='Y'/>
  <field name='SenderSubID' required='N'/>
  <field name='SenderLocationID' required='N'/>
  <field name='TargetSubID' required='N'/>
  <field name='TargetLocationID' required='N'/>
  <field name='OnBehalfOfSubID' required='N'/>
  <field name='OnBehalfOfLocationID' required='N'/>
  <field name='DeliverToSubID' required='N'/>
  <field name='DeliverToLocationID' required='N'/>
  <field name='PossDupFlag' required='N'/>
  <field name='PossResend' required='N'/>
  <field name='SendingTime' required='Y'/>
  <field name='OrigSendingTime' required='N'/>
  <field name='XmlDataLen' required='N'/>
  <field name='XmlData' required='N'/>
  <field name='MessageEncoding' required='N'/>
  <field name='LastMsgSeqNumProcessed' required='N'/>
  <group name='NoHops' required='N'>
   <field name='HopCompID' required='N'/>
   <field name='HopSendingTime' required='N'/>
   <field name='HopRefID' required='N'/>
  </group>
 </header>
 <messages>
  <message name='Heartbeat' msgtype='0' msgcat='admin'>
   <field name='TestReqID' required='N'/>
  </message>
  <message name='TestRequest' msgtype='1' msgcat='admin'>
   <field name='TestReqID' required='Y'/>
  </message>
  <message name='ResendRequest' msgtype='2' msgcat='admin'>
   <field name='BeginSeqNo' required='Y'/>
   <field name='EndSeqNo' required='Y'/>
  </message>
  <message name='Reject' msgtype='3' msgcat='admin'>
   <field name='RefSeqNum' required='Y'/>
   <field name='RefTagID' required='N'/>
   <field name='RefMsgType' required='N'/>
   <field name='RefApplVerID' required='N'/>
   <field name='RefApplExtID' required='N'/>
   <field name='RefCstmApplVerID' required='N'/>
   <field name='SessionRejectReason' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='SequenceReset' msgtype='4' msgcat='admin'>
   <field name='GapFillFlag' required='N'/>
   <field name='NewSeqNo' required='Y'/>
  </message>
  <message name='Logout' msgtype='5' msgcat='admin'>
   <field name='SessionStatus' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='IOI' msgtype='6' msgcat='app'>
   <field name='IOIID' required='Y'/>
   <field name='IOITransType' required='Y'/>
   <field name='IOIRefID' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='Parties' required='N'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='Side' required='Y'/>
   <field name='QtyType' required='N'/>
   <component name='OrderQtyData' required='N'/>
   <field name='IOIQty' required='Y'/>
   <field name='Currency' required='N'/>
   <component name='Stipulations' required='N'/>
   <component name='InstrmtLegIOIGrp' required='N'/>
   <field name='PriceType' required='N'/>
   <field name='Price' required='N'/>
   <field name='ValidUntilTime' required='N'/>
   <field name='IOIQltyInd' required='N'/>
   <field name='IOINaturalFlag' required='N'/>
   <component name='IOIQualGrp' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='TransactTime' required='N'/>
   <field name='URLLink' required='N'/>
   <component name='RoutingGrp' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <component name='YieldData' required='N'/>
  </message>
  <message name='Advertisement' msgtype='7' msgcat='app'>
   <field name='AdvId' required='Y'/>
   <field name='AdvTransType' required='Y'/>
   <field name='AdvRefID' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='AdvSide' required='Y'/>
   <field name='Quantity' required='Y'/>
   <field name='QtyType' required='N'/>
   <field name='Price' required='N'/>
   <field name='Currency' required='N'/>
   <field name='TradeDate' required='N'/>
   <field name='TransactTime' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='URLLink' required='N'/>
   <field name='LastMkt' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
  </message>
  <message name='ExecutionReport' msgtype='8' msgcat='app'>
   <field name='OrderID' required='Y'/>
   <field name='SecondaryOrderID' required='N'/>
   <field name='SecondaryClOrdID' required='N'/>
   <field name='SecondaryExecID' required='N'/>
   <field name='ClOrdID' required='N'/>
   <field name='OrigClOrdID' required='N'/>
   <field name='ClOrdLinkID' required='N'/>
   <field name='QuoteRespID' required='N'/>
   <field name='OrdStatusReqID' required='N'/>
   <field name='MassStatusReqID' required='N'/>
   <field name='HostCrossID' required='N'/>
   <field name='TotNumReports' required='N'/>
   <field name='LastRptRequested' required='N'/>
   <component name='Parties' required='N'/>
   <field name='TradeOriginationDate' required='N'/>
   <component name='ContraGrp' required='N'/>
   <field name='ListID' required='N'/>
   <field name='CrossID' required='N'/>
   <field name='OrigCrossID' required='N'/>
   <field name='CrossType' required='N'/>
   <field name='ExecID' required='Y'/>
   <field name='ExecRefID' required='N'/>
   <field name='ExecType' required='Y'/>
   <field name='OrdStatus' required='Y'/>
   <field name='WorkingIndicator' required='N'/>
   <field name='OrdRejReason' required='N'/>
   <field name='ExecRestatementReason' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='DayBookingInst' required='N'/>
   <field name='BookingUnit' required='N'/>
   <field name='PreallocMethod' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='MatchType' required='N'/>
   <field name='OrderCategory' required='N'/>
   <field name='CashMargin' required='N'/>
   <field name='ClearingFeeIndicator' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='Side' required='Y'/>
   <component name='Stipulations' required='N'/>
   <field name='QtyType' required='N'/>
   <component name='OrderQtyData' required='N'/>
   <field name='LotType' required='N'/>
   <field name='OrdType' required='N'/>
   <field name='PriceType' required='N'/>
   <field name='Price' required='N'/>
   <field name='PriceProtectionScope' required='N'/>
   <field name='StopPx' required='N'/>
   <component name='TriggeringInstruction' required='N'/>
   <component name='PegInstructions' required='N'/>
   <component name='DiscretionInstructions' required='N'/>
   <field name='PeggedPrice' required='N'/>
   <field name='PeggedRefPrice' required='N'/>
   <field name='DiscretionPrice' required='N'/>
   <field name='TargetStrategy' required='N'/>
   <component name='StrategyParametersGrp' required='N'/>
   <field name='TargetStrategyParameters' required='N'/>
   <field name='ParticipationRate' required='N'/>
   <field name='TargetStrategyPerformance' required='N'/>
   <field name='Currency' required='N'/>
   <field name='ComplianceID' required='N'/>
   <field name='SolicitedFlag' required='N'/>
   <field name='TimeInForce' required='N'/>
   <field name='EffectiveTime' required='N'/>
   <field name='ExpireDate' required='N'/>
   <field name='ExpireTime' required='N'/>
   <field name='ExecInst' required='N'/>
   <field name='AggressorIndicator' required='N'/>
   <field name='OrderCapacity' required='N'/>
   <field name='OrderRestrictions' required='N'/>
   <field name='PreTradeAnonymity' required='N'/>
   <field name='CustOrderCapacity' required='N'/>
   <field name='LastQty' required='N'/>
   <field name='CalculatedCcyLastQty' required='N'/>
   <field name='LastSwapPoints' required='N'/>
   <field name='UnderlyingLastQty' required='N'/>
   <field name='LastPx' required='N'/>
   <field name='UnderlyingLastPx' required='N'/>
   <field name='LastParPx' required='N'/>
   <field name='LastSpotRate' required='N'/>
   <field name='LastForwardPoints' required='N'/>
   <field name='LastMkt' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='TimeBracket' required='N'/>
   <field name='LastCapacity' required='N'/>
   <field name='LeavesQty' required='Y'/>
   <field name='CumQty' required='Y'/>
   <field name='AvgPx' required='N'/>
   <field name='DayOrderQty' required='N'/>
   <field name='DayCumQty' required='N'/>
   <field name='DayAvgPx' required='N'/>
   <field name='GTBookingInst' required='N'/>
   <field name='TradeDate' required='N'/>
   <field name='TransactTime' required='N'/>
   <field name='ReportToExch' required='N'/>
   <component name='CommissionData' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <component name='YieldData' required='N'/>
   <field name='GrossTradeAmt' required='N'/>
   <field name='NumDaysInterest' required='N'/>
   <field name='ExDate' required='N'/>
   <field name='AccruedInterestRate' required='N'/>
   <field name='AccruedInterestAmt' required='N'/>
   <field name='InterestAtMaturity' required='N'/>
   <field name='EndAccruedInterestAmt' required='N'/>
   <field name='StartCash' required='N'/>
   <field name='EndCash' required='N'/>
   <field name='TradedFlatSwitch' required='N'/>
   <field name='BasisFeatureDate' required='N'/>
   <field name='BasisFeaturePrice' required='N'/>
   <field name='Concession' required='N'/>
   <field name='TotalTakedown' required='N'/>
   <field name='NetMoney' required='N'/>
   <field name='SettlCurrAmt' required='N'/>
   <field name='SettlCurrency' required='N'/>
   <field name='SettlCurrFxRate' required='N'/>
   <field name='SettlCurrFxRateCalc' required='N'/>
   <field name='HandlInst' required='N'/>
   <field name='MinQty' required='N'/>
   <field name='MatchIncrement' required='N'/>
   <field name='MaxPriceLevels' required='N'/>
   <component name='DisplayInstruction' required='N'/>
   <field name='MaxFloor' required='N'/>
   <field name='PositionEffect' required='N'/>
   <field name='MaxShow' required='N'/>
   <field name='BookingType' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='SettlDate2' required='N'/>
   <field name='OrderQty2' required='N'/>
   <field name='LastForwardPoints2' required='N'/>
   <field name='MultiLegReportingType' required='N'/>
   <field name='CancellationRights' required='N'/>
   <field name='MoneyLaunderingStatus' required='N'/>
   <field name='RegistID' required='N'/>
   <field name='Designation' required='N'/>
   <field name='TransBkdTime' required='N'/>
   <field name='ExecValuationPoint' required='N'/>
   <field name='ExecPriceType' required='N'/>
   <field name='ExecPriceAdjustment' required='N'/>
   <field name='PriorityIndicator' required='N'/>
   <field name='PriceImprovement' required='N'/>
   <field name='LastLiquidityInd' required='N'/>
   <component name='ContAmtGrp' required='N'/>
   <component name='InstrmtLegExecGrp' required='N'/>
   <field name='CopyMsgIndicator' required='N'/>
   <component name='MiscFeesGrp' required='N'/>
   <field name='ManualOrderIndicator' required='N'/>
   <field name='CustDirectedOrder' required='N'/>
   <field name='ReceivedDeptID' required='N'/>
   <field name='CustOrderHandlingInst' required='N'/>
   <field name='OrderHandlingInstSource' required='N'/>
   <component name='TrdRegTimestamps' required='N'/>
  </message>
  <message name='OrderCancelReject' msgtype='9' msgcat='app'>
   <field name='OrderID' required='Y'/>
   <field name='SecondaryOrderID' required='N'/>
   <field name='SecondaryClOrdID' required='N'/>
   <field name='ClOrdID' required='Y'/>
   <field name='ClOrdLinkID' required='N'/>
   <field name='OrigClOrdID' required='Y'/>
   <field name='OrdStatus' required='Y'/>
   <field name='WorkingIndicator' required='N'/>
   <field name='OrigOrdModTime' required='N'/>
   <field name='ListID' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='TradeOriginationDate' required='N'/>
   <field name='TradeDate' required='N'/>
   <field name='TransactTime' required='N'/>
   <field name='CxlRejResponseTo' required='Y'/>
   <field name='CxlRejReason' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='Logon' msgtype='A' msgcat='admin'>
   <field name='EncryptMethod' required='Y'/>
   <field name='HeartBtInt' required='Y'/>
   <field name='RawDataLength' required='N'/>
   <field name='RawData' required='N'/>
   <field name='ResetSeqNumFlag' required='N'/>
   <field name='NextExpectedMsgSeqNum' required='N'/>
   <field name='MaxMessageSize' required='N'/>
   <component name='MsgTypeGrp' required='N'/>
   <field name='TestMessageIndicator' required='N'/>
   <field name='Username' required='N'/>
   <field name='Password' required='N'/>
   <field name='NewPassword' required='N'/>
   <field name='EncryptedPasswordMethod' required='N'/>
   <field name='EncryptedPasswordLen' required='N'/>
   <field name='EncryptedPassword' required='N'/>
   <field name='EncryptedNewPasswordLen' required='N'/>
   <field name='EncryptedNewPassword' required='N'/>
   <field name='SessionStatus' required='N'/>
   <field name='DefaultApplVerID' required='Y'/>
   <field name='DefaultApplExtID' required='N'/>
   <field name='DefaultCstmApplVerID' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='DerivativeSecurityList' msgtype='AA' msgcat='app'>
   <field name='SecurityReqID' required='Y'/>
   <field name='SecurityResponseID' required='Y'/>
   <field name='SecurityRequestResult' required='Y'/>
   <component name='UnderlyingInstrument' required='N'/>
   <field name='TotNoRelatedSym' required='N'/>
   <field name='LastFragment' required='N'/>
   <component name='RelSymDerivSecGrp' required='N'/>
  </message>
  <message name='NewOrderMultileg' msgtype='AB' msgcat='app'>
   <field name='ClOrdID' required='Y'/>
   <field name='SecondaryClOrdID' required='N'/>
   <field name='ClOrdLinkID' required='N'/>
   <component name='Parties' required='N'/>
   <field name='TradeOriginationDate' required='N'/>
   <field name='TradeDate' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='DayBookingInst' required='N'/>
   <field name='BookingUnit' required='N'/>
   <field name='PreallocMethod' required='N'/>
   <field name='AllocID' required='N'/>
   <component name='PreAllocMlegGrp' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='CashMargin' required='N'/>
   <field name='ClearingFeeIndicator' required='N'/>
   <field name='HandlInst' required='N'/>
   <field name='ExecInst' required='N'/>
   <field name='MinQty' required='N'/>
   <field name='MatchIncrement' required='N'/>
   <field name='MaxPriceLevels' required='N'/>
   <component name='DisplayInstruction' required='N'/>
   <field name='MaxFloor' required='N'/>
   <field name='ExDestination' required='N'/>
   <field name='ExDestinationIDSource' required='N'/>
   <component name='TrdgSesGrp' required='N'/>
   <field name='ProcessCode' required='N'/>
   <field name='Side' required='Y'/>
   <component name='Instrument' required='Y'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='PrevClosePx' required='N'/>
   <field name='SwapPoints' required='N'/>
   <component name='LegOrdGrp' required='Y'/>
   <field name='LocateReqd' required='N'/>
   <field name='TransactTime' required='Y'/>
   <field name='QtyType' required='N'/>
   <component name='OrderQtyData' required='N'/>
   <field name='OrdType' required='Y'/>
   <field name='PriceType' required='N'/>
   <field name='Price' required='N'/>
   <field name='PriceProtectionScope' required='N'/>
   <field name='StopPx' required='N'/>
   <component name='TriggeringInstruction' required='N'/>
   <field name='Currency' required='N'/>
   <field name='ComplianceID' required='N'/>
   <field name='SolicitedFlag' required='N'/>
   <field name='IOIID' required='N'/>
   <field name='QuoteID' required='N'/>
   <field name='RefOrderID' required='N'/>
   <field name='RefOrderIDSource' required='N'/>
   <field name='TimeInForce' required='N'/>
   <field name='EffectiveTime' required='N'/>
   <field name='ExpireDate' required='N'/>
   <field name='ExpireTime' required='N'/>
   <field name='GTBookingInst' required='N'/>
   <component name='CommissionData' required='N'/>
   <field name='OrderCapacity' required='N'/>
   <field name='OrderRestrictions' required='N'/>
   <field name='PreTradeAnonymity' required='N'/>
   <field name='CustOrderCapacity' required='N'/>
   <field name='ForexReq' required='N'/>
   <field name='SettlCurrency' required='N'/>
   <field name='BookingType' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='PositionEffect' required='N'/>
   <field name='CoveredOrUncovered' required='N'/>
   <field name='MaxShow' required='N'/>
   <component name='PegInstructions' required='N'/>
   <component name='DiscretionInstructions' required='N'/>
   <field name='TargetStrategy' required='N'/>
   <component name='StrategyParametersGrp' required='N'/>
   <field name='TargetStrategyParameters' required='N'/>
   <field name='ParticipationRate' required='N'/>
   <field name='CancellationRights' required='N'/>
   <field name='MoneyLaunderingStatus' required='N'/>
   <field name='RegistID' required='N'/>
   <field name='Designation' required='N'/>
   <field name='MultiLegRptTypeReq' required='N'/>
  </message>
  <message name='MultilegOrderCancelReplace' msgtype='AC' msgcat='app'>
   <field name='OrderID' required='N'/>
   <field name='OrigClOrdID' required='Y'/>
   <field name='ClOrdID' required='Y'/>
   <field name='SecondaryClOrdID' required='N'/>
   <field name='ClOrdLinkID' required='N'/>
   <field name='OrigOrdModTime' required='N'/>
   <component name='Parties' required='N'/>
   <field name='TradeOriginationDate' required='N'/>
   <field name='TradeDate' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='DayBookingInst' required='N'/>
   <field name='BookingUnit' required='N'/>
   <field name='PreallocMethod' required='N'/>
   <field name='AllocID' required='N'/>
   <component name='PreAllocMlegGrp' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='CashMargin' required='N'/>
   <field name='ClearingFeeIndicator' required='N'/>
   <field name='HandlInst' required='N'/>
   <field name='ExecInst' required='N'/>
   <field name='MinQty' required='N'/>
   <field name='MatchIncrement' required='N'/>
   <field name='MaxPriceLevels' required='N'/>
   <component name='DisplayInstruction' required='N'/>
   <field name='MaxFloor' required='N'/>
   <field name='ExDestination' required='N'/>
   <field name='ExDestinationIDSource' required='N'/>
   <component name='TrdgSesGrp' required='N'/>
   <field name='ProcessCode' required='N'/>
   <field name='Side' required='Y'/>
   <component name='Instrument' required='Y'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='PrevClosePx' required='N'/>
   <field name='SwapPoints' required='N'/>
   <component name='LegOrdGrp' required='Y'/>
   <field name='LocateReqd' required='N'/>
   <field name='TransactTime' required='Y'/>
   <field name='QtyType' required='N'/>
   <component name='OrderQtyData' required='Y'/>
   <field name='OrdType' required='Y'/>
   <field name='PriceType' required='N'/>
   <field name='Price' required='N'/>
   <field name='PriceProtectionScope' required='N'/>
   <field name='StopPx' required='N'/>
   <component name='TriggeringInstruction' required='N'/>
   <field name='Currency' required='N'/>
   <field name='ComplianceID' required='N'/>
   <field name='SolicitedFlag' required='N'/>
   <field name='IOIID' required='N'/>
   <field name='QuoteID' required='N'/>
   <field name='TimeInForce' required='N'/>
   <field name='EffectiveTime' required='N'/>
   <field name='ExpireDate' required='N'/>
   <field name='ExpireTime' required='N'/>
   <field name='GTBookingInst' required='N'/>
   <component name='CommissionData' required='N'/>
   <field name='OrderCapacity' required='N'/>
   <field name='OrderRestrictions' required='N'/>
   <field name='PreTradeAnonymity' required='N'/>
   <field name='CustOrderCapacity' required='N'/>
   <field name='ForexReq' required='N'/>
   <field name='SettlCurrency' required='N'/>
   <field name='BookingType' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='PositionEffect' required='N'/>
   <field name='CoveredOrUncovered' required='N'/>
   <field name='MaxShow' required='N'/>
   <component name='PegInstructions' required='N'/>
   <component name='DiscretionInstructions' required='N'/>
   <field name='TargetStrategy' required='N'/>
   <component name='StrategyParametersGrp' required='N'/>
   <field name='TargetStrategyParameters' required='N'/>
   <field name='ParticipationRate' required='N'/>
   <field name='CancellationRights' required='N'/>
   <field name='MoneyLaunderingStatus' required='N'/>
   <field name='RegistID' required='N'/>
   <field name='Designation' required='N'/>
   <field name='MultiLegRptTypeReq' required='N'/>
  </message>
  <message name='TradeCaptureReportRequest' msgtype='AD' msgcat='app'>
   <field name='TradeRequestID' required='Y'/>
   <field name='TradeID' required='N'/>
   <field name='SecondaryTradeID' required='N'/>
   <field name='FirmTradeID' required='N'/>
   <field name='SecondaryFirmTradeID' required='N'/>
   <field name='TradeRequestType' required='Y'/>
   <field name='SubscriptionRequestType' required='N'/>
   <field name='TradeReportID' required='N'/>
   <field name='SecondaryTradeReportID' required='N'/>
   <field name='ExecID' required='N'/>
   <field name='ExecType' required='N'/>
   <field name='OrderID' required='N'/>
   <field name='ClOrdID' required='N'/>
   <field name='MatchStatus' required='N'/>
   <field name='TrdType' required='N'/>
   <field name='TrdSubType' required='N'/>
   <field name='TradeHandlingInstr' required='N'/>
   <field name='TransferReason' required='N'/>
   <field name='SecondaryTrdType' required='N'/>
   <field name='TradeLinkID' required='N'/>
   <field name='TrdMatchID' required='N'/>
   <component name='Parties' required='N'/>
   <component name='Instrument' required='N'/>
   <component name='InstrumentExtension' required='N'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='TrdCapDtGrp' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='TimeBracket' required='N'/>
   <field name='Side' required='N'/>
   <field name='MultiLegReportingType' required='N'/>
   <field name='TradeInputSource' required='N'/>
   <field name='TradeInputDevice' required='N'/>
   <field name='ResponseTransportType' required='N'/>
   <field name='ResponseDestination' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='MessageEventSource' required='N'/>
  </message>
  <message name='TradeCaptureReport' msgtype='AE' msgcat='app'>
   <field name='TradeReportID' required='N'/>
   <field name='TradeID' required='N'/>
   <field name='SecondaryTradeID' required='N'/>
   <field name='FirmTradeID' required='N'/>
   <field name='SecondaryFirmTradeID' required='N'/>
   <field name='TradeReportTransType' required='N'/>
   <field name='TradeReportType' required='N'/>
   <field name='TrdRptStatus' required='N'/>
   <field name='TradeRequestID' required='N'/>
   <field name='TrdType' required='N'/>
   <field name='TrdSubType' required='N'/>
   <field name='SecondaryTrdType' required='N'/>
   <field name='TradeHandlingInstr' required='N'/>
   <field name='OrigTradeHandlingInstr' required='N'/>
   <field name='OrigTradeDate' required='N'/>
   <field name='OrigTradeID' required='N'/>
   <field name='OrigSecondaryTradeID' required='N'/>
   <field name='TransferReason' required='N'/>
   <field name='ExecType' required='N'/>
   <field name='TotNumTradeReports' required='N'/>
   <field name='LastRptRequested' required='N'/>
   <field name='UnsolicitedIndicator' required='N'/>
   <field name='SubscriptionRequestType' required='N'/>
   <field name='TradeReportRefID' required='N'/>
   <field name='SecondaryTradeReportRefID' required='N'/>
   <field name='SecondaryTradeReportID' required='N'/>
   <field name='TradeLinkID' required='N'/>
   <field name='TrdMatchID' required='N'/>
   <field name='ExecID' required='N'/>
   <field name='OrdStatus' required='N'/>
   <field name='SecondaryExecID' required='N'/>
   <field name='ExecRestatementReason' required='N'/>
   <field name='PreviouslyReported' required='N'/>
   <field name='PriceType' required='N'/>
   <component name='RootParties' required='N'/>
   <field name='AsOfIndicator' required='N'/>
   <field name='SettlSessID' required='N'/>
   <field name='SettlSessSubID' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='FinancingDetails' required='N'/>
   <component name='OrderQtyData' required='N'/>
   <field name='QtyType' required='N'/>
   <component name='YieldData' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='UnderlyingTradingSessionID' required='N'/>
   <field name='UnderlyingTradingSessionSubID' required='N'/>
   <field name='LastQty' required='Y'/>
   <field name='LastPx' required='Y'/>
   <field name='CalculatedCcyLastQty' required='N'/>
   <field name='LastParPx' required='N'/>
   <field name='LastSpotRate' required='N'/>
   <field name='LastForwardPoints' required='N'/>
   <field name='LastSwapPoints' required='N'/>
   <field name='LastMkt' required='N'/>
   <field name='TradeDate' required='Y'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='AvgPx' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <field name='AvgPxIndicator' required='N'/>
   <component name='PositionAmountData' required='N'/>
   <field name='MultiLegReportingType' required='N'/>
   <field name='TradeLegRefID' required='N'/>
   <component name='TrdInstrmtLegGrp' required='N'/>
   <field name='TransactTime' required='N'/>
   <component name='TrdRegTimestamps' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='UnderlyingSettlementDate' required='N'/>
   <field name='MatchStatus' required='N'/>
   <field name='MatchType' required='N'/>
   <field name='OrderCategory' required='N'/>
   <component name='TrdCapRptSideGrp' required='Y'/>
   <field name='CopyMsgIndicator' required='N'/>
   <field name='PublishTrdIndicator' required='N'/>
   <field name='ShortSaleReason' required='N'/>
   <field name='TierCode' required='N'/>
   <field name='MessageEventSource' required='N'/>
   <field name='LastUpdateTime' required='N'/>
   <field name='RndPx' required='N'/>
   <field name='TZTransactTime' required='N'/>
   <field name='ReportedPxDiff' required='N'/>
   <field name='GrossTradeAmt' required='N'/>
  </message>
  <message name='OrderMassStatusRequest' msgtype='AF' msgcat='app'>
   <field name='MassStatusReqID' required='Y'/>
   <field name='MassStatusReqType' required='Y'/>
   <component name='Parties' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <component name='Instrument' required='N'/>
   <component name='UnderlyingInstrument' required='N'/>
   <field name='Side' required='N'/>
  </message>
  <message name='QuoteRequestReject' msgtype='AG' msgcat='app'>
   <field name='QuoteReqID' required='Y'/>
   <field name='RFQReqID' required='N'/>
   <field name='QuoteRequestRejectReason' required='Y'/>
   <component name='QuotReqRjctGrp' required='Y'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='RFQRequest' msgtype='AH' msgcat='app'>
   <field name='RFQReqID' required='Y'/>
   <component name='RFQReqGrp' required='Y'/>
   <field name='SubscriptionRequestType' required='N'/>
  </message>
  <message name='QuoteStatusReport' msgtype='AI' msgcat='app'>
   <field name='QuoteStatusReqID' required='N'/>
   <field name='QuoteReqID' required='N'/>
   <field name='QuoteID' required='Y'/>
   <field name='QuoteRespID' required='N'/>
   <field name='QuoteType' required='N'/>
   <component name='Parties' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='Side' required='N'/>
   <component name='OrderQtyData' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='SettlDate2' required='N'/>
   <field name='OrderQty2' required='N'/>
   <field name='Currency' required='N'/>
   <component name='Stipulations' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <component name='LegQuotStatGrp' required='N'/>
   <component name='QuotQualGrp' required='N'/>
   <field name='ExpireTime' required='N'/>
   <field name='Price' required='N'/>
   <field name='PriceType' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <component name='YieldData' required='N'/>
   <field name='BidPx' required='N'/>
   <field name='OfferPx' required='N'/>
   <field name='MktBidPx' required='N'/>
   <field name='MktOfferPx' required='N'/>
   <field name='MinBidSize' required='N'/>
   <field name='BidSize' required='N'/>
   <field name='MinOfferSize' required='N'/>
   <field name='OfferSize' required='N'/>
   <field name='ValidUntilTime' required='N'/>
   <field name='BidSpotRate' required='N'/>
   <field name='OfferSpotRate' required='N'/>
   <field name='BidForwardPoints' required='N'/>
   <field name='OfferForwardPoints' required='N'/>
   <field name='MidPx' required='N'/>
   <field name='BidYield' required='N'/>
   <field name='MidYield' required='N'/>
   <field name='OfferYield' required='N'/>
   <field name='TransactTime' required='N'/>
   <field name='OrdType' required='N'/>
   <field name='BidForwardPoints2' required='N'/>
   <field name='OfferForwardPoints2' required='N'/>
   <field name='SettlCurrBidFxRate' required='N'/>
   <field name='SettlCurrOfferFxRate' required='N'/>
   <field name='SettlCurrFxRateCalc' required='N'/>
   <field name='CommType' required='N'/>
   <field name='Commission' required='N'/>
   <field name='CustOrderCapacity' required='N'/>
   <field name='ExDestination' required='N'/>
   <field name='ExDestinationIDSource' required='N'/>
   <field name='QuoteStatus' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='QuoteResponse' msgtype='AJ' msgcat='app'>
   <field name='QuoteRespID' required='Y'/>
   <field name='QuoteID' required='N'/>
   <field name='QuoteRespType' required='Y'/>
   <field name='ClOrdID' required='N'/>
   <field name='OrderCapacity' required='N'/>
   <field name='IOIID' required='N'/>
   <field name='QuoteType' required='N'/>
   <component name='QuotQualGrp' required='N'/>
   <component name='Parties' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='Side' required='N'/>
   <component name='OrderQtyData' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='SettlDate2' required='N'/>
   <field name='OrderQty2' required='N'/>
   <field name='Currency' required='N'/>
   <component name='Stipulations' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <component name='LegQuotGrp' required='N'/>
   <field name='BidPx' required='N'/>
   <field name='OfferPx' required='N'/>
   <field name='MktBidPx' required='N'/>
   <field name='MktOfferPx' required='N'/>
   <field name='MinBidSize' required='N'/>
   <field name='BidSize' required='N'/>
   <field name='MinOfferSize' required='N'/>
   <field name='OfferSize' required='N'/>
   <field name='ValidUntilTime' required='N'/>
   <field name='BidSpotRate' required='N'/>
   <field name='OfferSpotRate' required='N'/>
   <field name='BidForwardPoints' required='N'/>
   <field name='OfferForwardPoints' required='N'/>
   <field name='MidPx' required='N'/>
   <field name='BidYield' required='N'/>
   <field name='MidYield' required='N'/>
   <field name='OfferYield' required='N'/>
   <field name='TransactTime' required='N'/>
   <field name='OrdType' required='N'/>
   <field name='BidForwardPoints2' required='N'/>
   <field name='OfferForwardPoints2' required='N'/>
   <field name='SettlCurrBidFxRate' required='N'/>
   <field name='SettlCurrOfferFxRate' required='N'/>
   <field name='SettlCurrFxRateCalc' required='N'/>
   <field name='Commission' required='N'/>
   <field name='CommType' required='N'/>
   <field name='CustOrderCapacity' required='N'/>
   <field name='ExDestination' required='N'/>
   <field name='ExDestinationIDSource' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='Price' required='N'/>
   <field name='PriceType' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <component name='YieldData' required='N'/>
  </message>
  <message name='Confirmation' msgtype='AK' msgcat='app'>
   <field name='ConfirmID' required='Y'/>
   <field name='ConfirmRefID' required='N'/>
   <field name='ConfirmReqID' required='N'/>
   <field name='ConfirmTransType' required='Y'/>
   <field name='ConfirmType' required='Y'/>
   <field name='CopyMsgIndicator' required='N'/>
   <field name='LegalConfirm' required='N'/>
   <field name='ConfirmStatus' required='Y'/>
   <component name='Parties' required='N'/>
   <component name='OrdAllocGrp' required='N'/>
   <field name='AllocID' required='N'/>
   <field name='SecondaryAllocID' required='N'/>
   <field name='IndividualAllocID' required='N'/>
   <field name='TransactTime' required='Y'/>
   <field name='TradeDate' required='Y'/>
   <component name='TrdRegTimestamps' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='InstrumentExtension' required='N'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='Y'/>
   <component name='InstrmtLegGrp' required='Y'/>
   <component name='YieldData' required='N'/>
   <field name='AllocQty' required='Y'/>
   <field name='QtyType' required='N'/>
   <field name='Side' required='Y'/>
   <field name='Currency' required='N'/>
   <field name='LastMkt' required='N'/>
   <component name='CpctyConfGrp' required='Y'/>
   <field name='AllocAccount' required='Y'/>
   <field name='AllocAcctIDSource' required='N'/>
   <field name='AllocAccountType' required='N'/>
   <field name='AvgPx' required='Y'/>
   <field name='AvgPxPrecision' required='N'/>
   <field name='PriceType' required='N'/>
   <field name='AvgParPx' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <field name='ReportedPx' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='ProcessCode' required='N'/>
   <field name='GrossTradeAmt' required='Y'/>
   <field name='NumDaysInterest' required='N'/>
   <field name='ExDate' required='N'/>
   <field name='AccruedInterestRate' required='N'/>
   <field name='AccruedInterestAmt' required='N'/>
   <field name='InterestAtMaturity' required='N'/>
   <field name='EndAccruedInterestAmt' required='N'/>
   <field name='StartCash' required='N'/>
   <field name='EndCash' required='N'/>
   <field name='Concession' required='N'/>
   <field name='TotalTakedown' required='N'/>
   <field name='NetMoney' required='Y'/>
   <field name='MaturityNetMoney' required='N'/>
   <field name='SettlCurrAmt' required='N'/>
   <field name='SettlCurrency' required='N'/>
   <field name='SettlCurrFxRate' required='N'/>
   <field name='SettlCurrFxRateCalc' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <component name='SettlInstructionsData' required='N'/>
   <component name='CommissionData' required='N'/>
   <field name='SharedCommission' required='N'/>
   <component name='Stipulations' required='N'/>
   <component name='MiscFeesGrp' required='N'/>
  </message>
  <message name='PositionMaintenanceRequest' msgtype='AL' msgcat='app'>
   <field name='PosReqID' required='N'/>
   <field name='PosTransType' required='Y'/>
   <field name='PosMaintAction' required='Y'/>
   <field name='OrigPosReqRefID' required='N'/>
   <field name='PosMaintRptRefID' required='N'/>
   <field name='ClearingBusinessDate' required='Y'/>
   <field name='SettlSessID' required='N'/>
   <field name='SettlSessSubID' required='N'/>
   <component name='Parties' required='Y'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <component name='Instrument' required='Y'/>
   <field name='Currency' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='TrdgSesGrp' required='N'/>
   <field name='TransactTime' required='N'/>
   <component name='PositionQty' required='Y'/>
   <component name='PositionAmountData' required='N'/>
   <field name='AdjustmentType' required='N'/>
   <field name='ContraryInstructionIndicator' required='N'/>
   <field name='PriorSpreadIndicator' required='N'/>
   <field name='ThresholdAmount' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='SettlCurrency' required='N'/>
  </message>
  <message name='PositionMaintenanceReport' msgtype='AM' msgcat='app'>
   <field name='PosMaintRptID' required='Y'/>
   <field name='PosTransType' required='Y'/>
   <field name='PosReqID' required='N'/>
   <field name='PosMaintAction' required='Y'/>
   <field name='OrigPosReqRefID' required='N'/>
   <field name='PosMaintStatus' required='Y'/>
   <field name='PosMaintResult' required='N'/>
   <field name='ClearingBusinessDate' required='Y'/>
   <field name='SettlSessID' required='N'/>
   <field name='SettlSessSubID' required='N'/>
   <component name='Parties' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='PosMaintRptRefID' required='N'/>
   <component name='Instrument' required='Y'/>
   <field name='Currency' required='N'/>
   <field name='SettlCurrency' required='N'/>
   <field name='ContraryInstructionIndicator' required='N'/>
   <field name='PriorSpreadIndicator' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='TrdgSesGrp' required='N'/>
   <field name='TransactTime' required='N'/>
   <component name='PositionQty' required='Y'/>
   <component name='PositionAmountData' required='N'/>
   <field name='AdjustmentType' required='N'/>
   <field name='ThresholdAmount' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='RequestForPositions' msgtype='AN' msgcat='app'>
   <field name='PosReqID' required='Y'/>
   <field name='PosReqType' required='Y'/>
   <field name='MatchStatus' required='N'/>
   <field name='SubscriptionRequestType' required='N'/>
   <field name='SettlCurrency' required='N'/>
   <component name='Parties' required='Y'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <component name='Instrument' required='N'/>
   <field name='Currency' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='ClearingBusinessDate' required='Y'/>
   <field name='SettlSessID' required='N'/>
   <field name='SettlSessSubID' required='N'/>
   <component name='TrdgSesGrp' required='N'/>
   <field name='TransactTime' required='Y'/>
   <field name='ResponseTransportType' required='N'/>
   <field name='ResponseDestination' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='RequestForPositionsAck' msgtype='AO' msgcat='app'>
   <field name='PosMaintRptID' required='Y'/>
   <field name='PosReqID' required='N'/>
   <field name='TotalNumPosReports' required='N'/>
   <field name='UnsolicitedIndicator' required='N'/>
   <field name='PosReqResult' required='Y'/>
   <field name='PosReqStatus' required='Y'/>
   <field name='PosReqType' required='N'/>
   <field name='MatchStatus' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='SubscriptionRequestType' required='N'/>
   <field name='SettlSessID' required='N'/>
   <field name='SettlSessSubID' required='N'/>
   <field name='SettlCurrency' required='N'/>
   <component name='Parties' required='Y'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <component name='Instrument' required='N'/>
   <field name='Currency' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='ResponseTransportType' required='N'/>
   <field name='ResponseDestination' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='PositionReport' msgtype='AP' msgcat='app'>
   <field name='PosMaintRptID' required='Y'/>
   <field name='PosReqID' required='N'/>
   <field name='PosReqType' required='N'/>
   <field name='SubscriptionRequestType' required='N'/>
   <field name='TotalNumPosReports' required='N'/>
   <field name='PosReqResult' required='N'/>
   <field name='UnsolicitedIndicator' required='N'/>
   <field name='ClearingBusinessDate' required='Y'/>
   <field name='SettlSessID' required='N'/>
   <field name='SettlSessSubID' required='N'/>
   <field name='PriceType' required='N'/>
   <field name='SettlCurrency' required='N'/>
   <field name='MessageEventSource' required='N'/>
   <component name='Parties' required='Y'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <component name='Instrument' required='N'/>
   <field name='Currency' required='N'/>
   <field name='SettlPrice' required='N'/>
   <field name='SettlPriceType' required='N'/>
   <field name='PriorSettlPrice' required='N'/>
   <field name='MatchStatus' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='PosUndInstrmtGrp' required='N'/>
   <component name='PositionQty' required='N'/>
   <component name='PositionAmountData' required='N'/>
   <field name='RegistStatus' required='N'/>
   <field name='DeliveryDate' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='TradeCaptureReportRequestAck' msgtype='AQ' msgcat='app'>
   <field name='TradeRequestID' required='Y'/>
   <field name='TradeID' required='N'/>
   <field name='SecondaryTradeID' required='N'/>
   <field name='FirmTradeID' required='N'/>
   <field name='SecondaryFirmTradeID' required='N'/>
   <field name='TradeRequestType' required='Y'/>
   <field name='SubscriptionRequestType' required='N'/>
   <field name='TotNumTradeReports' required='N'/>
   <field name='TradeRequestResult' required='Y'/>
   <field name='TradeRequestStatus' required='Y'/>
   <component name='Instrument' required='Y'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='MultiLegReportingType' required='N'/>
   <field name='ResponseTransportType' required='N'/>
   <field name='ResponseDestination' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='MessageEventSource' required='N'/>
  </message>
  <message name='TradeCaptureReportAck' msgtype='AR' msgcat='app'>
   <field name='TradeReportID' required='N'/>
   <field name='TradeID' required='N'/>
   <field name='SecondaryTradeID' required='N'/>
   <field name='FirmTradeID' required='N'/>
   <field name='SecondaryFirmTradeID' required='N'/>
   <field name='TradeReportTransType' required='N'/>
   <field name='TradeReportType' required='N'/>
   <field name='TrdType' required='N'/>
   <field name='TrdSubType' required='N'/>
   <field name='SecondaryTrdType' required='N'/>
   <field name='TradeHandlingInstr' required='N'/>
   <field name='OrigTradeHandlingInstr' required='N'/>
   <field name='OrigTradeDate' required='N'/>
   <field name='OrigTradeID' required='N'/>
   <field name='OrigSecondaryTradeID' required='N'/>
   <field name='TransferReason' required='N'/>
   <component name='RootParties' required='N'/>
   <field name='ExecType' required='N'/>
   <field name='TradeReportRefID' required='N'/>
   <field name='SecondaryTradeReportRefID' required='N'/>
   <field name='TrdRptStatus' required='N'/>
   <field name='TradeReportRejectReason' required='N'/>
   <field name='SecondaryTradeReportID' required='N'/>
   <field name='SubscriptionRequestType' required='N'/>
   <field name='TradeLinkID' required='N'/>
   <field name='TrdMatchID' required='N'/>
   <field name='ExecID' required='N'/>
   <field name='SecondaryExecID' required='N'/>
   <field name='OrdStatus' required='N'/>
   <field name='ExecRestatementReason' required='N'/>
   <field name='PreviouslyReported' required='N'/>
   <field name='PriceType' required='N'/>
   <field name='UnderlyingTradingSessionID' required='N'/>
   <field name='UnderlyingTradingSessionSubID' required='N'/>
   <field name='SettlSessID' required='N'/>
   <field name='SettlSessSubID' required='N'/>
   <field name='QtyType' required='N'/>
   <field name='LastQty' required='N'/>
   <field name='LastPx' required='N'/>
   <component name='Instrument' required='Y'/>
   <field name='LastParPx' required='N'/>
   <field name='CalculatedCcyLastQty' required='N'/>
   <field name='LastSwapPoints' required='N'/>
   <field name='LastSpotRate' required='N'/>
   <field name='LastForwardPoints' required='N'/>
   <field name='LastMkt' required='N'/>
   <field name='TradeDate' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='AvgPx' required='N'/>
   <field name='AvgPxIndicator' required='N'/>
   <field name='MultiLegReportingType' required='N'/>
   <field name='TradeLegRefID' required='N'/>
   <field name='TransactTime' required='N'/>
   <field name='SettlType' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='MatchStatus' required='N'/>
   <field name='MatchType' required='N'/>
   <field name='CopyMsgIndicator' required='N'/>
   <field name='PublishTrdIndicator' required='N'/>
   <field name='ShortSaleReason' required='N'/>
   <component name='TrdInstrmtLegGrp' required='N'/>
   <component name='TrdRegTimestamps' required='N'/>
   <field name='ResponseTransportType' required='N'/>
   <field name='ResponseDestination' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='AsOfIndicator' required='N'/>
   <field name='ClearingFeeIndicator' required='N'/>
   <component name='PositionAmountData' required='N'/>
   <field name='TierCode' required='N'/>
   <field name='MessageEventSource' required='N'/>
   <field name='LastUpdateTime' required='N'/>
   <field name='RndPx' required='N'/>
   <component name='TrdCapRptAckSideGrp' required='N'/>
   <field name='RptSys' required='N'/>
   <field name='GrossTradeAmt' required='N'/>
   <field name='SettlDate' required='N'/>
  </message>
  <message name='AllocationReport' msgtype='AS' msgcat='app'>
   <field name='AllocReportID' required='Y'/>
   <field name='AllocID' required='N'/>
   <field name='AllocTransType' required='Y'/>
   <field name='AllocReportRefID' required='N'/>
   <field name='AllocCancReplaceReason' required='N'/>
   <field name='SecondaryAllocID' required='N'/>
   <field name='AllocReportType' required='Y'/>
   <field name='AllocStatus' required='Y'/>
   <field name='AllocRejCode' required='N'/>
   <field name='RefAllocID' required='N'/>
   <field name='AllocIntermedReqType' required='N'/>
   <field name='AllocLinkID' required='N'/>
   <field name='AllocLinkType' required='N'/>
   <field name='BookingRefID' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='TrdType' required='N'/>
   <field name='TrdSubType' required='N'/>
   <field name='MultiLegReportingType' required='N'/>
   <field name='CustOrderCapacity' required='N'/>
   <field name='TradeInputSource' required='N'/>
   <field name='RndPx' required='N'/>
   <field name='MessageEventSource' required='N'/>
   <field name='TradeInputDevice' required='N'/>
   <field name='AvgPxIndicator' required='N'/>
   <field name='AllocNoOrdersType' required='N'/>
   <component name='OrdAllocGrp' required='N'/>
   <component name='ExecAllocGrp' required='N'/>
   <field name='PreviouslyReported' required='N'/>
   <field name='ReversalIndicator' required='N'/>
   <field name='MatchType' required='N'/>
   <field name='Side' required='Y'/>
   <component name='Instrument' required='Y'/>
   <component name='InstrumentExtension' required='N'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='Quantity' required='Y'/>
   <field name='QtyType' required='N'/>
   <field name='LastMkt' required='N'/>
   <field name='TradeOriginationDate' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='PriceType' required='N'/>
   <field name='AvgPx' required='Y'/>
   <field name='AvgParPx' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <field name='Currency' required='N'/>
   <field name='AvgPxPrecision' required='N'/>
   <component name='Parties' required='N'/>
   <field name='TradeDate' required='Y'/>
   <field name='TransactTime' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='BookingType' required='N'/>
   <field name='GrossTradeAmt' required='N'/>
   <field name='Concession' required='N'/>
   <field name='TotalTakedown' required='N'/>
   <field name='NetMoney' required='N'/>
   <field name='PositionEffect' required='N'/>
   <field name='AutoAcceptIndicator' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='NumDaysInterest' required='N'/>
   <field name='AccruedInterestRate' required='N'/>
   <field name='AccruedInterestAmt' required='N'/>
   <field name='TotalAccruedInterestAmt' required='N'/>
   <field name='InterestAtMaturity' required='N'/>
   <field name='EndAccruedInterestAmt' required='N'/>
   <field name='StartCash' required='N'/>
   <field name='EndCash' required='N'/>
   <field name='LegalConfirm' required='N'/>
   <component name='Stipulations' required='N'/>
   <component name='YieldData' required='N'/>
   <component name='PositionAmountData' required='N'/>
   <field name='TotNoAllocs' required='N'/>
   <field name='LastFragment' required='N'/>
   <component name='AllocGrp' required='N'/>
  </message>
  <message name='AllocationReportAck' msgtype='AT' msgcat='app'>
   <field name='AllocReportID' required='Y'/>
   <field name='AllocID' required='Y'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='AvgPxIndicator' required='N'/>
   <field name='Quantity' required='N'/>
   <field name='AllocTransType' required='N'/>
   <component name='Parties' required='N'/>
   <field name='SecondaryAllocID' required='N'/>
   <field name='TradeDate' required='N'/>
   <field name='TransactTime' required='N'/>
   <field name='AllocStatus' required='N'/>
   <field name='AllocRejCode' required='N'/>
   <field name='AllocReportType' required='N'/>
   <field name='AllocIntermedReqType' required='N'/>
   <field name='MatchStatus' required='N'/>
   <field name='Product' required='N'/>
   <field name='SecurityType' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <component name='AllocAckGrp' required='N'/>
  </message>
  <message name='ConfirmationAck' msgtype='AU' msgcat='app'>
   <field name='ConfirmID' required='Y'/>
   <field name='TradeDate' required='Y'/>
   <field name='TransactTime' required='Y'/>
   <field name='AffirmStatus' required='Y'/>
   <field name='ConfirmRejReason' required='N'/>
   <field name='MatchStatus' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='SettlementInstructionRequest' msgtype='AV' msgcat='app'>
   <field name='SettlInstReqID' required='Y'/>
   <field name='TransactTime' required='Y'/>
   <component name='Parties' required='N'/>
   <field name='AllocAccount' required='N'/>
   <field name='AllocAcctIDSource' required='N'/>
   <field name='Side' required='N'/>
   <field name='Product' required='N'/>
   <field name='SecurityType' required='N'/>
   <field name='CFICode' required='N'/>
   <field name='SettlCurrency' required='N'/>
   <field name='EffectiveTime' required='N'/>
   <field name='ExpireTime' required='N'/>
   <field name='LastUpdateTime' required='N'/>
   <field name='StandInstDbType' required='N'/>
   <field name='StandInstDbName' required='N'/>
   <field name='StandInstDbID' required='N'/>
  </message>
  <message name='AssignmentReport' msgtype='AW' msgcat='app'>
   <field name='AsgnRptID' required='Y'/>
   <field name='TotNumAssignmentReports' required='N'/>
   <field name='LastRptRequested' required='N'/>
   <component name='Parties' required='Y'/>
   <field name='Account' required='N'/>
   <field name='AccountType' required='N'/>
   <component name='Instrument' required='N'/>
   <field name='Currency' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='PositionQty' required='N'/>
   <component name='PositionAmountData' required='N'/>
   <field name='ThresholdAmount' required='N'/>
   <field name='SettlPrice' required='N'/>
   <field name='SettlPriceType' required='N'/>
   <field name='UnderlyingSettlPrice' required='N'/>
   <field name='PriorSettlPrice' required='N'/>
   <field name='ExpireDate' required='N'/>
   <field name='AssignmentMethod' required='N'/>
   <field name='AssignmentUnit' required='N'/>
   <field name='OpenInterest' required='N'/>
   <field name='ExerciseMethod' required='N'/>
   <field name='SettlSessID' required='N'/>
   <field name='SettlSessSubID' required='N'/>
   <field name='ClearingBusinessDate' required='Y'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='CollateralRequest' msgtype='AX' msgcat='app'>
   <field name='CollReqID' required='Y'/>
   <field name='CollAsgnReason' required='Y'/>
   <field name='TransactTime' required='Y'/>
   <field name='ExpireTime' required='N'/>
   <component name='Parties' required='N'/>
   <field name='Account' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='ClOrdID' required='N'/>
   <field name='OrderID' required='N'/>
   <field name='SecondaryOrderID' required='N'/>
   <field name='SecondaryClOrdID' required='N'/>
   <component name='ExecCollGrp' required='N'/>
   <component name='TrdCollGrp' required='N'/>
   <component name='Instrument' required='N'/>
   <component name='FinancingDetails' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='Quantity' required='N'/>
   <field name='QtyType' required='N'/>
   <field name='Currency' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='UndInstrmtCollGrp' required='N'/>
   <field name='MarginExcess' required='N'/>
   <field name='TotalNetValue' required='N'/>
   <field name='CashOutstanding' required='N'/>
   <component name='TrdRegTimestamps' required='N'/>
   <field name='Side' required='N'/>
   <component name='MiscFeesGrp' required='N'/>
   <field name='Price' required='N'/>
   <field name='PriceType' required='N'/>
   <field name='AccruedInterestAmt' required='N'/>
   <field name='EndAccruedInterestAmt' required='N'/>
   <field name='StartCash' required='N'/>
   <field name='EndCash' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <component name='Stipulations' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='SettlSessID' required='N'/>
   <field name='SettlSessSubID' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='CollateralAssignment' msgtype='AY' msgcat='app'>
   <field name='CollAsgnID' required='Y'/>
   <field name='CollReqID' required='N'/>
   <field name='CollAsgnReason' required='Y'/>
   <field name='CollAsgnTransType' required='Y'/>
   <field name='CollAsgnRefID' required='N'/>
   <field name='TransactTime' required='Y'/>
   <field name='ExpireTime' required='N'/>
   <component name='Parties' required='N'/>
   <field name='Account' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='ClOrdID' required='N'/>
   <field name='OrderID' required='N'/>
   <field name='SecondaryOrderID' required='N'/>
   <field name='SecondaryClOrdID' required='N'/>
   <component name='ExecCollGrp' required='N'/>
   <component name='TrdCollGrp' required='N'/>
   <component name='Instrument' required='N'/>
   <component name='FinancingDetails' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='Quantity' required='N'/>
   <field name='QtyType' required='N'/>
   <field name='Currency' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='UndInstrmtCollGrp' required='N'/>
   <field name='MarginExcess' required='N'/>
   <field name='TotalNetValue' required='N'/>
   <field name='CashOutstanding' required='N'/>
   <component name='TrdRegTimestamps' required='N'/>
   <field name='Side' required='N'/>
   <component name='MiscFeesGrp' required='N'/>
   <field name='Price' required='N'/>
   <field name='PriceType' required='N'/>
   <field name='AccruedInterestAmt' required='N'/>
   <field name='EndAccruedInterestAmt' required='N'/>
   <field name='StartCash' required='N'/>
   <field name='EndCash' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <component name='Stipulations' required='N'/>
   <component name='SettlInstructionsData' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='SettlSessID' required='N'/>
   <field name='SettlSessSubID' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='CollateralResponse' msgtype='AZ' msgcat='app'>
   <field name='CollRespID' required='Y'/>
   <field name='CollAsgnID' required='N'/>
   <field name='CollReqID' required='N'/>
   <field name='CollAsgnReason' required='N'/>
   <field name='CollAsgnTransType' required='N'/>
   <field name='CollAsgnRespType' required='Y'/>
   <field name='CollAsgnRejectReason' required='N'/>
   <field name='TransactTime' required='Y'/>
   <field name='CollApplType' required='N'/>
   <field name='FinancialStatus' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <component name='Parties' required='N'/>
   <field name='Account' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='ClOrdID' required='N'/>
   <field name='OrderID' required='N'/>
   <field name='SecondaryOrderID' required='N'/>
   <field name='SecondaryClOrdID' required='N'/>
   <component name='ExecCollGrp' required='N'/>
   <component name='TrdCollGrp' required='N'/>
   <component name='Instrument' required='N'/>
   <component name='FinancingDetails' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='Quantity' required='N'/>
   <field name='QtyType' required='N'/>
   <field name='Currency' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='UndInstrmtCollGrp' required='N'/>
   <field name='MarginExcess' required='N'/>
   <field name='TotalNetValue' required='N'/>
   <field name='CashOutstanding' required='N'/>
   <component name='TrdRegTimestamps' required='N'/>
   <field name='Side' required='N'/>
   <component name='MiscFeesGrp' required='N'/>
   <field name='Price' required='N'/>
   <field name='PriceType' required='N'/>
   <field name='AccruedInterestAmt' required='N'/>
   <field name='EndAccruedInterestAmt' required='N'/>
   <field name='StartCash' required='N'/>
   <field name='EndCash' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <component name='Stipulations' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='News' msgtype='B' msgcat='app'>
   <field name='OrigTime' required='N'/>
   <field name='Urgency' required='N'/>
   <field name='Headline' required='Y'/>
   <field name='EncodedHeadlineLen' required='N'/>
   <field name='EncodedHeadline' required='N'/>
   <component name='RoutingGrp' required='N'/>
   <component name='InstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='LinesOfTextGrp' required='Y'/>
   <field name='URLLink' required='N'/>
   <field name='RawDataLength' required='N'/>
   <field name='RawData' required='N'/>
  </message>
  <message name='CollateralReport' msgtype='BA' msgcat='app'>
   <field name='CollRptID' required='Y'/>
   <field name='CollInquiryID' required='N'/>
   <field name='TransactTime' required='N'/>
   <field name='CollApplType' required='N'/>
   <field name='FinancialStatus' required='N'/>
   <field name='CollStatus' required='Y'/>
   <field name='TotNumReports' required='N'/>
   <field name='LastRptRequested' required='N'/>
   <component name='Parties' required='N'/>
   <field name='Account' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='ClOrdID' required='N'/>
   <field name='OrderID' required='N'/>
   <field name='SecondaryOrderID' required='N'/>
   <field name='SecondaryClOrdID' required='N'/>
   <component name='ExecCollGrp' required='N'/>
   <component name='TrdCollGrp' required='N'/>
   <component name='Instrument' required='N'/>
   <component name='FinancingDetails' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='Quantity' required='N'/>
   <field name='QtyType' required='N'/>
   <field name='Currency' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='MarginExcess' required='N'/>
   <field name='TotalNetValue' required='N'/>
   <field name='CashOutstanding' required='N'/>
   <component name='TrdRegTimestamps' required='N'/>
   <field name='Side' required='N'/>
   <component name='MiscFeesGrp' required='N'/>
   <field name='Price' required='N'/>
   <field name='PriceType' required='N'/>
   <field name='AccruedInterestAmt' required='N'/>
   <field name='EndAccruedInterestAmt' required='N'/>
   <field name='StartCash' required='N'/>
   <field name='EndCash' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <component name='Stipulations' required='N'/>
   <component name='SettlInstructionsData' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='SettlSessID' required='N'/>
   <field name='SettlSessSubID' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='CollateralInquiry' msgtype='BB' msgcat='app'>
   <field name='CollInquiryID' required='N'/>
   <component name='CollInqQualGrp' required='N'/>
   <field name='SubscriptionRequestType' required='N'/>
   <field name='ResponseTransportType' required='N'/>
   <field name='ResponseDestination' required='N'/>
   <component name='Parties' required='N'/>
   <field name='Account' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='ClOrdID' required='N'/>
   <field name='OrderID' required='N'/>
   <field name='SecondaryOrderID' required='N'/>
   <field name='SecondaryClOrdID' required='N'/>
   <component name='ExecCollGrp' required='N'/>
   <component name='TrdCollGrp' required='N'/>
   <component name='Instrument' required='N'/>
   <component name='FinancingDetails' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='Quantity' required='N'/>
   <field name='QtyType' required='N'/>
   <field name='Currency' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='MarginExcess' required='N'/>
   <field name='TotalNetValue' required='N'/>
   <field name='CashOutstanding' required='N'/>
   <component name='TrdRegTimestamps' required='N'/>
   <field name='Side' required='N'/>
   <field name='Price' required='N'/>
   <field name='PriceType' required='N'/>
   <field name='AccruedInterestAmt' required='N'/>
   <field name='EndAccruedInterestAmt' required='N'/>
   <field name='StartCash' required='N'/>
   <field name='EndCash' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <component name='Stipulations' required='N'/>
   <component name='SettlInstructionsData' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='SettlSessID' required='N'/>
   <field name='SettlSessSubID' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='NetworkCounterpartySystemStatusRequest' msgtype='BC' msgcat='app'>
   <field name='NetworkRequestType' required='Y'/>
   <field name='NetworkRequestID' required='Y'/>
   <component name='CompIDReqGrp' required='N'/>
  </message>
  <message name='NetworkCounterpartySystemStatusResponse' msgtype='BD' msgcat='app'>
   <field name='NetworkStatusResponseType' required='Y'/>
   <field name='NetworkRequestID' required='N'/>
   <field name='NetworkResponseID' required='Y'/>
   <field name='LastNetworkResponseID' required='N'/>
   <component name='CompIDStatGrp' required='Y'/>
  </message>
  <message name='UserRequest' msgtype='BE' msgcat='app'>
   <field name='UserRequestID' required='Y'/>
   <field name='UserRequestType' required='Y'/>
   <field name='Username' required='Y'/>
   <field name='Password' required='N'/>
   <field name='NewPassword' required='N'/>
   <field name='RawDataLength' required='N'/>
   <field name='RawData' required='N'/>
  </message>
  <message name='UserResponse' msgtype='BF' msgcat='app'>
   <field name='UserRequestID' required='Y'/>
   <field name='Username' required='Y'/>
   <field name='UserStatus' required='N'/>
   <field name='UserStatusText' required='N'/>
  </message>
  <message name='CollateralInquiryAck' msgtype='BG' msgcat='app'>
   <field name='CollInquiryID' required='Y'/>
   <field name='CollInquiryStatus' required='Y'/>
   <field name='CollInquiryResult' required='N'/>
   <component name='CollInqQualGrp' required='N'/>
   <field name='TotNumReports' required='N'/>
   <component name='Parties' required='N'/>
   <field name='Account' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='ClOrdID' required='N'/>
   <field name='OrderID' required='N'/>
   <field name='SecondaryOrderID' required='N'/>
   <field name='SecondaryClOrdID' required='N'/>
   <component name='ExecCollGrp' required='N'/>
   <component name='TrdCollGrp' required='N'/>
   <component name='Instrument' required='N'/>
   <component name='FinancingDetails' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='Quantity' required='N'/>
   <field name='QtyType' required='N'/>
   <field name='Currency' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='SettlSessID' required='N'/>
   <field name='SettlSessSubID' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='ResponseTransportType' required='N'/>
   <field name='ResponseDestination' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='ConfirmationRequest' msgtype='BH' msgcat='app'>
   <field name='ConfirmReqID' required='Y'/>
   <field name='ConfirmType' required='Y'/>
   <component name='OrdAllocGrp' required='N'/>
   <field name='AllocID' required='N'/>
   <field name='SecondaryAllocID' required='N'/>
   <field name='IndividualAllocID' required='N'/>
   <field name='TransactTime' required='Y'/>
   <field name='AllocAccount' required='N'/>
   <field name='AllocAcctIDSource' required='N'/>
   <field name='AllocAccountType' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='TradingSessionListRequest' msgtype='BI' msgcat='app'>
   <field name='TradSesReqID' required='Y'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='SecurityExchange' required='N'/>
   <field name='TradSesMethod' required='N'/>
   <field name='TradSesMode' required='N'/>
   <field name='SubscriptionRequestType' required='Y'/>
  </message>
  <message name='TradingSessionList' msgtype='BJ' msgcat='app'>
   <field name='TradSesReqID' required='N'/>
   <component name='TrdSessLstGrp' required='Y'/>
  </message>
  <message name='SecurityListUpdateReport' msgtype='BK' msgcat='app'>
   <field name='SecurityReportID' required='N'/>
   <field name='SecurityReqID' required='N'/>
   <field name='SecurityResponseID' required='N'/>
   <field name='SecurityRequestResult' required='N'/>
   <field name='TotNoRelatedSym' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='SecurityUpdateAction' required='N'/>
   <field name='CorporateAction' required='N'/>
   <field name='LastFragment' required='N'/>
   <component name='SecLstUpdRelSymGrp' required='N'/>
  </message>
  <message name='AdjustedPositionReport' msgtype='BL' msgcat='app'>
   <field name='PosMaintRptID' required='Y'/>
   <field name='PosReqType' required='N'/>
   <field name='ClearingBusinessDate' required='Y'/>
   <field name='SettlSessID' required='N'/>
   <field name='PosMaintRptRefID' required='N'/>
   <component name='Parties' required='Y'/>
   <component name='PositionQty' required='Y'/>
   <component name='Instrument' required='N'/>
   <field name='SettlPrice' required='N'/>
   <field name='PriorSettlPrice' required='N'/>
  </message>
  <message name='AllocationInstructionAlert' msgtype='BM' msgcat='app'>
   <field name='AllocID' required='Y'/>
   <field name='AllocTransType' required='Y'/>
   <field name='AllocType' required='Y'/>
   <field name='SecondaryAllocID' required='N'/>
   <field name='RefAllocID' required='N'/>
   <field name='AllocCancReplaceReason' required='N'/>
   <field name='AllocIntermedReqType' required='N'/>
   <field name='AllocLinkID' required='N'/>
   <field name='AllocLinkType' required='N'/>
   <field name='BookingRefID' required='N'/>
   <field name='AllocNoOrdersType' required='N'/>
   <component name='OrdAllocGrp' required='N'/>
   <component name='ExecAllocGrp' required='N'/>
   <field name='PreviouslyReported' required='N'/>
   <field name='ReversalIndicator' required='N'/>
   <field name='MatchType' required='N'/>
   <field name='Side' required='Y'/>
   <component name='Instrument' required='Y'/>
   <component name='InstrumentExtension' required='N'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='Quantity' required='Y'/>
   <field name='QtyType' required='N'/>
   <field name='LastMkt' required='N'/>
   <field name='TradeOriginationDate' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='PriceType' required='N'/>
   <field name='AvgPx' required='N'/>
   <field name='AvgParPx' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <field name='Currency' required='N'/>
   <field name='AvgPxPrecision' required='N'/>
   <component name='Parties' required='N'/>
   <field name='TradeDate' required='Y'/>
   <field name='TransactTime' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='BookingType' required='N'/>
   <field name='GrossTradeAmt' required='N'/>
   <field name='Concession' required='N'/>
   <field name='TotalTakedown' required='N'/>
   <field name='NetMoney' required='N'/>
   <field name='PositionEffect' required='N'/>
   <field name='AutoAcceptIndicator' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='NumDaysInterest' required='N'/>
   <field name='AccruedInterestRate' required='N'/>
   <field name='AccruedInterestAmt' required='N'/>
   <field name='TotalAccruedInterestAmt' required='N'/>
   <field name='InterestAtMaturity' required='N'/>
   <field name='EndAccruedInterestAmt' required='N'/>
   <field name='StartCash' required='N'/>
   <field name='EndCash' required='N'/>
   <field name='LegalConfirm' required='N'/>
   <component name='Stipulations' required='N'/>
   <component name='YieldData' required='N'/>
   <component name='PositionAmountData' required='N'/>
   <field name='TotNoAllocs' required='N'/>
   <field name='LastFragment' required='N'/>
   <component name='AllocGrp' required='N'/>
   <field name='AvgPxIndicator' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='TrdType' required='N'/>
   <field name='TrdSubType' required='N'/>
   <field name='CustOrderCapacity' required='N'/>
   <field name='TradeInputSource' required='N'/>
   <field name='MultiLegReportingType' required='N'/>
   <field name='MessageEventSource' required='N'/>
   <field name='RndPx' required='N'/>
  </message>
  <message name='ExecutionAcknowledgement' msgtype='BN' msgcat='app'>
   <field name='OrderID' required='Y'/>
   <field name='SecondaryOrderID' required='N'/>
   <field name='ClOrdID' required='N'/>
   <field name='ExecAckStatus' required='Y'/>
   <field name='ExecID' required='Y'/>
   <field name='DKReason' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='Side' required='Y'/>
   <component name='OrderQtyData' required='Y'/>
   <field name='LastQty' required='N'/>
   <field name='LastPx' required='N'/>
   <field name='PriceType' required='N'/>
   <field name='LastParPx' required='N'/>
   <field name='CumQty' required='N'/>
   <field name='AvgPx' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='ContraryIntentionReport' msgtype='BO' msgcat='app'>
   <field name='ContIntRptID' required='Y'/>
   <field name='TransactTime' required='N'/>
   <field name='LateIndicator' required='N'/>
   <field name='InputSource' required='N'/>
   <field name='ClearingBusinessDate' required='Y'/>
   <component name='Parties' required='Y'/>
   <component name='ExpirationQty' required='Y'/>
   <component name='Instrument' required='Y'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='SecurityDefinitionUpdateReport' msgtype='BP' msgcat='app'>
   <field name='SecurityReportID' required='N'/>
   <field name='SecurityReqID' required='N'/>
   <field name='SecurityResponseID' required='N'/>
   <field name='SecurityResponseType' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='SecurityUpdateAction' required='N'/>
   <field name='CorporateAction' required='N'/>
   <component name='Instrument' required='N'/>
   <component name='UnderlyingInstrument' required='N'/>
   <field name='Currency' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='ExpirationCycle' required='N'/>
   <field name='RoundLot' required='N'/>
   <field name='MinTradeVol' required='N'/>
  </message>
  <message name='Email' msgtype='C' msgcat='app'>
   <field name='EmailThreadID' required='Y'/>
   <field name='EmailType' required='Y'/>
   <field name='OrigTime' required='N'/>
   <field name='Subject' required='Y'/>
   <field name='EncodedSubjectLen' required='N'/>
   <field name='EncodedSubject' required='N'/>
   <component name='RoutingGrp' required='N'/>
   <component name='InstrmtGrp' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='OrderID' required='N'/>
   <field name='ClOrdID' required='N'/>
   <component name='LinesOfTextGrp' required='Y'/>
   <field name='RawDataLength' required='N'/>
   <field name='RawData' required='N'/>
  </message>
  <message name='NewOrderSingle' msgtype='D' msgcat='app'>
   <field name='ClOrdID' required='Y'/>
   <field name='SecondaryClOrdID' required='N'/>
   <field name='ClOrdLinkID' required='N'/>
   <component name='Parties' required='N'/>
   <field name='TradeOriginationDate' required='N'/>
   <field name='TradeDate' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='DayBookingInst' required='N'/>
   <field name='BookingUnit' required='N'/>
   <field name='PreallocMethod' required='N'/>
   <field name='AllocID' required='N'/>
   <component name='PreAllocGrp' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='CashMargin' required='N'/>
   <field name='ClearingFeeIndicator' required='N'/>
   <field name='HandlInst' required='N'/>
   <field name='ExecInst' required='N'/>
   <field name='MinQty' required='N'/>
   <field name='MatchIncrement' required='N'/>
   <field name='MaxPriceLevels' required='N'/>
   <component name='DisplayInstruction' required='N'/>
   <field name='MaxFloor' required='N'/>
   <field name='ExDestination' required='N'/>
   <field name='ExDestinationIDSource' required='N'/>
   <component name='TrdgSesGrp' required='N'/>
   <field name='ProcessCode' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='PrevClosePx' required='N'/>
   <field name='Side' required='Y'/>
   <field name='LocateReqd' required='N'/>
   <field name='TransactTime' required='Y'/>
   <component name='Stipulations' required='N'/>
   <field name='QtyType' required='N'/>
   <component name='OrderQtyData' required='Y'/>
   <field name='OrdType' required='Y'/>
   <field name='PriceType' required='N'/>
   <field name='Price' required='N'/>
   <field name='PriceProtectionScope' required='N'/>
   <field name='StopPx' required='N'/>
   <component name='TriggeringInstruction' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <component name='YieldData' required='N'/>
   <field name='Currency' required='N'/>
   <field name='ComplianceID' required='N'/>
   <field name='SolicitedFlag' required='N'/>
   <field name='IOIID' required='N'/>
   <field name='QuoteID' required='N'/>
   <field name='TimeInForce' required='N'/>
   <field name='EffectiveTime' required='N'/>
   <field name='ExpireDate' required='N'/>
   <field name='ExpireTime' required='N'/>
   <field name='GTBookingInst' required='N'/>
   <component name='CommissionData' required='N'/>
   <field name='OrderCapacity' required='N'/>
   <field name='OrderRestrictions' required='N'/>
   <field name='PreTradeAnonymity' required='N'/>
   <field name='CustOrderCapacity' required='N'/>
   <field name='ForexReq' required='N'/>
   <field name='SettlCurrency' required='N'/>
   <field name='BookingType' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='SettlDate2' required='N'/>
   <field name='OrderQty2' required='N'/>
   <field name='Price2' required='N'/>
   <field name='PositionEffect' required='N'/>
   <field name='CoveredOrUncovered' required='N'/>
   <field name='MaxShow' required='N'/>
   <component name='PegInstructions' required='N'/>
   <component name='DiscretionInstructions' required='N'/>
   <field name='TargetStrategy' required='N'/>
   <component name='StrategyParametersGrp' required='N'/>
   <field name='TargetStrategyParameters' required='N'/>
   <field name='ParticipationRate' required='N'/>
   <field name='CancellationRights' required='N'/>
   <field name='MoneyLaunderingStatus' required='N'/>
   <field name='RegistID' required='N'/>
   <field name='Designation' required='N'/>
   <field name='ManualOrderIndicator' required='N'/>
   <field name='CustDirectedOrder' required='N'/>
   <field name='ReceivedDeptID' required='N'/>
   <field name='CustOrderHandlingInst' required='N'/>
   <field name='OrderHandlingInstSource' required='N'/>
   <component name='TrdRegTimestamps' required='N'/>
   <field name='RefOrderID' required='N'/>
   <field name='RefOrderIDSource' required='N'/>
  </message>
  <message name='NewOrderList' msgtype='E' msgcat='app'>
   <field name='ListID' required='Y'/>
   <field name='BidID' required='N'/>
   <field name='ClientBidID' required='N'/>
   <field name='ProgRptReqs' required='N'/>
   <field name='BidType' required='Y'/>
   <field name='ProgPeriodInterval' required='N'/>
   <field name='CancellationRights' required='N'/>
   <field name='MoneyLaunderingStatus' required='N'/>
   <field name='RegistID' required='N'/>
   <field name='ListExecInstType' required='N'/>
   <field name='ListExecInst' required='N'/>
   <field name='EncodedListExecInstLen' required='N'/>
   <field name='EncodedListExecInst' required='N'/>
   <field name='AllowableOneSidednessPct' required='N'/>
   <field name='AllowableOneSidednessValue' required='N'/>
   <field name='AllowableOneSidednessCurr' required='N'/>
   <field name='TotNoOrders' required='Y'/>
   <field name='LastFragment' required='N'/>
   <component name='RootParties' required='N'/>
   <component name='ListOrdGrp' required='Y'/>
  </message>
  <message name='OrderCancelRequest' msgtype='F' msgcat='app'>
   <field name='OrigClOrdID' required='Y'/>
   <field name='OrderID' required='N'/>
   <field name='ClOrdID' required='Y'/>
   <field name='SecondaryClOrdID' required='N'/>
   <field name='ClOrdLinkID' required='N'/>
   <field name='ListID' required='N'/>
   <field name='OrigOrdModTime' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <component name='Parties' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='Side' required='Y'/>
   <field name='TransactTime' required='Y'/>
   <component name='OrderQtyData' required='Y'/>
   <field name='ComplianceID' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='OrderCancelReplaceRequest' msgtype='G' msgcat='app'>
   <field name='OrderID' required='N'/>
   <component name='Parties' required='N'/>
   <field name='TradeOriginationDate' required='N'/>
   <field name='TradeDate' required='N'/>
   <field name='OrigClOrdID' required='Y'/>
   <field name='ClOrdID' required='Y'/>
   <field name='SecondaryClOrdID' required='N'/>
   <field name='ClOrdLinkID' required='N'/>
   <field name='ListID' required='N'/>
   <field name='OrigOrdModTime' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='DayBookingInst' required='N'/>
   <field name='BookingUnit' required='N'/>
   <field name='PreallocMethod' required='N'/>
   <field name='AllocID' required='N'/>
   <component name='PreAllocGrp' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='CashMargin' required='N'/>
   <field name='ClearingFeeIndicator' required='N'/>
   <field name='HandlInst' required='N'/>
   <field name='ExecInst' required='N'/>
   <field name='MinQty' required='N'/>
   <field name='MatchIncrement' required='N'/>
   <field name='MaxPriceLevels' required='N'/>
   <component name='DisplayInstruction' required='N'/>
   <field name='MaxFloor' required='N'/>
   <field name='ExDestination' required='N'/>
   <field name='ExDestinationIDSource' required='N'/>
   <component name='TrdgSesGrp' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='Side' required='Y'/>
   <field name='TransactTime' required='Y'/>
   <field name='QtyType' required='N'/>
   <component name='OrderQtyData' required='Y'/>
   <field name='OrdType' required='Y'/>
   <field name='PriceType' required='N'/>
   <field name='Price' required='N'/>
   <field name='PriceProtectionScope' required='N'/>
   <field name='StopPx' required='N'/>
   <component name='TriggeringInstruction' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <component name='YieldData' required='N'/>
   <component name='PegInstructions' required='N'/>
   <component name='DiscretionInstructions' required='N'/>
   <field name='TargetStrategy' required='N'/>
   <component name='StrategyParametersGrp' required='N'/>
   <field name='TargetStrategyParameters' required='N'/>
   <field name='ParticipationRate' required='N'/>
   <field name='ComplianceID' required='N'/>
   <field name='SolicitedFlag' required='N'/>
   <field name='Currency' required='N'/>
   <field name='TimeInForce' required='N'/>
   <field name='EffectiveTime' required='N'/>
   <field name='ExpireDate' required='N'/>
   <field name='ExpireTime' required='N'/>
   <field name='GTBookingInst' required='N'/>
   <component name='CommissionData' required='N'/>
   <field name='OrderCapacity' required='N'/>
   <field name='OrderRestrictions' required='N'/>
   <field name='PreTradeAnonymity' required='N'/>
   <field name='CustOrderCapacity' required='N'/>
   <field name='ForexReq' required='N'/>
   <field name='SettlCurrency' required='N'/>
   <field name='BookingType' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='SettlDate2' required='N'/>
   <field name='OrderQty2' required='N'/>
   <field name='Price2' required='N'/>
   <field name='PositionEffect' required='N'/>
   <field name='CoveredOrUncovered' required='N'/>
   <field name='MaxShow' required='N'/>
   <field name='LocateReqd' required='N'/>
   <field name='CancellationRights' required='N'/>
   <field name='MoneyLaunderingStatus' required='N'/>
   <field name='RegistID' required='N'/>
   <field name='Designation' required='N'/>
   <field name='ManualOrderIndicator' required='N'/>
   <field name='CustDirectedOrder' required='N'/>
   <field name='ReceivedDeptID' required='N'/>
   <field name='CustOrderHandlingInst' required='N'/>
   <field name='OrderHandlingInstSource' required='N'/>
   <component name='TrdRegTimestamps' required='N'/>
  </message>
  <message name='OrderStatusRequest' msgtype='H' msgcat='app'>
   <field name='OrderID' required='N'/>
   <field name='ClOrdID' required='Y'/>
   <field name='SecondaryClOrdID' required='N'/>
   <field name='ClOrdLinkID' required='N'/>
   <component name='Parties' required='N'/>
   <field name='OrdStatusReqID' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='Side' required='Y'/>
  </message>
  <message name='AllocationInstruction' msgtype='J' msgcat='app'>
   <field name='AllocID' required='Y'/>
   <field name='AllocTransType' required='Y'/>
   <field name='AllocType' required='Y'/>
   <field name='SecondaryAllocID' required='N'/>
   <field name='RefAllocID' required='N'/>
   <field name='AllocCancReplaceReason' required='N'/>
   <field name='AllocIntermedReqType' required='N'/>
   <field name='AllocLinkID' required='N'/>
   <field name='AllocLinkType' required='N'/>
   <field name='BookingRefID' required='N'/>
   <field name='AllocNoOrdersType' required='N'/>
   <component name='OrdAllocGrp' required='N'/>
   <component name='ExecAllocGrp' required='N'/>
   <field name='PreviouslyReported' required='N'/>
   <field name='ReversalIndicator' required='N'/>
   <field name='MatchType' required='N'/>
   <field name='Side' required='Y'/>
   <component name='Instrument' required='Y'/>
   <component name='InstrumentExtension' required='N'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='Quantity' required='Y'/>
   <field name='QtyType' required='N'/>
   <field name='LastMkt' required='N'/>
   <field name='TradeOriginationDate' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='PriceType' required='N'/>
   <field name='AvgPx' required='N'/>
   <field name='AvgParPx' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <field name='Currency' required='N'/>
   <field name='AvgPxPrecision' required='N'/>
   <component name='Parties' required='N'/>
   <field name='TradeDate' required='Y'/>
   <field name='TransactTime' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='BookingType' required='N'/>
   <field name='GrossTradeAmt' required='N'/>
   <field name='Concession' required='N'/>
   <field name='TotalTakedown' required='N'/>
   <field name='NetMoney' required='N'/>
   <field name='PositionEffect' required='N'/>
   <field name='AutoAcceptIndicator' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='NumDaysInterest' required='N'/>
   <field name='AccruedInterestRate' required='N'/>
   <field name='AccruedInterestAmt' required='N'/>
   <field name='TotalAccruedInterestAmt' required='N'/>
   <field name='InterestAtMaturity' required='N'/>
   <field name='EndAccruedInterestAmt' required='N'/>
   <field name='StartCash' required='N'/>
   <field name='EndCash' required='N'/>
   <field name='LegalConfirm' required='N'/>
   <component name='Stipulations' required='N'/>
   <component name='YieldData' required='N'/>
   <component name='PositionAmountData' required='N'/>
   <field name='TotNoAllocs' required='N'/>
   <field name='LastFragment' required='N'/>
   <component name='AllocGrp' required='N'/>
   <field name='AvgPxIndicator' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='TrdType' required='N'/>
   <field name='TrdSubType' required='N'/>
   <field name='CustOrderCapacity' required='N'/>
   <field name='TradeInputSource' required='N'/>
   <field name='MultiLegReportingType' required='N'/>
   <field name='MessageEventSource' required='N'/>
   <field name='RndPx' required='N'/>
  </message>
  <message name='ListCancelRequest' msgtype='K' msgcat='app'>
   <field name='ListID' required='Y'/>
   <component name='Parties' required='N'/>
   <field name='TransactTime' required='Y'/>
   <field name='TradeOriginationDate' required='N'/>
   <field name='TradeDate' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='ListExecute' msgtype='L' msgcat='app'>
   <field name='ListID' required='Y'/>
   <field name='ClientBidID' required='N'/>
   <field name='BidID' required='N'/>
   <field name='TransactTime' required='Y'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='ListStatusRequest' msgtype='M' msgcat='app'>
   <field name='ListID' required='Y'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='ListStatus' msgtype='N' msgcat='app'>
   <field name='ListID' required='Y'/>
   <field name='ListStatusType' required='Y'/>
   <field name='NoRpts' required='Y'/>
   <field name='ListOrderStatus' required='Y'/>
   <field name='RptSeq' required='Y'/>
   <field name='ListStatusText' required='N'/>
   <field name='EncodedListStatusTextLen' required='N'/>
   <field name='EncodedListStatusText' required='N'/>
   <field name='TransactTime' required='N'/>
   <field name='TotNoOrders' required='Y'/>
   <field name='LastFragment' required='N'/>
   <component name='OrdListStatGrp' required='Y'/>
  </message>
  <message name='AllocationInstructionAck' msgtype='P' msgcat='app'>
   <field name='AllocID' required='Y'/>
   <component name='Parties' required='N'/>
   <field name='SecondaryAllocID' required='N'/>
   <field name='TradeDate' required='N'/>
   <field name='TransactTime' required='N'/>
   <field name='AllocStatus' required='Y'/>
   <field name='AllocRejCode' required='N'/>
   <field name='AllocType' required='N'/>
   <field name='AllocIntermedReqType' required='N'/>
   <field name='MatchStatus' required='N'/>
   <field name='Product' required='N'/>
   <field name='SecurityType' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <component name='AllocAckGrp' required='N'/>
  </message>
  <message name='DontKnowTrade' msgtype='Q' msgcat='app'>
   <field name='OrderID' required='Y'/>
   <field name='SecondaryOrderID' required='N'/>
   <field name='ExecID' required='Y'/>
   <field name='DKReason' required='Y'/>
   <component name='Instrument' required='Y'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='Side' required='Y'/>
   <component name='OrderQtyData' required='Y'/>
   <field name='LastQty' required='N'/>
   <field name='LastPx' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='QuoteRequest' msgtype='R' msgcat='app'>
   <field name='QuoteReqID' required='Y'/>
   <field name='RFQReqID' required='N'/>
   <field name='ClOrdID' required='N'/>
   <field name='OrderCapacity' required='N'/>
   <component name='QuotReqGrp' required='Y'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='Quote' msgtype='S' msgcat='app'>
   <field name='QuoteReqID' required='N'/>
   <field name='QuoteID' required='Y'/>
   <field name='QuoteRespID' required='N'/>
   <field name='QuoteType' required='N'/>
   <component name='QuotQualGrp' required='N'/>
   <field name='QuoteResponseLevel' required='N'/>
   <component name='Parties' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='Side' required='N'/>
   <component name='OrderQtyData' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='SettlDate2' required='N'/>
   <field name='OrderQty2' required='N'/>
   <field name='Currency' required='N'/>
   <component name='Stipulations' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <component name='LegQuotGrp' required='N'/>
   <field name='BidPx' required='N'/>
   <field name='OfferPx' required='N'/>
   <field name='MktBidPx' required='N'/>
   <field name='MktOfferPx' required='N'/>
   <field name='MinBidSize' required='N'/>
   <field name='BidSize' required='N'/>
   <field name='MinOfferSize' required='N'/>
   <field name='OfferSize' required='N'/>
   <field name='ValidUntilTime' required='N'/>
   <field name='BidSpotRate' required='N'/>
   <field name='OfferSpotRate' required='N'/>
   <field name='BidForwardPoints' required='N'/>
   <field name='OfferForwardPoints' required='N'/>
   <field name='BidSwapPoints' required='N'/>
   <field name='OfferSwapPoints' required='N'/>
   <field name='MidPx' required='N'/>
   <field name='BidYield' required='N'/>
   <field name='MidYield' required='N'/>
   <field name='OfferYield' required='N'/>
   <field name='TransactTime' required='N'/>
   <field name='OrdType' required='N'/>
   <field name='BidForwardPoints2' required='N'/>
   <field name='OfferForwardPoints2' required='N'/>
   <field name='SettlCurrBidFxRate' required='N'/>
   <field name='SettlCurrOfferFxRate' required='N'/>
   <field name='SettlCurrFxRateCalc' required='N'/>
   <field name='CommType' required='N'/>
   <field name='Commission' required='N'/>
   <field name='CustOrderCapacity' required='N'/>
   <field name='ExDestination' required='N'/>
   <field name='ExDestinationIDSource' required='N'/>
   <field name='OrderCapacity' required='N'/>
   <field name='PriceType' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <component name='YieldData' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='SettlementInstructions' msgtype='T' msgcat='app'>
   <field name='SettlInstMsgID' required='Y'/>
   <field name='SettlInstReqID' required='N'/>
   <field name='SettlInstMode' required='Y'/>
   <field name='SettlInstReqRejCode' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='ClOrdID' required='N'/>
   <field name='TransactTime' required='Y'/>
   <component name='SettlInstGrp' required='N'/>
  </message>
  <message name='MarketDataRequest' msgtype='V' msgcat='app'>
   <field name='MDReqID' required='Y'/>
   <field name='SubscriptionRequestType' required='Y'/>
   <field name='MarketDepth' required='Y'/>
   <field name='MDUpdateType' required='N'/>
   <field name='AggregatedBook' required='N'/>
   <field name='OpenCloseSettlFlag' required='N'/>
   <field name='Scope' required='N'/>
   <field name='MDImplicitDelete' required='N'/>
   <component name='MDReqGrp' required='Y'/>
   <component name='InstrmtMDReqGrp' required='Y'/>
   <component name='TrdgSesGrp' required='N'/>
   <field name='ApplQueueAction' required='N'/>
   <field name='ApplQueueMax' required='N'/>
   <field name='MDQuoteType' required='N'/>
  </message>
  <message name='MarketDataSnapshotFullRefresh' msgtype='W' msgcat='app'>
   <field name='MDReportID' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='MDBookType' required='N'/>
   <field name='MDFeedType' required='N'/>
   <field name='TradeDate' required='N'/>
   <field name='MDReqID' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='FinancialStatus' required='N'/>
   <field name='CorporateAction' required='N'/>
   <field name='NetChgPrevDay' required='N'/>
   <component name='MDFullGrp' required='Y'/>
   <field name='ApplQueueDepth' required='N'/>
   <field name='ApplQueueResolution' required='N'/>
   <component name='RoutingGrp' required='N'/>
  </message>
  <message name='MarketDataIncrementalRefresh' msgtype='X' msgcat='app'>
   <field name='MDBookType' required='N'/>
   <field name='MDFeedType' required='N'/>
   <field name='TradeDate' required='N'/>
   <field name='MDReqID' required='N'/>
   <component name='MDIncGrp' required='Y'/>
   <field name='ApplQueueDepth' required='N'/>
   <field name='ApplQueueResolution' required='N'/>
   <component name='RoutingGrp' required='N'/>
  </message>
  <message name='MarketDataRequestReject' msgtype='Y' msgcat='app'>
   <field name='MDReqID' required='Y'/>
   <field name='MDReqRejReason' required='N'/>
   <component name='MDRjctGrp' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='QuoteCancel' msgtype='Z' msgcat='app'>
   <field name='QuoteReqID' required='N'/>
   <field name='QuoteID' required='N'/>
   <field name='QuoteCancelType' required='Y'/>
   <field name='QuoteResponseLevel' required='N'/>
   <component name='Parties' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <component name='QuotCxlEntriesGrp' required='N'/>
  </message>
  <message name='QuoteStatusRequest' msgtype='a' msgcat='app'>
   <field name='QuoteStatusReqID' required='N'/>
   <field name='QuoteID' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <component name='Parties' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='SubscriptionRequestType' required='N'/>
  </message>
  <message name='MassQuoteAcknowledgement' msgtype='b' msgcat='app'>
   <field name='QuoteReqID' required='N'/>
   <field name='QuoteID' required='N'/>
   <field name='QuoteStatus' required='Y'/>
   <field name='QuoteRejectReason' required='N'/>
   <field name='QuoteResponseLevel' required='N'/>
   <field name='QuoteType' required='N'/>
   <component name='Parties' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <component name='QuotSetAckGrp' required='N'/>
  </message>
  <message name='SecurityDefinitionRequest' msgtype='c' msgcat='app'>
   <field name='SecurityReqID' required='Y'/>
   <field name='SecurityRequestType' required='Y'/>
   <component name='Instrument' required='N'/>
   <component name='InstrumentExtension' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='Currency' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='ExpirationCycle' required='N'/>
   <field name='SubscriptionRequestType' required='N'/>
  </message>
  <message name='SecurityDefinition' msgtype='d' msgcat='app'>
   <field name='SecurityReportID' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='SecurityReqID' required='N'/>
   <field name='SecurityResponseID' required='N'/>
   <field name='SecurityResponseType' required='N'/>
   <component name='Instrument' required='N'/>
   <component name='InstrumentExtension' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <field name='Currency' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='ExpirationCycle' required='N'/>
   <field name='RoundLot' required='N'/>
   <field name='MinTradeVol' required='N'/>
  </message>
  <message name='SecurityStatusRequest' msgtype='e' msgcat='app'>
   <field name='SecurityStatusReqID' required='Y'/>
   <component name='Instrument' required='Y'/>
   <component name='InstrumentExtension' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='Currency' required='N'/>
   <field name='SubscriptionRequestType' required='Y'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
  </message>
  <message name='SecurityStatus' msgtype='f' msgcat='app'>
   <field name='SecurityStatusReqID' required='N'/>
   <component name='Instrument' required='Y'/>
   <component name='InstrumentExtension' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='Currency' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='UnsolicitedIndicator' required='N'/>
   <field name='SecurityTradingStatus' required='N'/>
   <field name='FinancialStatus' required='N'/>
   <field name='CorporateAction' required='N'/>
   <field name='HaltReasonChar' required='N'/>
   <field name='InViewOfCommon' required='N'/>
   <field name='DueToRelated' required='N'/>
   <field name='BuyVolume' required='N'/>
   <field name='SellVolume' required='N'/>
   <field name='HighPx' required='N'/>
   <field name='LowPx' required='N'/>
   <field name='LastPx' required='N'/>
   <field name='TransactTime' required='N'/>
   <field name='Adjustment' required='N'/>
   <field name='FirstPx' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='TradingSessionStatusRequest' msgtype='g' msgcat='app'>
   <field name='TradSesReqID' required='Y'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='TradSesMethod' required='N'/>
   <field name='TradSesMode' required='N'/>
   <field name='SubscriptionRequestType' required='Y'/>
   <field name='SecurityExchange' required='N'/>
  </message>
  <message name='TradingSessionStatus' msgtype='h' msgcat='app'>
   <field name='TradSesReqID' required='N'/>
   <field name='TradingSessionID' required='Y'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='TradSesMethod' required='N'/>
   <field name='TradSesMode' required='N'/>
   <field name='UnsolicitedIndicator' required='N'/>
   <field name='TradSesStatus' required='Y'/>
   <field name='TradSesStatusRejReason' required='N'/>
   <field name='TradSesStartTime' required='N'/>
   <field name='TradSesOpenTime' required='N'/>
   <field name='TradSesPreCloseTime' required='N'/>
   <field name='TradSesCloseTime' required='N'/>
   <field name='TradSesEndTime' required='N'/>
   <field name='TotalVolumeTraded' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <component name='Instrument' required='N'/>
  </message>
  <message name='MassQuote' msgtype='i' msgcat='app'>
   <field name='QuoteReqID' required='N'/>
   <field name='QuoteID' required='Y'/>
   <field name='QuoteType' required='N'/>
   <field name='QuoteResponseLevel' required='N'/>
   <component name='Parties' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='AccountType' required='N'/>
   <field name='DefBidSize' required='N'/>
   <field name='DefOfferSize' required='N'/>
   <component name='QuotSetGrp' required='Y'/>
  </message>
  <message name='BusinessMessageReject' msgtype='j' msgcat='app'>
   <field name='RefSeqNum' required='N'/>
   <field name='RefMsgType' required='Y'/>
   <field name='BusinessRejectRefID' required='N'/>
   <field name='BusinessRejectReason' required='Y'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='BidRequest' msgtype='k' msgcat='app'>
   <field name='BidID' required='N'/>
   <field name='ClientBidID' required='Y'/>
   <field name='BidRequestTransType' required='Y'/>
   <field name='ListName' required='N'/>
   <field name='TotNoRelatedSym' required='Y'/>
   <field name='BidType' required='Y'/>
   <field name='NumTickets' required='N'/>
   <field name='Currency' required='N'/>
   <field name='SideValue1' required='N'/>
   <field name='SideValue2' required='N'/>
   <component name='BidDescReqGrp' required='N'/>
   <component name='BidCompReqGrp' required='N'/>
   <field name='LiquidityIndType' required='N'/>
   <field name='WtAverageLiquidity' required='N'/>
   <field name='ExchangeForPhysical' required='N'/>
   <field name='OutMainCntryUIndex' required='N'/>
   <field name='CrossPercent' required='N'/>
   <field name='ProgRptReqs' required='N'/>
   <field name='ProgPeriodInterval' required='N'/>
   <field name='IncTaxInd' required='N'/>
   <field name='ForexReq' required='N'/>
   <field name='NumBidders' required='N'/>
   <field name='TradeDate' required='N'/>
   <field name='BidTradeType' required='Y'/>
   <field name='BasisPxType' required='Y'/>
   <field name='StrikeTime' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='BidResponse' msgtype='l' msgcat='app'>
   <field name='BidID' required='N'/>
   <field name='ClientBidID' required='N'/>
   <component name='BidCompRspGrp' required='Y'/>
  </message>
  <message name='ListStrikePrice' msgtype='m' msgcat='app'>
   <field name='ListID' required='Y'/>
   <field name='TotNoStrikes' required='Y'/>
   <field name='LastFragment' required='N'/>
   <component name='InstrmtStrkPxGrp' required='Y'/>
   <component name='UndInstrmtStrkPxGrp' required='N'/>
  </message>
  <message name='XMLnonFIX' msgtype='n' msgcat='admin'/>
  <message name='RegistrationInstructions' msgtype='o' msgcat='app'>
   <field name='RegistID' required='Y'/>
   <field name='RegistTransType' required='Y'/>
   <field name='RegistRefID' required='Y'/>
   <field name='ClOrdID' required='N'/>
   <component name='Parties' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='RegistAcctType' required='N'/>
   <field name='TaxAdvantageType' required='N'/>
   <field name='OwnershipType' required='N'/>
   <component name='RgstDtlsGrp' required='N'/>
   <component name='RgstDistInstGrp' required='N'/>
  </message>
  <message name='RegistrationInstructionsResponse' msgtype='p' msgcat='app'>
   <field name='RegistID' required='Y'/>
   <field name='RegistTransType' required='Y'/>
   <field name='RegistRefID' required='Y'/>
   <field name='ClOrdID' required='N'/>
   <component name='Parties' required='N'/>
   <field name='Account' required='N'/>
   <field name='AcctIDSource' required='N'/>
   <field name='RegistStatus' required='Y'/>
   <field name='RegistRejReasonCode' required='N'/>
   <field name='RegistRejReasonText' required='N'/>
  </message>
  <message name='OrderMassCancelRequest' msgtype='q' msgcat='app'>
   <field name='ClOrdID' required='Y'/>
   <field name='SecondaryClOrdID' required='N'/>
   <field name='MassCancelRequestType' required='Y'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <component name='Parties' required='N'/>
   <component name='Instrument' required='N'/>
   <component name='UnderlyingInstrument' required='N'/>
   <field name='Side' required='N'/>
   <field name='TransactTime' required='Y'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='OrderMassCancelReport' msgtype='r' msgcat='app'>
   <field name='ClOrdID' required='N'/>
   <field name='SecondaryClOrdID' required='N'/>
   <field name='OrderID' required='Y'/>
   <field name='SecondaryOrderID' required='N'/>
   <field name='MassCancelRequestType' required='Y'/>
   <field name='MassCancelResponse' required='Y'/>
   <field name='MassCancelRejectReason' required='N'/>
   <field name='TotalAffectedOrders' required='N'/>
   <component name='AffectedOrdGrp' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <component name='Parties' required='N'/>
   <component name='Instrument' required='N'/>
   <component name='UnderlyingInstrument' required='N'/>
   <field name='Side' required='N'/>
   <field name='TransactTime' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
  </message>
  <message name='NewOrderCross' msgtype='s' msgcat='app'>
   <field name='CrossID' required='Y'/>
   <field name='CrossType' required='Y'/>
   <field name='CrossPrioritization' required='Y'/>
   <component name='RootParties' required='N'/>
   <component name='SideCrossOrdModGrp' required='Y'/>
   <component name='Instrument' required='Y'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='HandlInst' required='N'/>
   <field name='ExecInst' required='N'/>
   <field name='MinQty' required='N'/>
   <field name='MatchIncrement' required='N'/>
   <field name='MaxPriceLevels' required='N'/>
   <component name='DisplayInstruction' required='N'/>
   <field name='MaxFloor' required='N'/>
   <field name='ExDestination' required='N'/>
   <field name='ExDestinationIDSource' required='N'/>
   <component name='TrdgSesGrp' required='N'/>
   <field name='ProcessCode' required='N'/>
   <field name='PrevClosePx' required='N'/>
   <field name='LocateReqd' required='N'/>
   <field name='TransactTime' required='Y'/>
   <field name='TransBkdTime' required='N'/>
   <component name='Stipulations' required='N'/>
   <field name='OrdType' required='Y'/>
   <field name='PriceType' required='N'/>
   <field name='Price' required='N'/>
   <field name='PriceProtectionScope' required='N'/>
   <field name='StopPx' required='N'/>
   <component name='TriggeringInstruction' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <component name='YieldData' required='N'/>
   <field name='Currency' required='N'/>
   <field name='ComplianceID' required='N'/>
   <field name='IOIID' required='N'/>
   <field name='QuoteID' required='N'/>
   <field name='TimeInForce' required='N'/>
   <field name='EffectiveTime' required='N'/>
   <field name='ExpireDate' required='N'/>
   <field name='ExpireTime' required='N'/>
   <field name='GTBookingInst' required='N'/>
   <field name='MaxShow' required='N'/>
   <component name='PegInstructions' required='N'/>
   <component name='DiscretionInstructions' required='N'/>
   <field name='TargetStrategy' required='N'/>
   <component name='StrategyParametersGrp' required='N'/>
   <field name='TargetStrategyParameters' required='N'/>
   <field name='ParticipationRate' required='N'/>
   <field name='CancellationRights' required='N'/>
   <field name='MoneyLaunderingStatus' required='N'/>
   <field name='RegistID' required='N'/>
   <field name='Designation' required='N'/>
  </message>
  <message name='CrossOrderCancelReplaceRequest' msgtype='t' msgcat='app'>
   <field name='OrderID' required='N'/>
   <field name='CrossID' required='Y'/>
   <field name='OrigCrossID' required='Y'/>
   <field name='HostCrossID' required='N'/>
   <field name='CrossType' required='Y'/>
   <field name='CrossPrioritization' required='Y'/>
   <component name='RootParties' required='N'/>
   <component name='SideCrossOrdModGrp' required='Y'/>
   <component name='Instrument' required='Y'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='SettlType' required='N'/>
   <field name='SettlDate' required='N'/>
   <field name='HandlInst' required='N'/>
   <field name='ExecInst' required='N'/>
   <field name='MinQty' required='N'/>
   <field name='MatchIncrement' required='N'/>
   <field name='MaxPriceLevels' required='N'/>
   <component name='DisplayInstruction' required='N'/>
   <field name='MaxFloor' required='N'/>
   <field name='ExDestination' required='N'/>
   <field name='ExDestinationIDSource' required='N'/>
   <component name='TrdgSesGrp' required='N'/>
   <field name='ProcessCode' required='N'/>
   <field name='PrevClosePx' required='N'/>
   <field name='LocateReqd' required='N'/>
   <field name='TransactTime' required='Y'/>
   <field name='TransBkdTime' required='N'/>
   <component name='Stipulations' required='N'/>
   <field name='OrdType' required='Y'/>
   <field name='PriceType' required='N'/>
   <field name='Price' required='N'/>
   <field name='PriceProtectionScope' required='N'/>
   <field name='StopPx' required='N'/>
   <component name='TriggeringInstruction' required='N'/>
   <component name='SpreadOrBenchmarkCurveData' required='N'/>
   <component name='YieldData' required='N'/>
   <field name='Currency' required='N'/>
   <field name='ComplianceID' required='N'/>
   <field name='IOIID' required='N'/>
   <field name='QuoteID' required='N'/>
   <field name='TimeInForce' required='N'/>
   <field name='EffectiveTime' required='N'/>
   <field name='ExpireDate' required='N'/>
   <field name='ExpireTime' required='N'/>
   <field name='GTBookingInst' required='N'/>
   <field name='MaxShow' required='N'/>
   <component name='PegInstructions' required='N'/>
   <component name='DiscretionInstructions' required='N'/>
   <field name='TargetStrategy' required='N'/>
   <component name='StrategyParametersGrp' required='N'/>
   <field name='TargetStrategyParameters' required='N'/>
   <field name='ParticipationRate' required='N'/>
   <field name='CancellationRights' required='N'/>
   <field name='MoneyLaunderingStatus' required='N'/>
   <field name='RegistID' required='N'/>
   <field name='Designation' required='N'/>
  </message>
  <message name='CrossOrderCancelRequest' msgtype='u' msgcat='app'>
   <field name='OrderID' required='N'/>
   <field name='CrossID' required='Y'/>
   <field name='OrigCrossID' required='Y'/>
   <field name='HostCrossID' required='N'/>
   <field name='CrossType' required='Y'/>
   <field name='CrossPrioritization' required='Y'/>
   <component name='RootParties' required='N'/>
   <component name='SideCrossOrdCxlGrp' required='Y'/>
   <component name='Instrument' required='Y'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='TransactTime' required='Y'/>
  </message>
  <message name='SecurityTypeRequest' msgtype='v' msgcat='app'>
   <field name='SecurityReqID' required='Y'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='Product' required='N'/>
   <field name='SecurityType' required='N'/>
   <field name='SecuritySubType' required='N'/>
  </message>
  <message name='SecurityTypes' msgtype='w' msgcat='app'>
   <field name='SecurityReqID' required='Y'/>
   <field name='SecurityResponseID' required='Y'/>
   <field name='SecurityResponseType' required='Y'/>
   <field name='TotNoSecurityTypes' required='N'/>
   <field name='LastFragment' required='N'/>
   <component name='SecTypesGrp' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='SubscriptionRequestType' required='N'/>
  </message>
  <message name='SecurityListRequest' msgtype='x' msgcat='app'>
   <field name='SecurityReqID' required='Y'/>
   <field name='SecurityListRequestType' required='Y'/>
   <component name='Instrument' required='N'/>
   <component name='InstrumentExtension' required='N'/>
   <component name='FinancingDetails' required='N'/>
   <component name='UndInstrmtGrp' required='N'/>
   <component name='InstrmtLegGrp' required='N'/>
   <field name='Currency' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='SubscriptionRequestType' required='N'/>
  </message>
  <message name='SecurityList' msgtype='y' msgcat='app'>
   <field name='SecurityReportID' required='N'/>
   <field name='ClearingBusinessDate' required='N'/>
   <field name='SecurityReqID' required='N'/>
   <field name='SecurityResponseID' required='N'/>
   <field name='SecurityRequestResult' required='N'/>
   <field name='TotNoRelatedSym' required='N'/>
   <field name='LastFragment' required='N'/>
   <component name='SecListGrp' required='N'/>
  </message>
  <message name='DerivativeSecurityListRequest' msgtype='z' msgcat='app'>
   <field name='SecurityReqID' required='Y'/>
   <field name='SecurityListRequestType' required='Y'/>
   <component name='UnderlyingInstrument' required='N'/>
   <field name='SecuritySubType' required='N'/>
   <field name='Currency' required='N'/>
   <field name='Text' required='N'/>
   <field name='EncodedTextLen' required='N'/>
   <field name='EncodedText' required='N'/>
   <field name='TradingSessionID' required='N'/>
   <field name='TradingSessionSubID' required='N'/>
   <field name='SubscriptionRequestType' required='N'/>
  </message>
 </messages>
 <trailer>
  <field name='SignatureLength' required='N'/>
  <field name='Signature' required='N'/>
  <field name='CheckSum' required='Y'/>
 </trailer>
 <components>
  <component name='HopGrp'>
   <group name='NoHops' required='N'>
    <field name='HopCompID' required='N'/>
    <field name='HopSendingTime' required='N'/>
    <field name='HopRefID' required='N'/>
   </group>
  </component>
  <component name='CommissionData'>
   <field name='Commission' required='N'/>
   <field name='CommType' required='N'/>
   <field name='CommCurrency' required='N'/>
   <field name='FundRenewWaiv' required='N'/>
  </component>
  <component name='DiscretionInstructions'>
   <field name='DiscretionInst' required='N'/>
   <field name='DiscretionOffsetValue' required='N'/>
   <field name='DiscretionMoveType' required='N'/>
   <field name='DiscretionOffsetType' required='N'/>
   <field name='DiscretionLimitType' required='N'/>
   <field name='DiscretionRoundDirection' required='N'/>
   <field name='DiscretionScope' required='N'/>
  </component>
  <component name='FinancingDetails'>
   <field name='AgreementDesc' required='N'/>
   <field name='AgreementID' required='N'/>
   <field name='AgreementDate' required='N'/>
   <field name='AgreementCurrency' required='N'/>
   <field name='TerminationType' required='N'/>
   <field name='StartDate' required='N'/>
   <field name='EndDate' required='N'/>
   <field name='DeliveryType' required='N'/>
   <field name='MarginRatio' required='N'/>
  </component>
  <component name='Instrument'>
   <field name='Symbol' required='N'/>
   <field name='SymbolSfx' required='N'/>
   <field name='SecurityID' required='N'/>
   <field name='SecurityIDSource' required='N'/>
   <component name='SecAltIDGrp' required='N'/>
   <field name='Product' required='N'/>
   <field name='CFICode' required='N'/>
   <field name='SecurityType' required='N'/>
   <field name='SecuritySubType' required='N'/>
   <field name='MaturityMonthYear' required='N'/>
   <field name='MaturityDate' required='N'/>
   <field name='MaturityTime' required='N'/>
   <field name='PutOrCall' required='N'/>
   <field name='SettleOnOpenFlag' required='N'/>
   <field name='InstrmtAssignmentMethod' required='N'/>
   <field name='SecurityStatus' required='N'/>
   <field name='CouponPaymentDate' required='N'/>
   <field name='IssueDate' required='N'/>
   <field name='RepoCollateralSecurityType' required='N'/>
   <field name='RepurchaseTerm' required='N'/>
   <field name='RepurchaseRate' required='N'/>
   <field name='Factor' required='N'/>
   <field name='CreditRating' required='N'/>
   <field name='InstrRegistry' required='N'/>
   <field name='CountryOfIssue' required='N'/>
   <field name='StateOrProvinceOfIssue' required='N'/>
   <field name='LocaleOfIssue' required='N'/>
   <field name='RedemptionDate' required='N'/>
   <field name='StrikePrice' required='N'/>
   <field name='StrikeCurrency' required='N'/>
   <field name='StrikeMultiplier' required='N'/>
   <field name='StrikeValue' required='N'/>
   <field name='OptAttribute' required='N'/>
   <field name='ContractMultiplier' required='N'/>
   <field name='MinPriceIncrement' required='N'/>
   <field name='UnitofMeasure' required='N'/>
   <field name='TimeUnit' required='N'/>
   <field name='CouponRate' required='N'/>
   <field name='SecurityExchange' required='N'/>
   <field name='PositionLimit' required='N'/>
   <field name='NTPositionLimit' required='N'/>
   <field name='Issuer' required='N'/>
   <field name='EncodedIssuerLen' required='N'/>
   <field name='EncodedIssuer' required='N'/>
   <field name='SecurityDesc' required='N'/>
   <field name='EncodedSecurityDescLen' required='N'/>
   <field name='EncodedSecurityDesc' required='N'/>
   <field name='Pool' required='N'/>
   <field name='ContractSettlMonth' required='N'/>
   <field name='CPProgram' required='N'/>
   <field name='CPRegType' required='N'/>
   <component name='EvntGrp' required='N'/>
   <field name='DatedDate' required='N'/>
   <field name='InterestAccrualDate' required='N'/>
   <component name='InstrumentParties' required='N'/>
  </component>
  <component name='InstrumentExtension'>
   <field name='DeliveryForm' required='N'/>
   <field name='PctAtRisk' required='N'/>
   <component name='AttrbGrp' required='N'/>
  </component>
  <component name='InstrumentLeg'>
   <field name='LegSymbol' required='N'/>
   <field name='LegSymbolSfx' required='N'/>
   <field name='LegSecurityID' required='N'/>
   <field name='LegSecurityIDSource' required='N'/>
   <component name='LegSecAltIDGrp' required='N'/>
   <field name='LegProduct' required='N'/>
   <field name='LegCFICode' required='N'/>
   <field name='LegSecurityType' required='N'/>
   <field name='LegSecuritySubType' required='N'/>
   <field name='LegMaturityMonthYear' required='N'/>
   <field name='LegMaturityDate' required='N'/>
   <field name='LegCouponPaymentDate' required='N'/>
   <field name='LegIssueDate' required='N'/>
   <field name='LegRepoCollateralSecurityType' required='N'/>
   <field name='LegRepurchaseTerm' required='N'/>
   <field name='LegRepurchaseRate' required='N'/>
   <field name='LegFactor' required='N'/>
   <field name='LegCreditRating' required='N'/>
   <field name='LegInstrRegistry' required='N'/>
   <field name='LegCountryOfIssue' required='N'/>
   <field name='LegStateOrProvinceOfIssue' required='N'/>
   <field name='LegLocaleOfIssue' required='N'/>
   <field name='LegRedemptionDate' required='N'/>
   <field name='LegStrikePrice' required='N'/>
   <field name='LegStrikeCurrency' required='N'/>
   <field name='LegOptAttribute' required='N'/>
   <field name='LegContractMultiplier' required='N'/>
   <field name='LegUnitofMeasure' required='N'/>
   <field name='LegTimeUnit' required='N'/>
   <field name='LegCouponRate' required='N'/>
   <field name='LegSecurityExchange' required='N'/>
   <field name='LegIssuer' required='N'/>
   <field name='EncodedLegIssuerLen' required='N'/>
   <field name='EncodedLegIssuer' required='N'/>
   <field name='LegSecurityDesc' required='N'/>
   <field name='EncodedLegSecurityDescLen' required='N'/>
   <field name='EncodedLegSecurityDesc' required='N'/>
   <field name='LegRatioQty' required='N'/>
   <field name='LegSide' required='N'/>
   <field name='LegCurrency' required='N'/>
   <field name='LegPool' required='N'/>
   <field name='LegDatedDate' required='N'/>
   <field name='LegContractSettlMonth' required='N'/>
   <field name='LegInterestAccrualDate' required='N'/>
   <field name='LegOptionRatio' required='N'/>
   <field name='LegPrice' required='N'/>
  </component>
  <component name='LegBenchmarkCurveData'>
   <field name='LegBenchmarkCurveCurrency' required='N'/>
   <field name='LegBenchmarkCurveName' required='N'/>
   <field name='LegBenchmarkCurvePoint' required='N'/>
   <field name='LegBenchmarkPrice' required='N'/>
   <field name='LegBenchmarkPriceType' required='N'/>
  </component>
  <component name='LegStipulations'>
   <group name='NoLegStipulations' required='N'>
    <field name='LegStipulationType' required='N'/>
    <field name='LegStipulationValue' required='N'/>
   </group>
  </component>
  <component name='NestedParties'>
   <group name='NoNestedPartyIDs' required='N'>
    <field name='NestedPartyID' required='N'/>
    <field name='NestedPartyIDSource' required='N'/>
    <field name='NestedPartyRole' required='N'/>
    <component name='NstdPtysSubGrp' required='N'/>
   </group>
  </component>
  <component name='OrderQtyData'>
   <field name='OrderQty' required='N'/>
   <field name='CashOrderQty' required='N'/>
   <field name='OrderPercent' required='N'/>
   <field name='RoundingDirection' required='N'/>
   <field name='RoundingModulus' required='N'/>
  </component>
  <component name='Parties'>
   <group name='NoPartyIDs' required='N'>
    <field name='PartyID' required='N'/>
    <field name='PartyIDSource' required='N'/>
    <field name='PartyRole' required='N'/>
    <component name='PtysSubGrp' required='N'/>
   </group>
  </component>
  <component name='PegInstructions'>
   <field name='PegOffsetValue' required='N'/>
   <field name='PegPriceType' required='N'/>
   <field name='PegMoveType' required='N'/>
   <field name='PegOffsetType' required='N'/>
   <field name='PegLimitType' required='N'/>
   <field name='PegRoundDirection' required='N'/>
   <field name='PegScope' required='N'/>
   <field name='PegSecurityIDSource' required='N'/>
   <field name='PegSecurityID' required='N'/>
   <field name='PegSymbol' required='N'/>
   <field name='PegSecurityDesc' required='N'/>
  </component>
  <component name='PositionAmountData'>
   <group name='NoPosAmt' required='N'>
    <field name='PosAmtType' required='N'/>
    <field name='PosAmt' required='N'/>
    <field name='PositionCurrency' required='N'/>
   </group>
  </component>
  <component name='PositionQty'>
   <group name='NoPositions' required='N'>
    <field name='PosType' required='N'/>
    <field name='LongQty' required='N'/>
    <field name='ShortQty' required='N'/>
    <field name='PosQtyStatus' required='N'/>
    <field name='QuantityDate' required='N'/>
    <component name='NestedParties' required='N'/>
   </group>
  </component>
  <component name='SettlInstructionsData'>
   <field name='SettlDeliveryType' required='N'/>
   <field name='StandInstDbType' required='N'/>
   <field name='StandInstDbName' required='N'/>
   <field name='StandInstDbID' required='N'/>
   <component name='DlvyInstGrp' required='N'/>
  </component>
  <component name='SettlParties'>
   <group name='NoSettlPartyIDs' required='N'>
    <field name='SettlPartyID' required='N'/>
    <field name='SettlPartyIDSource' required='N'/>
    <field name='SettlPartyRole' required='N'/>
    <component name='SettlPtysSubGrp' required='N'/>
   </group>
  </component>
  <component name='SpreadOrBenchmarkCurveData'>
   <field name='Spread' required='N'/>
   <field name='BenchmarkCurveCurrency' required='N'/>
   <field name='BenchmarkCurveName' required='N'/>
   <field name='BenchmarkCurvePoint' required='N'/>
   <field name='BenchmarkPrice' required='N'/>
   <field name='BenchmarkPriceType' required='N'/>
   <field name='BenchmarkSecurityID' required='N'/>
   <field name='BenchmarkSecurityIDSource' required='N'/>
  </component>
  <component name='Stipulations'>
   <group name='NoStipulations' required='N'>
    <field name='StipulationType' required='N'/>
    <field name='StipulationValue' required='N'/>
   </group>
  </component>
  <component name='TrdRegTimestamps'>
   <group name='NoTrdRegTimestamps' required='N'>
    <field name='TrdRegTimestamp' required='N'/>
    <field name='TrdRegTimestampType' required='N'/>
    <field name='TrdRegTimestampOrigin' required='N'/>
    <field name='DeskType' required='N'/>
    <field name='DeskTypeSource' required='N'/>
    <field name='DeskOrderHandlingInst' required='N'/>
   </group>
  </component>
  <component name='UnderlyingInstrument'>
   <field name='UnderlyingSymbol' required='N'/>
   <field name='UnderlyingSymbolSfx' required='N'/>
   <field name='UnderlyingSecurityID' required='N'/>
   <field name='UnderlyingSecurityIDSource' required='N'/>
   <component name='UndSecAltIDGrp' required='N'/>
   <field name='UnderlyingProduct' required='N'/>
   <field name='UnderlyingCFICode' required='N'/>
   <field name='UnderlyingSecurityType' required='N'/>
   <field name='UnderlyingSecuritySubType' required='N'/>
   <field name='UnderlyingMaturityMonthYear' required='N'/>
   <field name='UnderlyingMaturityDate' required='N'/>
   <field name='UnderlyingPutOrCall' required='N'/>
   <field name='UnderlyingCouponPaymentDate' required='N'/>
   <field name='UnderlyingIssueDate' required='N'/>
   <field name='UnderlyingRepoCollateralSecurityType' required='N'/>
   <field name='UnderlyingRepurchaseTerm' required='N'/>
   <field name='UnderlyingRepurchaseRate' required='N'/>
   <field name='UnderlyingFactor' required='N'/>
   <field name='UnderlyingCreditRating' required='N'/>
   <field name='UnderlyingInstrRegistry' required='N'/>
   <field name='UnderlyingCountryOfIssue' required='N'/>
   <field name='UnderlyingStateOrProvinceOfIssue' required='N'/>
   <field name='UnderlyingLocaleOfIssue' required='N'/>
   <field name='UnderlyingRedemptionDate' required='N'/>
   <field name='UnderlyingStrikePrice' required='N'/>
   <field name='UnderlyingStrikeCurrency' required='N'/>
   <field name='UnderlyingOptAttribute' required='N'/>
   <field name='UnderlyingContractMultiplier' required='N'/>
   <field name='UnderlyingUnitofMeasure' required='N'/>
   <field name='UnderlyingTimeUnit' required='N'/>
   <field name='UnderlyingCouponRate' required='N'/>
   <field name='UnderlyingSecurityExchange' required='N'/>
   <field name='UnderlyingIssuer' required='N'/>
   <field name='EncodedUnderlyingIssuerLen' required='N'/>
   <field name='EncodedUnderlyingIssuer' required='N'/>
   <field name='UnderlyingSecurityDesc' required='N'/>
   <field name='EncodedUnderlyingSecurityDescLen' required='N'/>
   <field name='EncodedUnderlyingSecurityDesc' required='N'/>
   <field name='UnderlyingCPProgram' required='N'/>
   <field name='UnderlyingCPRegType' required='N'/>
   <field name='UnderlyingAllocationPercent' required='N'/>
   <field name='UnderlyingCurrency' required='N'/>
   <field name='UnderlyingQty' required='N'/>
   <field name='UnderlyingSettlementType' required='N'/>
   <field name='UnderlyingCashAmount' required='N'/>
   <field name='UnderlyingCashType' required='N'/>
   <field name='UnderlyingPx' required='N'/>
   <field name='UnderlyingDirtyPrice' required='N'/>
   <field name='UnderlyingEndPrice' required='N'/>
   <field name='UnderlyingStartValue' required='N'/>
   <field name='UnderlyingCurrentValue' required='N'/>
   <field name='UnderlyingEndValue' required='N'/>
   <component name='UnderlyingStipulations' required='N'/>
   <field name='UnderlyingAdjustedQuantity' required='N'/>
   <field name='UnderlyingFXRate' required='N'/>
   <field name='UnderlyingFXRateCalc' required='N'/>
   <field name='UnderlyingCapValue' required='N'/>
   <component name='UndlyInstrumentParties' required='N'/>
   <field name='UnderlyingSettlMethod' required='N'/>
  </component>
  <component name='YieldData'>
   <field name='YieldType' required='N'/>
   <field name='Yield' required='N'/>
   <field name='YieldCalcDate' required='N'/>
   <field name='YieldRedemptionDate' required='N'/>
   <field name='YieldRedemptionPrice' required='N'/>
   <field name='YieldRedemptionPriceType' required='N'/>
  </component>
  <component name='UnderlyingStipulations'>
   <group name='NoUnderlyingStips' required='N'>
    <field name='UnderlyingStipType' required='N'/>
    <field name='UnderlyingStipValue' required='N'/>
   </group>
  </component>
  <component name='NestedParties2'>
   <group name='NoNested2PartyIDs' required='N'>
    <field name='Nested2PartyID' required='N'/>
    <field name='Nested2PartyIDSource' required='N'/>
    <field name='Nested2PartyRole' required='N'/>
    <component name='NstdPtys2SubGrp' required='N'/>
   </group>
  </component>
  <component name='NestedParties3'>
   <group name='NoNested3PartyIDs' required='N'>
    <field name='Nested3PartyID' required='N'/>
    <field name='Nested3PartyIDSource' required='N'/>
    <field name='Nested3PartyRole' required='N'/>
    <component name='NstdPtys3SubGrp' required='N'/>
   </group>
  </component>
  <component name='AffectedOrdGrp'>
   <group name='NoAffectedOrders' required='N'>
    <field name='OrigClOrdID' required='N'/>
    <field name='AffectedOrderID' required='N'/>
    <field name='AffectedSecondaryOrderID' required='N'/>
   </group>
  </component>
  <component name='AllocAckGrp'>
   <group name='NoAllocs' required='N'>
    <field name='AllocAccount' required='N'/>
    <field name='AllocAcctIDSource' required='N'/>
    <field name='AllocPrice' required='N'/>
    <field name='AllocPositionEffect' required='N'/>
    <field name='IndividualAllocID' required='N'/>
    <field name='IndividualAllocRejCode' required='N'/>
    <component name='NestedParties' required='N'/>
    <field name='AllocText' required='N'/>
    <field name='EncodedAllocTextLen' required='N'/>
    <field name='EncodedAllocText' required='N'/>
    <field name='SecondaryIndividualAllocID' required='N'/>
    <field name='AllocCustomerCapacity' required='N'/>
    <field name='IndividualAllocType' required='N'/>
    <field name='AllocQty' required='N'/>
   </group>
  </component>
  <component name='AllocGrp'>
   <group name='NoAllocs' required='N'>
    <field name='AllocAccount' required='N'/>
    <field name='AllocAcctIDSource' required='N'/>
    <field name='MatchStatus' required='N'/>
    <field name='AllocPrice' required='N'/>
    <field name='AllocQty' required='N'/>
    <field name='IndividualAllocID' required='N'/>
    <field name='ProcessCode' required='N'/>
    <field name='SecondaryIndividualAllocID' required='N'/>
    <field name='AllocMethod' required='N'/>
    <field name='AllocCustomerCapacity' required='N'/>
    <field name='AllocPositionEffect' required='N'/>
    <field name='IndividualAllocType' required='N'/>
    <component name='NestedParties' required='N'/>
    <field name='NotifyBrokerOfCredit' required='N'/>
    <field name='AllocHandlInst' required='N'/>
    <field name='AllocText' required='N'/>
    <field name='EncodedAllocTextLen' required='N'/>
    <field name='EncodedAllocText' required='N'/>
    <component name='CommissionData' required='N'/>
    <field name='AllocAvgPx' required='N'/>
    <field name='AllocNetMoney' required='N'/>
    <field name='SettlCurrAmt' required='N'/>
    <field name='AllocSettlCurrAmt' required='N'/>
    <field name='SettlCurrency' required='N'/>
    <field name='AllocSettlCurrency' required='N'/>
    <field name='SettlCurrFxRate' required='N'/>
    <field name='SettlCurrFxRateCalc' required='N'/>
    <field name='AllocAccruedInterestAmt' required='N'/>
    <field name='AllocInterestAtMaturity' required='N'/>
    <component name='MiscFeesGrp' required='N'/>
    <component name='ClrInstGrp' required='N'/>
    <field name='ClearingFeeIndicator' required='N'/>
    <field name='AllocSettlInstType' required='N'/>
    <component name='SettlInstructionsData' required='N'/>
   </group>
  </component>
  <component name='BidCompReqGrp'>
   <group name='NoBidComponents' required='N'>
    <field name='ListID' required='N'/>
    <field name='Side' required='N'/>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
    <field name='NetGrossInd' required='N'/>
    <field name='SettlType' required='N'/>
    <field name='SettlDate' required='N'/>
    <field name='Account' required='N'/>
    <field name='AcctIDSource' required='N'/>
   </group>
  </component>
  <component name='BidCompRspGrp'>
   <group name='NoBidComponents' required='Y'>
    <component name='CommissionData' required='Y'/>
    <field name='ListID' required='N'/>
    <field name='Country' required='N'/>
    <field name='Side' required='N'/>
    <field name='Price' required='N'/>
    <field name='PriceType' required='N'/>
    <field name='FairValue' required='N'/>
    <field name='NetGrossInd' required='N'/>
    <field name='SettlType' required='N'/>
    <field name='SettlDate' required='N'/>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
    <field name='Text' required='N'/>
    <field name='EncodedTextLen' required='N'/>
    <field name='EncodedText' required='N'/>
   </group>
  </component>
  <component name='BidDescReqGrp'>
   <group name='NoBidDescriptors' required='N'>
    <field name='BidDescriptorType' required='N'/>
    <field name='BidDescriptor' required='N'/>
    <field name='SideValueInd' required='N'/>
    <field name='LiquidityValue' required='N'/>
    <field name='LiquidityNumSecurities' required='N'/>
    <field name='LiquidityPctLow' required='N'/>
    <field name='LiquidityPctHigh' required='N'/>
    <field name='EFPTrackingError' required='N'/>
    <field name='FairValue' required='N'/>
    <field name='OutsideIndexPct' required='N'/>
    <field name='ValueOfFutures' required='N'/>
   </group>
  </component>
  <component name='ClrInstGrp'>
   <group name='NoClearingInstructions' required='N'>
    <field name='ClearingInstruction' required='N'/>
   </group>
  </component>
  <component name='CollInqQualGrp'>
   <group name='NoCollInquiryQualifier' required='N'>
    <field name='CollInquiryQualifier' required='N'/>
   </group>
  </component>
  <component name='CompIDReqGrp'>
   <group name='NoCompIDs' required='N'>
    <field name='RefCompID' required='N'/>
    <field name='RefSubID' required='N'/>
    <field name='LocationID' required='N'/>
    <field name='DeskID' required='N'/>
   </group>
  </component>
  <component name='CompIDStatGrp'>
   <group name='NoCompIDs' required='Y'>
    <field name='RefCompID' required='Y'/>
    <field name='RefSubID' required='N'/>
    <field name='LocationID' required='N'/>
    <field name='DeskID' required='N'/>
    <field name='StatusValue' required='Y'/>
    <field name='StatusText' required='N'/>
   </group>
  </component>
  <component name='ContAmtGrp'>
   <group name='NoContAmts' required='N'>
    <field name='ContAmtType' required='N'/>
    <field name='ContAmtValue' required='N'/>
    <field name='ContAmtCurr' required='N'/>
   </group>
  </component>
  <component name='ContraGrp'>
   <group name='NoContraBrokers' required='N'>
    <field name='ContraBroker' required='N'/>
    <field name='ContraTrader' required='N'/>
    <field name='ContraTradeQty' required='N'/>
    <field name='ContraTradeTime' required='N'/>
    <field name='ContraLegRefID' required='N'/>
   </group>
  </component>
  <component name='CpctyConfGrp'>
   <group name='NoCapacities' required='Y'>
    <field name='OrderCapacity' required='Y'/>
    <field name='OrderRestrictions' required='N'/>
    <field name='OrderCapacityQty' required='Y'/>
   </group>
  </component>
  <component name='ExecAllocGrp'>
   <group name='NoExecs' required='N'>
    <field name='LastQty' required='N'/>
    <field name='ExecID' required='N'/>
    <field name='SecondaryExecID' required='N'/>
    <field name='LastPx' required='N'/>
    <field name='LastParPx' required='N'/>
    <field name='LastCapacity' required='N'/>
    <field name='TradeID' required='N'/>
    <field name='FirmTradeID' required='N'/>
   </group>
  </component>
  <component name='ExecCollGrp'>
   <group name='NoExecs' required='N'>
    <field name='ExecID' required='N'/>
   </group>
  </component>
  <component name='ExecsGrp'>
   <group name='NoExecs' required='N'>
    <field name='ExecID' required='N'/>
   </group>
  </component>
  <component name='InstrmtGrp'>
   <group name='NoRelatedSym' required='N'>
    <component name='Instrument' required='N'/>
   </group>
  </component>
  <component name='InstrmtLegExecGrp'>
   <group name='NoLegs' required='N'>
    <component name='InstrumentLeg' required='N'/>
    <field name='LegQty' required='N'/>
    <field name='LegOrderQty' required='N'/>
    <field name='LegSwapType' required='N'/>
    <component name='LegStipulations' required='N'/>
    <field name='LegPositionEffect' required='N'/>
    <field name='LegCoveredOrUncovered' required='N'/>
    <component name='NestedParties' required='N'/>
    <field name='LegRefID' required='N'/>
    <field name='LegSettlType' required='N'/>
    <field name='LegSettlDate' required='N'/>
    <field name='LegLastPx' required='N'/>
    <field name='LegSettlCurrency' required='N'/>
    <field name='LegLastForwardPoints' required='N'/>
    <field name='LegCalculatedCcyLastQty' required='N'/>
    <field name='LegGrossTradeAmt' required='N'/>
   </group>
  </component>
  <component name='InstrmtLegGrp'>
   <group name='NoLegs' required='N'>
    <component name='InstrumentLeg' required='N'/>
   </group>
  </component>
  <component name='InstrmtLegIOIGrp'>
   <group name='NoLegs' required='N'>
    <component name='InstrumentLeg' required='N'/>
    <field name='LegIOIQty' required='N'/>
    <component name='LegStipulations' required='N'/>
   </group>
  </component>
  <component name='InstrmtLegSecListGrp'>
   <group name='NoLegs' required='N'>
    <component name='InstrumentLeg' required='N'/>
    <field name='LegSwapType' required='N'/>
    <field name='LegSettlType' required='N'/>
    <component name='LegStipulations' required='N'/>
    <component name='LegBenchmarkCurveData' required='N'/>
   </group>
  </component>
  <component name='InstrmtMDReqGrp'>
   <group name='NoRelatedSym' required='Y'>
    <component name='Instrument' required='Y'/>
    <component name='UndInstrmtGrp' required='N'/>
    <component name='InstrmtLegGrp' required='N'/>
    <field name='Currency' required='N'/>
    <field name='QuoteType' required='N'/>
    <field name='SettlType' required='N'/>
    <field name='SettlDate' required='N'/>
    <field name='MDEntrySize' required='N'/>
   </group>
  </component>
  <component name='InstrmtStrkPxGrp'>
   <group name='NoStrikes' required='Y'>
    <component name='Instrument' required='Y'/>
   </group>
  </component>
  <component name='IOIQualGrp'>
   <group name='NoIOIQualifiers' required='N'>
    <field name='IOIQualifier' required='N'/>
   </group>
  </component>
  <component name='LegOrdGrp'>
   <group name='NoLegs' required='Y'>
    <component name='InstrumentLeg' required='N'/>
    <field name='LegOptionRatio' required='N'/>
    <field name='LegQty' required='N'/>
    <field name='LegSwapType' required='N'/>
    <component name='LegStipulations' required='N'/>
    <component name='LegPreAllocGrp' required='N'/>
    <field name='LegPositionEffect' required='N'/>
    <field name='LegCoveredOrUncovered' required='N'/>
    <component name='NestedParties' required='N'/>
    <field name='LegRefID' required='N'/>
    <field name='LegPrice' required='N'/>
    <field name='LegSettlType' required='N'/>
    <field name='LegSettlDate' required='N'/>
    <field name='LegOrderQty' required='N'/>
   </group>
  </component>
  <component name='LegPreAllocGrp'>
   <group name='NoLegAllocs' required='N'>
    <field name='LegAllocAccount' required='N'/>
    <field name='LegIndividualAllocID' required='N'/>
    <component name='NestedParties2' required='N'/>
    <field name='LegAllocQty' required='N'/>
    <field name='LegAllocAcctIDSource' required='N'/>
    <field name='LegSettlCurrency' required='N'/>
   </group>
  </component>
  <component name='LegQuotGrp'>
   <group name='NoLegs' required='N'>
    <component name='InstrumentLeg' required='N'/>
    <field name='LegQty' required='N'/>
    <field name='LegOrderQty' required='N'/>
    <field name='LegSwapType' required='N'/>
    <field name='LegSettlType' required='N'/>
    <field name='LegSettlDate' required='N'/>
    <component name='LegStipulations' required='N'/>
    <component name='NestedParties' required='N'/>
    <field name='LegPriceType' required='N'/>
    <field name='LegBidPx' required='N'/>
    <field name='LegOfferPx' required='N'/>
    <component name='LegBenchmarkCurveData' required='N'/>
    <field name='LegRefID' required='N'/>
    <field name='LegBidForwardPoints' required='N'/>
    <field name='LegOfferForwardPoints' required='N'/>
   </group>
  </component>
  <component name='LegQuotStatGrp'>
   <group name='NoLegs' required='N'>
    <component name='InstrumentLeg' required='N'/>
    <field name='LegQty' required='N'/>
    <field name='LegOrderQty' required='N'/>
    <field name='LegSwapType' required='N'/>
    <field name='LegSettlType' required='N'/>
    <field name='LegSettlDate' required='N'/>
    <component name='LegStipulations' required='N'/>
    <component name='NestedParties' required='N'/>
   </group>
  </component>
  <component name='LinesOfTextGrp'>
   <group name='NoLinesOfText' required='Y'>
    <field name='Text' required='Y'/>
    <field name='EncodedTextLen' required='N'/>
    <field name='EncodedText' required='N'/>
   </group>
  </component>
  <component name='ListOrdGrp'>
   <group name='NoOrders' required='Y'>
    <field name='ClOrdID' required='Y'/>
    <field name='SecondaryClOrdID' required='N'/>
    <field name='ListSeqNo' required='Y'/>
    <field name='ClOrdLinkID' required='N'/>
    <field name='SettlInstMode' required='N'/>
    <component name='Parties' required='N'/>
    <field name='TradeOriginationDate' required='N'/>
    <field name='TradeDate' required='N'/>
    <field name='Account' required='N'/>
    <field name='AcctIDSource' required='N'/>
    <field name='AccountType' required='N'/>
    <field name='DayBookingInst' required='N'/>
    <field name='BookingUnit' required='N'/>
    <field name='AllocID' required='N'/>
    <field name='PreallocMethod' required='N'/>
    <component name='PreAllocGrp' required='N'/>
    <field name='SettlType' required='N'/>
    <field name='SettlDate' required='N'/>
    <field name='CashMargin' required='N'/>
    <field name='ClearingFeeIndicator' required='N'/>
    <field name='HandlInst' required='N'/>
    <field name='ExecInst' required='N'/>
    <field name='MinQty' required='N'/>
    <field name='MatchIncrement' required='N'/>
    <field name='MaxPriceLevels' required='N'/>
    <component name='DisplayInstruction' required='N'/>
    <field name='MaxFloor' required='N'/>
    <field name='ExDestination' required='N'/>
    <field name='ExDestinationIDSource' required='N'/>
    <component name='TrdgSesGrp' required='N'/>
    <field name='ProcessCode' required='N'/>
    <component name='Instrument' required='Y'/>
    <component name='UndInstrmtGrp' required='N'/>
    <field name='PrevClosePx' required='N'/>
    <field name='Side' required='Y'/>
    <field name='SideValueInd' required='N'/>
    <field name='LocateReqd' required='N'/>
    <field name='TransactTime' required='N'/>
    <component name='Stipulations' required='N'/>
    <field name='QtyType' required='N'/>
    <component name='OrderQtyData' required='Y'/>
    <field name='OrdType' required='N'/>
    <field name='PriceType' required='N'/>
    <field name='Price' required='N'/>
    <field name='PriceProtectionScope' required='N'/>
    <field name='StopPx' required='N'/>
    <component name='TriggeringInstruction' required='N'/>
    <component name='SpreadOrBenchmarkCurveData' required='N'/>
    <component name='YieldData' required='N'/>
    <field name='Currency' required='N'/>
    <field name='ComplianceID' required='N'/>
    <field name='SolicitedFlag' required='N'/>
    <field name='IOIID' required='N'/>
    <field name='QuoteID' required='N'/>
    <field name='RefOrderID' required='N'/>
    <field name='RefOrderIDSource' required='N'/>
    <field name='TimeInForce' required='N'/>
    <field name='EffectiveTime' required='N'/>
    <field name='ExpireDate' required='N'/>
    <field name='ExpireTime' required='N'/>
    <field name='GTBookingInst' required='N'/>
    <component name='CommissionData' required='N'/>
    <field name='OrderCapacity' required='N'/>
    <field name='OrderRestrictions' required='N'/>
    <field name='PreTradeAnonymity' required='N'/>
    <field name='CustOrderCapacity' required='N'/>
    <field name='ForexReq' required='N'/>
    <field name='SettlCurrency' required='N'/>
    <field name='BookingType' required='N'/>
    <field name='Text' required='N'/>
    <field name='EncodedTextLen' required='N'/>
    <field name='EncodedText' required='N'/>
    <field name='SettlDate2' required='N'/>
    <field name='OrderQty2' required='N'/>
    <field name='Price2' required='N'/>
    <field name='PositionEffect' required='N'/>
    <field name='CoveredOrUncovered' required='N'/>
    <field name='MaxShow' required='N'/>
    <component name='PegInstructions' required='N'/>
    <component name='DiscretionInstructions' required='N'/>
    <field name='TargetStrategy' required='N'/>
    <component name='StrategyParametersGrp' required='N'/>
    <field name='TargetStrategyParameters' required='N'/>
    <field name='ParticipationRate' required='N'/>
    <field name='Designation' required='N'/>
   </group>
  </component>
  <component name='MDFullGrp'>
   <group name='NoMDEntries' required='Y'>
    <field name='MDEntryType' required='Y'/>
    <field name='MDEntryID' required='N'/>
    <field name='MDEntryPx' required='N'/>
    <field name='OrdType' required='N'/>
    <field name='Currency' required='N'/>
    <field name='MDEntrySize' required='N'/>
    <field name='MDEntryDate' required='N'/>
    <field name='MDEntryTime' required='N'/>
    <field name='TickDirection' required='N'/>
    <field name='MDMkt' required='N'/>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
    <field name='QuoteCondition' required='N'/>
    <field name='TradeCondition' required='N'/>
    <field name='MDEntryOriginator' required='N'/>
    <field name='LocationID' required='N'/>
    <field name='DeskID' required='N'/>
    <field name='OpenCloseSettlFlag' required='N'/>
    <field name='TimeInForce' required='N'/>
    <field name='ExpireDate' required='N'/>
    <field name='ExpireTime' required='N'/>
    <field name='MinQty' required='N'/>
    <field name='ExecInst' required='N'/>
    <field name='SellerDays' required='N'/>
    <field name='OrderID' required='N'/>
    <field name='SecondaryOrderID' required='N'/>
    <field name='QuoteEntryID' required='N'/>
    <field name='MDEntryBuyer' required='N'/>
    <field name='MDEntrySeller' required='N'/>
    <field name='NumberOfOrders' required='N'/>
    <field name='MDEntryPositionNo' required='N'/>
    <field name='Scope' required='N'/>
    <field name='PriceDelta' required='N'/>
    <field name='Text' required='N'/>
    <field name='EncodedTextLen' required='N'/>
    <field name='EncodedText' required='N'/>
    <field name='MDPriceLevel' required='N'/>
    <field name='OrderCapacity' required='N'/>
    <field name='MDOriginType' required='N'/>
    <field name='HighPx' required='N'/>
    <field name='LowPx' required='N'/>
    <field name='TradeVolume' required='N'/>
    <field name='SettlType' required='N'/>
    <field name='SettlDate' required='N'/>
    <field name='MDQuoteType' required='N'/>
    <field name='RptSeq' required='N'/>
    <field name='DealingCapacity' required='N'/>
    <field name='MDEntrySpotRate' required='N'/>
    <field name='MDEntryForwardPoints' required='N'/>
    <component name='Parties' required='N'/>
   </group>
  </component>
  <component name='MDIncGrp'>
   <group name='NoMDEntries' required='Y'>
    <field name='MDUpdateAction' required='Y'/>
    <field name='DeleteReason' required='N'/>
    <field name='MDEntryType' required='N'/>
    <field name='MDEntryID' required='N'/>
    <field name='MDEntryRefID' required='N'/>
    <component name='Instrument' required='N'/>
    <component name='UndInstrmtGrp' required='N'/>
    <component name='InstrmtLegGrp' required='N'/>
    <field name='FinancialStatus' required='N'/>
    <field name='CorporateAction' required='N'/>
    <field name='MDEntryPx' required='N'/>
    <field name='OrdType' required='N'/>
    <field name='Currency' required='N'/>
    <field name='MDEntrySize' required='N'/>
    <field name='MDEntryDate' required='N'/>
    <field name='MDEntryTime' required='N'/>
    <field name='TickDirection' required='N'/>
    <field name='MDMkt' required='N'/>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
    <field name='QuoteCondition' required='N'/>
    <field name='TradeCondition' required='N'/>
    <field name='MDEntryOriginator' required='N'/>
    <field name='LocationID' required='N'/>
    <field name='DeskID' required='N'/>
    <field name='OpenCloseSettlFlag' required='N'/>
    <field name='TimeInForce' required='N'/>
    <field name='ExpireDate' required='N'/>
    <field name='ExpireTime' required='N'/>
    <field name='MinQty' required='N'/>
    <field name='ExecInst' required='N'/>
    <field name='SellerDays' required='N'/>
    <field name='OrderID' required='N'/>
    <field name='SecondaryOrderID' required='N'/>
    <field name='QuoteEntryID' required='N'/>
    <field name='MDEntryBuyer' required='N'/>
    <field name='MDEntrySeller' required='N'/>
    <field name='NumberOfOrders' required='N'/>
    <field name='MDEntryPositionNo' required='N'/>
    <field name='Scope' required='N'/>
    <field name='PriceDelta' required='N'/>
    <field name='NetChgPrevDay' required='N'/>
    <field name='Text' required='N'/>
    <field name='EncodedTextLen' required='N'/>
    <field name='EncodedText' required='N'/>
    <field name='MDPriceLevel' required='N'/>
    <field name='OrderCapacity' required='N'/>
    <field name='MDOriginType' required='N'/>
    <field name='HighPx' required='N'/>
    <field name='LowPx' required='N'/>
    <field name='TradeVolume' required='N'/>
    <field name='SettlType' required='N'/>
    <field name='SettlDate' required='N'/>
    <field name='MDQuoteType' required='N'/>
    <field name='RptSeq' required='N'/>
    <field name='DealingCapacity' required='N'/>
    <field name='MDEntrySpotRate' required='N'/>
    <field name='MDEntryForwardPoints' required='N'/>
    <component name='Parties' required='N'/>
   </group>
  </component>
  <component name='MDReqGrp'>
   <group name='NoMDEntryTypes' required='Y'>
    <field name='MDEntryType' required='Y'/>
   </group>
  </component>
  <component name='MDRjctGrp'>
   <group name='NoAltMDSource' required='N'>
    <field name='AltMDSourceID' required='N'/>
   </group>
  </component>
  <component name='MiscFeesGrp'>
   <group name='NoMiscFees' required='N'>
    <field name='MiscFeeAmt' required='N'/>
    <field name='MiscFeeCurr' required='N'/>
    <field name='MiscFeeType' required='N'/>
    <field name='MiscFeeBasis' required='N'/>
   </group>
  </component>
  <component name='OrdAllocGrp'>
   <group name='NoOrders' required='N'>
    <field name='ClOrdID' required='N'/>
    <field name='OrderID' required='N'/>
    <field name='SecondaryOrderID' required='N'/>
    <field name='SecondaryClOrdID' required='N'/>
    <field name='ListID' required='N'/>
    <component name='NestedParties2' required='N'/>
    <field name='OrderQty' required='N'/>
    <field name='OrderAvgPx' required='N'/>
    <field name='OrderBookingQty' required='N'/>
   </group>
  </component>
  <component name='OrdListStatGrp'>
   <group name='NoOrders' required='Y'>
    <field name='ClOrdID' required='Y'/>
    <field name='SecondaryClOrdID' required='N'/>
    <field name='CumQty' required='Y'/>
    <field name='OrdStatus' required='Y'/>
    <field name='WorkingIndicator' required='N'/>
    <field name='LeavesQty' required='Y'/>
    <field name='CxlQty' required='Y'/>
    <field name='AvgPx' required='Y'/>
    <field name='OrdRejReason' required='N'/>
    <field name='Text' required='N'/>
    <field name='EncodedTextLen' required='N'/>
    <field name='EncodedText' required='N'/>
   </group>
  </component>
  <component name='PosUndInstrmtGrp'>
   <group name='NoUnderlyings' required='N'>
    <component name='UnderlyingInstrument' required='N'/>
    <field name='UnderlyingSettlPrice' required='N'/>
    <field name='UnderlyingSettlPriceType' required='N'/>
    <field name='UnderlyingDeliveryAmount' required='N'/>
    <component name='UnderlyingAmount' required='N'/>
   </group>
  </component>
  <component name='PreAllocGrp'>
   <group name='NoAllocs' required='N'>
    <field name='AllocAccount' required='N'/>
    <field name='AllocAcctIDSource' required='N'/>
    <field name='AllocSettlCurrency' required='N'/>
    <field name='IndividualAllocID' required='N'/>
    <component name='NestedParties' required='N'/>
    <field name='AllocQty' required='N'/>
   </group>
  </component>
  <component name='PreAllocMlegGrp'>
   <group name='NoAllocs' required='N'>
    <field name='AllocAccount' required='N'/>
    <field name='AllocAcctIDSource' required='N'/>
    <field name='AllocSettlCurrency' required='N'/>
    <field name='IndividualAllocID' required='N'/>
    <component name='NestedParties3' required='N'/>
    <field name='AllocQty' required='N'/>
   </group>
  </component>
  <component name='QuotCxlEntriesGrp'>
   <group name='NoQuoteEntries' required='N'>
    <component name='Instrument' required='N'/>
    <component name='FinancingDetails' required='N'/>
    <component name='UndInstrmtGrp' required='N'/>
    <component name='InstrmtLegGrp' required='N'/>
   </group>
  </component>
  <component name='QuotEntryAckGrp'>
   <group name='NoQuoteEntries' required='N'>
    <field name='QuoteEntryID' required='N'/>
    <component name='Instrument' required='N'/>
    <component name='InstrmtLegGrp' required='N'/>
    <field name='BidPx' required='N'/>
    <field name='OfferPx' required='N'/>
    <field name='BidSize' required='N'/>
    <field name='OfferSize' required='N'/>
    <field name='ValidUntilTime' required='N'/>
    <field name='BidSpotRate' required='N'/>
    <field name='OfferSpotRate' required='N'/>
    <field name='BidForwardPoints' required='N'/>
    <field name='OfferForwardPoints' required='N'/>
    <field name='MidPx' required='N'/>
    <field name='BidYield' required='N'/>
    <field name='MidYield' required='N'/>
    <field name='OfferYield' required='N'/>
    <field name='TransactTime' required='N'/>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
    <field name='SettlDate' required='N'/>
    <field name='OrdType' required='N'/>
    <field name='SettlDate2' required='N'/>
    <field name='OrderQty2' required='N'/>
    <field name='BidForwardPoints2' required='N'/>
    <field name='OfferForwardPoints2' required='N'/>
    <field name='Currency' required='N'/>
    <field name='QuoteEntryRejectReason' required='N'/>
   </group>
  </component>
  <component name='QuotEntryGrp'>
   <group name='NoQuoteEntries' required='Y'>
    <field name='QuoteEntryID' required='Y'/>
    <component name='Instrument' required='N'/>
    <component name='InstrmtLegGrp' required='N'/>
    <field name='BidPx' required='N'/>
    <field name='OfferPx' required='N'/>
    <field name='BidSize' required='N'/>
    <field name='OfferSize' required='N'/>
    <field name='ValidUntilTime' required='N'/>
    <field name='BidSpotRate' required='N'/>
    <field name='OfferSpotRate' required='N'/>
    <field name='BidForwardPoints' required='N'/>
    <field name='OfferForwardPoints' required='N'/>
    <field name='MidPx' required='N'/>
    <field name='BidYield' required='N'/>
    <field name='MidYield' required='N'/>
    <field name='OfferYield' required='N'/>
    <field name='TransactTime' required='N'/>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
    <field name='SettlDate' required='N'/>
    <field name='OrdType' required='N'/>
    <field name='SettlDate2' required='N'/>
    <field name='OrderQty2' required='N'/>
    <field name='BidForwardPoints2' required='N'/>
    <field name='OfferForwardPoints2' required='N'/>
    <field name='Currency' required='N'/>
   </group>
  </component>
  <component name='QuotQualGrp'>
   <group name='NoQuoteQualifiers' required='N'>
    <field name='QuoteQualifier' required='N'/>
   </group>
  </component>
  <component name='QuotReqGrp'>
   <group name='NoRelatedSym' required='Y'>
    <component name='Instrument' required='Y'/>
    <component name='FinancingDetails' required='N'/>
    <component name='UndInstrmtGrp' required='N'/>
    <field name='PrevClosePx' required='N'/>
    <field name='QuoteRequestType' required='N'/>
    <field name='QuoteType' required='N'/>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
    <field name='TradeOriginationDate' required='N'/>
    <field name='Side' required='N'/>
    <field name='QtyType' required='N'/>
    <component name='OrderQtyData' required='N'/>
    <field name='SettlType' required='N'/>
    <field name='SettlDate' required='N'/>
    <field name='SettlDate2' required='N'/>
    <field name='OrderQty2' required='N'/>
    <field name='Currency' required='N'/>
    <component name='Stipulations' required='N'/>
    <field name='Account' required='N'/>
    <field name='AcctIDSource' required='N'/>
    <field name='AccountType' required='N'/>
    <component name='QuotReqLegsGrp' required='N'/>
    <component name='QuotQualGrp' required='N'/>
    <field name='QuotePriceType' required='N'/>
    <field name='OrdType' required='N'/>
    <field name='ValidUntilTime' required='N'/>
    <field name='ExpireTime' required='N'/>
    <field name='TransactTime' required='N'/>
    <component name='SpreadOrBenchmarkCurveData' required='N'/>
    <field name='PriceType' required='N'/>
    <field name='Price' required='N'/>
    <field name='Price2' required='N'/>
    <component name='YieldData' required='N'/>
    <component name='Parties' required='N'/>
   </group>
  </component>
  <component name='QuotReqLegsGrp'>
   <group name='NoLegs' required='N'>
    <component name='InstrumentLeg' required='N'/>
    <field name='LegOptionRatio' required='N'/>
    <field name='LegPrice' required='N'/>
    <field name='LegQty' required='N'/>
    <field name='LegOrderQty' required='N'/>
    <field name='LegSwapType' required='N'/>
    <field name='LegSettlType' required='N'/>
    <field name='LegSettlDate' required='N'/>
    <component name='LegStipulations' required='N'/>
    <component name='NestedParties' required='N'/>
    <component name='LegBenchmarkCurveData' required='N'/>
    <field name='LegRefID' required='N'/>
   </group>
  </component>
  <component name='QuotReqRjctGrp'>
   <group name='NoRelatedSym' required='Y'>
    <component name='Instrument' required='Y'/>
    <component name='FinancingDetails' required='N'/>
    <component name='UndInstrmtGrp' required='N'/>
    <field name='PrevClosePx' required='N'/>
    <field name='QuoteRequestType' required='N'/>
    <field name='QuoteType' required='N'/>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
    <field name='TradeOriginationDate' required='N'/>
    <field name='Side' required='N'/>
    <field name='QtyType' required='N'/>
    <component name='OrderQtyData' required='N'/>
    <field name='SettlType' required='N'/>
    <field name='SettlDate' required='N'/>
    <field name='SettlDate2' required='N'/>
    <field name='OrderQty2' required='N'/>
    <field name='Currency' required='N'/>
    <component name='Stipulations' required='N'/>
    <field name='Account' required='N'/>
    <field name='AcctIDSource' required='N'/>
    <field name='AccountType' required='N'/>
    <component name='QuotReqLegsGrp' required='N'/>
    <component name='QuotQualGrp' required='N'/>
    <field name='QuotePriceType' required='N'/>
    <field name='OrdType' required='N'/>
    <field name='ExpireTime' required='N'/>
    <field name='TransactTime' required='N'/>
    <component name='SpreadOrBenchmarkCurveData' required='N'/>
    <field name='PriceType' required='N'/>
    <field name='Price' required='N'/>
    <field name='Price2' required='N'/>
    <component name='YieldData' required='N'/>
    <component name='Parties' required='N'/>
   </group>
  </component>
  <component name='QuotSetAckGrp'>
   <group name='NoQuoteSets' required='N'>
    <field name='QuoteSetID' required='N'/>
    <component name='UnderlyingInstrument' required='N'/>
    <field name='TotNoQuoteEntries' required='N'/>
    <field name='LastFragment' required='N'/>
    <component name='QuotEntryAckGrp' required='N'/>
   </group>
  </component>
  <component name='QuotSetGrp'>
   <group name='NoQuoteSets' required='Y'>
    <field name='QuoteSetID' required='Y'/>
    <component name='UnderlyingInstrument' required='N'/>
    <field name='QuoteSetValidUntilTime' required='N'/>
    <field name='TotNoQuoteEntries' required='Y'/>
    <field name='LastFragment' required='N'/>
    <component name='QuotEntryGrp' required='Y'/>
   </group>
  </component>
  <component name='RelSymDerivSecGrp'>
   <group name='NoRelatedSym' required='N'>
    <component name='Instrument' required='N'/>
    <field name='Currency' required='N'/>
    <field name='ExpirationCycle' required='N'/>
    <component name='InstrumentExtension' required='N'/>
    <component name='InstrmtLegGrp' required='N'/>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
    <field name='Text' required='N'/>
    <field name='EncodedTextLen' required='N'/>
    <field name='EncodedText' required='N'/>
   </group>
  </component>
  <component name='RFQReqGrp'>
   <group name='NoRelatedSym' required='Y'>
    <component name='Instrument' required='Y'/>
    <component name='UndInstrmtGrp' required='N'/>
    <component name='InstrmtLegGrp' required='N'/>
    <field name='PrevClosePx' required='N'/>
    <field name='QuoteRequestType' required='N'/>
    <field name='QuoteType' required='N'/>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
   </group>
  </component>
  <component name='RgstDistInstGrp'>
   <group name='NoDistribInsts' required='N'>
    <field name='DistribPaymentMethod' required='N'/>
    <field name='DistribPercentage' required='N'/>
    <field name='CashDistribCurr' required='N'/>
    <field name='CashDistribAgentName' required='N'/>
    <field name='CashDistribAgentCode' required='N'/>
    <field name='CashDistribAgentAcctNumber' required='N'/>
    <field name='CashDistribPayRef' required='N'/>
    <field name='CashDistribAgentAcctName' required='N'/>
   </group>
  </component>
  <component name='RgstDtlsGrp'>
   <group name='NoRegistDtls' required='N'>
    <field name='RegistDtls' required='N'/>
    <field name='RegistEmail' required='N'/>
    <field name='MailingDtls' required='N'/>
    <field name='MailingInst' required='N'/>
    <component name='NestedParties' required='N'/>
    <field name='OwnerType' required='N'/>
    <field name='DateOfBirth' required='N'/>
    <field name='InvestorCountryOfResidence' required='N'/>
   </group>
  </component>
  <component name='RoutingGrp'>
   <group name='NoRoutingIDs' required='N'>
    <field name='RoutingType' required='N'/>
    <field name='RoutingID' required='N'/>
   </group>
  </component>
  <component name='SecListGrp'>
   <group name='NoRelatedSym' required='N'>
    <component name='Instrument' required='N'/>
    <component name='InstrumentExtension' required='N'/>
    <component name='FinancingDetails' required='N'/>
    <component name='UndInstrmtGrp' required='N'/>
    <field name='Currency' required='N'/>
    <component name='Stipulations' required='N'/>
    <component name='InstrmtLegSecListGrp' required='N'/>
    <component name='SpreadOrBenchmarkCurveData' required='N'/>
    <component name='YieldData' required='N'/>
    <field name='RoundLot' required='N'/>
    <field name='MinTradeVol' required='N'/>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
    <field name='ExpirationCycle' required='N'/>
    <field name='Text' required='N'/>
    <field name='EncodedTextLen' required='N'/>
    <field name='EncodedText' required='N'/>
   </group>
  </component>
  <component name='SecTypesGrp'>
   <group name='NoSecurityTypes' required='N'>
    <field name='SecurityType' required='N'/>
    <field name='SecuritySubType' required='N'/>
    <field name='Product' required='N'/>
    <field name='CFICode' required='N'/>
   </group>
  </component>
  <component name='SettlInstGrp'>
   <group name='NoSettlInst' required='N'>
    <field name='SettlInstID' required='N'/>
    <field name='SettlInstTransType' required='N'/>
    <field name='SettlInstRefID' required='N'/>
    <component name='Parties' required='N'/>
    <field name='Side' required='N'/>
    <field name='Product' required='N'/>
    <field name='SecurityType' required='N'/>
    <field name='CFICode' required='N'/>
    <field name='SettlCurrency' required='N'/>
    <field name='EffectiveTime' required='N'/>
    <field name='ExpireTime' required='N'/>
    <field name='LastUpdateTime' required='N'/>
    <component name='SettlInstructionsData' required='N'/>
    <field name='PaymentMethod' required='N'/>
    <field name='PaymentRef' required='N'/>
    <field name='CardHolderName' required='N'/>
    <field name='CardNumber' required='N'/>
    <field name='CardStartDate' required='N'/>
    <field name='CardExpDate' required='N'/>
    <field name='CardIssNum' required='N'/>
    <field name='PaymentDate' required='N'/>
    <field name='PaymentRemitterID' required='N'/>
   </group>
  </component>
  <component name='SideCrossOrdCxlGrp'>
   <group name='NoSides' required='Y'>
    <field name='Side' required='Y'/>
    <field name='OrigClOrdID' required='Y'/>
    <field name='ClOrdID' required='Y'/>
    <field name='SecondaryClOrdID' required='N'/>
    <field name='ClOrdLinkID' required='N'/>
    <field name='OrigOrdModTime' required='N'/>
    <component name='Parties' required='N'/>
    <field name='TradeOriginationDate' required='N'/>
    <field name='TradeDate' required='N'/>
    <component name='OrderQtyData' required='Y'/>
    <field name='ComplianceID' required='N'/>
    <field name='Text' required='N'/>
    <field name='EncodedTextLen' required='N'/>
    <field name='EncodedText' required='N'/>
   </group>
  </component>
  <component name='SideCrossOrdModGrp'>
   <group name='NoSides' required='Y'>
    <field name='Side' required='Y'/>
    <field name='ClOrdID' required='Y'/>
    <field name='SecondaryClOrdID' required='N'/>
    <field name='ClOrdLinkID' required='N'/>
    <component name='Parties' required='N'/>
    <field name='TradeOriginationDate' required='N'/>
    <field name='TradeDate' required='N'/>
    <field name='Account' required='N'/>
    <field name='AcctIDSource' required='N'/>
    <field name='AccountType' required='N'/>
    <field name='DayBookingInst' required='N'/>
    <field name='BookingUnit' required='N'/>
    <field name='PreallocMethod' required='N'/>
    <field name='AllocID' required='N'/>
    <component name='PreAllocGrp' required='N'/>
    <field name='QtyType' required='N'/>
    <component name='OrderQtyData' required='Y'/>
    <component name='CommissionData' required='N'/>
    <field name='OrderCapacity' required='N'/>
    <field name='OrderRestrictions' required='N'/>
    <field name='PreTradeAnonymity' required='N'/>
    <field name='CustOrderCapacity' required='N'/>
    <field name='ForexReq' required='N'/>
    <field name='SettlCurrency' required='N'/>
    <field name='BookingType' required='N'/>
    <field name='Text' required='N'/>
    <field name='EncodedTextLen' required='N'/>
    <field name='EncodedText' required='N'/>
    <field name='PositionEffect' required='N'/>
    <field name='CoveredOrUncovered' required='N'/>
    <field name='CashMargin' required='N'/>
    <field name='ClearingFeeIndicator' required='N'/>
    <field name='SolicitedFlag' required='N'/>
    <field name='SideComplianceID' required='N'/>
    <field name='SideTimeInForce' required='N'/>
   </group>
  </component>
  <component name='TrdAllocGrp'>
   <group name='NoAllocs' required='N'>
    <field name='AllocAccount' required='N'/>
    <field name='AllocAcctIDSource' required='N'/>
    <field name='AllocSettlCurrency' required='N'/>
    <field name='IndividualAllocID' required='N'/>
    <component name='NestedParties2' required='N'/>
    <field name='AllocQty' required='N'/>
    <field name='AllocCustomerCapacity' required='N'/>
    <field name='AllocMethod' required='N'/>
    <field name='SecondaryIndividualAllocID' required='N'/>
    <field name='AllocClearingFeeIndicator' required='N'/>
   </group>
  </component>
  <component name='TrdCapRptSideGrp'>
   <group name='NoSides' required='Y'>
    <field name='Side' required='Y'/>
    <field name='OrderID' required='N'/>
    <field name='SecondaryOrderID' required='N'/>
    <field name='ClOrdID' required='N'/>
    <field name='ExecRefID' required='N'/>
    <field name='SecondaryClOrdID' required='N'/>
    <field name='ListID' required='N'/>
    <field name='SideQty' required='N'/>
    <field name='SideTradeReportID' required='N'/>
    <field name='SideFillStationCd' required='N'/>
    <field name='SideReasonCd' required='N'/>
    <field name='RptSeq' required='N'/>
    <field name='SideTrdSubTyp' required='N'/>
    <component name='Parties' required='N'/>
    <field name='Account' required='N'/>
    <field name='AcctIDSource' required='N'/>
    <field name='AccountType' required='N'/>
    <field name='ProcessCode' required='N'/>
    <field name='LotType' required='N'/>
    <field name='OddLot' required='N'/>
    <component name='ClrInstGrp' required='N'/>
    <field name='TradeInputSource' required='N'/>
    <field name='TradeInputDevice' required='N'/>
    <field name='OrderInputDevice' required='N'/>
    <field name='Currency' required='N'/>
    <field name='ComplianceID' required='N'/>
    <field name='SolicitedFlag' required='N'/>
    <field name='OrderCapacity' required='N'/>
    <field name='OrderRestrictions' required='N'/>
    <field name='CustOrderCapacity' required='N'/>
    <field name='OrdType' required='N'/>
    <field name='ExecInst' required='N'/>
    <field name='TransBkdTime' required='N'/>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
    <field name='TimeBracket' required='N'/>
    <component name='CommissionData' required='N'/>
    <field name='NumDaysInterest' required='N'/>
    <field name='ExDate' required='N'/>
    <field name='AccruedInterestRate' required='N'/>
    <field name='AccruedInterestAmt' required='N'/>
    <field name='InterestAtMaturity' required='N'/>
    <field name='EndAccruedInterestAmt' required='N'/>
    <field name='StartCash' required='N'/>
    <field name='EndCash' required='N'/>
    <field name='Concession' required='N'/>
    <field name='TotalTakedown' required='N'/>
    <field name='NetMoney' required='N'/>
    <field name='SettlCurrAmt' required='N'/>
    <field name='SettlCurrency' required='N'/>
    <field name='SettlCurrFxRate' required='N'/>
    <field name='SettlCurrFxRateCalc' required='N'/>
    <field name='PositionEffect' required='N'/>
    <field name='Text' required='N'/>
    <field name='EncodedTextLen' required='N'/>
    <field name='EncodedText' required='N'/>
    <field name='SideMultiLegReportingType' required='N'/>
    <component name='ContAmtGrp' required='N'/>
    <component name='Stipulations' required='N'/>
    <component name='MiscFeesGrp' required='N'/>
    <field name='ExchangeRule' required='N'/>
    <field name='TradeAllocIndicator' required='N'/>
    <field name='PreallocMethod' required='N'/>
    <field name='AllocID' required='N'/>
    <component name='TrdAllocGrp' required='N'/>
    <component name='SideTrdRegTS' required='N'/>
    <field name='SideGrossTradeAmt' required='N'/>
    <field name='AggressorIndicator' required='N'/>
    <field name='ExchangeSpecialInstructions' required='N'/>
   </group>
  </component>
  <component name='TrdCollGrp'>
   <group name='NoTrades' required='N'>
    <field name='TradeReportID' required='N'/>
    <field name='SecondaryTradeReportID' required='N'/>
   </group>
  </component>
  <component name='TrdInstrmtLegGrp'>
   <group name='NoLegs' required='N'>
    <component name='InstrumentLeg' required='N'/>
    <field name='LegQty' required='N'/>
    <field name='LegSwapType' required='N'/>
    <field name='LegReportID' required='N'/>
    <component name='LegStipulations' required='N'/>
    <field name='LegPositionEffect' required='N'/>
    <field name='LegCoveredOrUncovered' required='N'/>
    <component name='NestedParties' required='N'/>
    <field name='LegRefID' required='N'/>
    <field name='LegPrice' required='N'/>
    <field name='LegSettlType' required='N'/>
    <field name='LegSettlDate' required='N'/>
    <field name='LegLastPx' required='N'/>
    <field name='LegSettlCurrency' required='N'/>
    <field name='LegLastForwardPoints' required='N'/>
    <field name='LegCalculatedCcyLastQty' required='N'/>
    <field name='LegGrossTradeAmt' required='N'/>
   </group>
  </component>
  <component name='TrdgSesGrp'>
   <group name='NoTradingSessions' required='N'>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
   </group>
  </component>
  <component name='UndInstrmtCollGrp'>
   <group name='NoUnderlyings' required='N'>
    <component name='UnderlyingInstrument' required='N'/>
    <field name='CollAction' required='N'/>
   </group>
  </component>
  <component name='UndInstrmtGrp'>
   <group name='NoUnderlyings' required='N'>
    <component name='UnderlyingInstrument' required='N'/>
   </group>
  </component>
  <component name='UndInstrmtStrkPxGrp'>
   <group name='NoUnderlyings' required='N'>
    <component name='UnderlyingInstrument' required='N'/>
    <field name='PrevClosePx' required='N'/>
    <field name='ClOrdID' required='N'/>
    <field name='SecondaryClOrdID' required='N'/>
    <field name='Side' required='N'/>
    <field name='Price' required='Y'/>
    <field name='Currency' required='N'/>
    <field name='Text' required='N'/>
    <field name='EncodedTextLen' required='N'/>
    <field name='EncodedText' required='N'/>
   </group>
  </component>
  <component name='TrdCapDtGrp'>
   <group name='NoDates' required='N'>
    <field name='TradeDate' required='N'/>
    <field name='LastUpdateTime' required='N'/>
    <field name='TransactTime' required='N'/>
   </group>
  </component>
  <component name='EvntGrp'>
   <group name='NoEvents' required='N'>
    <field name='EventType' required='N'/>
    <field name='EventDate' required='N'/>
    <field name='EventPx' required='N'/>
    <field name='EventText' required='N'/>
   </group>
  </component>
  <component name='SecAltIDGrp'>
   <group name='NoSecurityAltID' required='N'>
    <field name='SecurityAltID' required='N'/>
    <field name='SecurityAltIDSource' required='N'/>
   </group>
  </component>
  <component name='LegSecAltIDGrp'>
   <group name='NoLegSecurityAltID' required='N'>
    <field name='LegSecurityAltID' required='N'/>
    <field name='LegSecurityAltIDSource' required='N'/>
   </group>
  </component>
  <component name='UndSecAltIDGrp'>
   <group name='NoUnderlyingSecurityAltID' required='N'>
    <field name='UnderlyingSecurityAltID' required='N'/>
    <field name='UnderlyingSecurityAltIDSource' required='N'/>
   </group>
  </component>
  <component name='AttrbGrp'>
   <group name='NoInstrAttrib' required='N'>
    <field name='InstrAttribType' required='N'/>
    <field name='InstrAttribValue' required='N'/>
   </group>
  </component>
  <component name='DlvyInstGrp'>
   <group name='NoDlvyInst' required='N'>
    <field name='SettlInstSource' required='N'/>
    <field name='DlvyInstType' required='N'/>
    <component name='SettlParties' required='N'/>
   </group>
  </component>
  <component name='SettlPtysSubGrp'>
   <group name='NoSettlPartySubIDs' required='N'>
    <field name='SettlPartySubID' required='N'/>
    <field name='SettlPartySubIDType' required='N'/>
   </group>
  </component>
  <component name='PtysSubGrp'>
   <group name='NoPartySubIDs' required='N'>
    <field name='PartySubID' required='N'/>
    <field name='PartySubIDType' required='N'/>
   </group>
  </component>
  <component name='NstdPtysSubGrp'>
   <group name='NoNestedPartySubIDs' required='N'>
    <field name='NestedPartySubID' required='N'/>
    <field name='NestedPartySubIDType' required='N'/>
   </group>
  </component>
  <component name='HopGrp'>
   <group name='NoHops' required='N'>
    <field name='HopCompID' required='N'/>
    <field name='HopSendingTime' required='N'/>
    <field name='HopRefID' required='N'/>
   </group>
  </component>
  <component name='NstdPtys2SubGrp'>
   <group name='NoNested2PartySubIDs' required='N'>
    <field name='Nested2PartySubID' required='N'/>
    <field name='Nested2PartySubIDType' required='N'/>
   </group>
  </component>
  <component name='NstdPtys3SubGrp'>
   <group name='NoNested3PartySubIDs' required='N'>
    <field name='Nested3PartySubID' required='N'/>
    <field name='Nested3PartySubIDType' required='N'/>
   </group>
  </component>
  <component name='StrategyParametersGrp'>
   <group name='NoStrategyParameters' required='N'>
    <field name='StrategyParameterName' required='N'/>
    <field name='StrategyParameterType' required='N'/>
    <field name='StrategyParameterValue' required='N'/>
   </group>
  </component>
  <component name='SecLstUpdRelSymGrp'>
   <group name='NoRelatedSym' required='N'>
    <component name='Instrument' required='N'/>
    <component name='InstrumentExtension' required='N'/>
    <component name='FinancingDetails' required='N'/>
    <component name='UnderlyingInstrument' required='N'/>
    <field name='Currency' required='N'/>
    <component name='Stipulations' required='N'/>
    <component name='SecLstUpdRelSymsLegGrp' required='N'/>
    <component name='SpreadOrBenchmarkCurveData' required='N'/>
    <component name='YieldData' required='N'/>
    <field name='RoundLot' required='N'/>
    <field name='MinTradeVol' required='N'/>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
    <field name='ExpirationCycle' required='N'/>
    <field name='Text' required='N'/>
    <field name='EncodedTextLen' required='N'/>
    <field name='EncodedText' required='N'/>
   </group>
  </component>
  <component name='SecLstUpdRelSymsLegGrp'>
   <group name='NoLegs' required='N'>
    <component name='InstrumentLeg' required='N'/>
    <field name='LegSwapType' required='N'/>
    <field name='LegSettlType' required='N'/>
    <component name='LegStipulations' required='N'/>
    <component name='LegBenchmarkCurveData' required='N'/>
   </group>
  </component>
  <component name='UnderlyingAmount'>
   <group name='NoUnderlyingAmounts' required='N'>
    <field name='UnderlyingPayAmount' required='N'/>
    <field name='UnderlyingCollectAmount' required='N'/>
    <field name='UnderlyingSettlementDate' required='N'/>
    <field name='UnderlyingSettlementStatus' required='N'/>
   </group>
  </component>
  <component name='ExpirationQty'>
   <group name='NoExpiration' required='N'>
    <field name='ExpType' required='N'/>
    <field name='ExpQty' required='N'/>
   </group>
  </component>
  <component name='InstrumentParties'>
   <group name='NoInstrumentParties' required='N'>
    <field name='InstrumentPartyID' required='N'/>
    <field name='InstrumentPartyIDSource' required='N'/>
    <field name='InstrumentPartyRole' required='N'/>
    <component name='InstrumentPtysSubGrp' required='N'/>
   </group>
  </component>
  <component name='InstrumentPtysSubGrp'>
   <group name='NoInstrumentPartySubIDs' required='N'>
    <field name='InstrumentPartySubID' required='N'/>
    <field name='InstrumentPartySubIDType' required='N'/>
   </group>
  </component>
  <component name='SideTrdRegTS'>
   <group name='NoSideTrdRegTS' required='N'>
    <field name='SideTrdRegTimestamp' required='N'/>
    <field name='SideTrdRegTimestampType' required='N'/>
    <field name='SideTrdRegTimestampSrc' required='N'/>
   </group>
  </component>
  <component name='TrdCapRptAckSideGrp'>
   <group name='NoSides' required='Y'>
    <field name='Side' required='Y'/>
    <field name='OrderID' required='N'/>
    <field name='SecondaryOrderID' required='N'/>
    <field name='ClOrdID' required='N'/>
    <field name='SecondaryClOrdID' required='N'/>
    <field name='ListID' required='N'/>
    <component name='Parties' required='N'/>
    <field name='Account' required='N'/>
    <field name='AcctIDSource' required='N'/>
    <field name='AccountType' required='N'/>
    <field name='ProcessCode' required='N'/>
    <field name='OddLot' required='N'/>
    <field name='LotType' required='N'/>
    <component name='ClrInstGrp' required='N'/>
    <field name='TradeInputSource' required='N'/>
    <field name='TradeInputDevice' required='N'/>
    <field name='OrderInputDevice' required='N'/>
    <field name='Currency' required='N'/>
    <field name='ComplianceID' required='N'/>
    <field name='SolicitedFlag' required='N'/>
    <field name='OrderCapacity' required='N'/>
    <field name='OrderRestrictions' required='N'/>
    <field name='CustOrderCapacity' required='N'/>
    <field name='OrdType' required='N'/>
    <field name='ExecInst' required='N'/>
    <field name='TransBkdTime' required='N'/>
    <field name='TradingSessionID' required='N'/>
    <field name='TradingSessionSubID' required='N'/>
    <field name='TimeBracket' required='N'/>
    <component name='CommissionData' required='N'/>
    <field name='NumDaysInterest' required='N'/>
    <field name='ExDate' required='N'/>
    <field name='AccruedInterestRate' required='N'/>
    <field name='AccruedInterestAmt' required='N'/>
    <field name='InterestAtMaturity' required='N'/>
    <field name='EndAccruedInterestAmt' required='N'/>
    <field name='StartCash' required='N'/>
    <field name='EndCash' required='N'/>
    <field name='Concession' required='N'/>
    <field name='TotalTakedown' required='N'/>
    <field name='NetMoney' required='N'/>
    <field name='SettlCurrAmt' required='N'/>
    <field name='SettlCurrency' required='N'/>
    <field name='SettlCurrFxRate' required='N'/>
    <field name='SettlCurrFxRateCalc' required='N'/>
    <field name='PositionEffect' required='N'/>
    <field name='SideMultiLegReportingType' required='N'/>
    <component name='ContAmtGrp' required='N'/>
    <component name='Stipulations' required='N'/>
    <component name='MiscFeesGrp' required='N'/>
    <field name='ExchangeRule' required='N'/>
    <field name='TradeAllocIndicator' required='N'/>
    <field name='PreallocMethod' required='N'/>
    <field name='AllocID' required='N'/>
    <component name='TrdAllocGrp' required='N'/>
    <field name='SideGrossTradeAmt' required='N'/>
    <field name='AggressorIndicator' required='N'/>
    <field name='SideQty' required='N'/>
    <field name='SideTradeReportID' required='N'/>
    <field name='SideFillStationCd' required='N'/>
    <field name='SideReasonCd' required='N'/>
    <field name='RptSeq' required='N'/>
    <field name='SideTrdSubTyp' required='N'/>
    <component name='SideTrdRegTS' required='N'/>
   </group>
  </component>
  <component name='UndlyInstrumentParties'>
   <group name='NoUndlyInstrumentParties' required='N'>
    <field name='UndlyInstrumentPartyID' required='N'/>
    <field name='UndlyInstrumentPartyIDSource' required='N'/>
    <field name='UndlyInstrumentPartyRole' required='N'/>
    <component name='UndlyInstrumentPtysSubGrp' required='N'/>
   </group>
  </component>
  <component name='UndlyInstrumentPtysSubGrp'>
   <group name='NoUndlyInstrumentPartySubIDs' required='N'>
    <field name='UndlyInstrumentPartySubID' required='N'/>
    <field name='UndlyInstrumentPartySubIDType' required='N'/>
   </group>
  </component>
  <component name='DisplayInstruction'>
   <field name='DisplayQty' required='N'/>
   <field name='SecondaryDisplayQty' required='N'/>
   <field name='DisplayWhen' required='N'/>
   <field name='DisplayMethod' required='N'/>
   <field name='DisplayLowQty' required='N'/>
   <field name='DisplayHighQty' required='N'/>
   <field name='DisplayMinIncr' required='N'/>
   <field name='RefreshQty' required='N'/>
  </component>
  <component name='TriggeringInstruction'>
   <field name='TriggerType' required='N'/>
   <field name='TriggerAction' required='N'/>
   <field name='TriggerPrice' required='N'/>
   <field name='TriggerSymbol' required='N'/>
   <field name='TriggerSecurityID' required='N'/>
   <field name='TriggerSecurityIDSource' required='N'/>
   <field name='TriggerSecurityDesc' required='N'/>
   <field name='TriggerPriceType' required='N'/>
   <field name='TriggerPriceTypeScope' required='N'/>
   <field name='TriggerPriceDirection' required='N'/>
   <field name='TriggerNewPrice' required='N'/>
   <field name='TriggerOrderType' required='N'/>
   <field name='TriggerNewQty' required='N'/>
   <field name='TriggerTradingSessionID' required='N'/>
   <field name='TriggerTradingSessionSubID' required='N'/>
  </component>
  <component name='RootParties'>
   <group name='NoRootPartyIDs' required='N'>
    <field name='RootPartyID' required='N'/>
    <field name='RootPartyIDSource' required='N'/>
    <field name='RootPartyRole' required='N'/>
    <component name='RootSubParties' required='N'/>
   </group>
  </component>
  <component name='RootSubParties'>
   <group name='NoRootPartySubIDs' required='N'>
    <field name='RootPartySubID' required='N'/>
    <field name='RootPartySubIDType' required='N'/>
   </group>
  </component>
  <component name='TrdSessLstGrp'>
   <group name='NoTradingSessions' required='Y'>
    <field name='TradingSessionID' required='Y'/>
    <field name='TradingSessionSubID' required='N'/>
    <field name='SecurityExchange' required='N'/>
    <field name='TradSesMethod' required='N'/>
    <field name='TradSesMode' required='N'/>
    <field name='UnsolicitedIndicator' required='N'/>
    <field name='TradSesStatus' required='Y'/>
    <field name='TradSesStatusRejReason' required='N'/>
    <field name='TradSesStartTime' required='N'/>
    <field name='TradSesOpenTime' required='N'/>
    <field name='TradSesPreCloseTime' required='N'/>
    <field name='TradSesCloseTime' required='N'/>
    <field name='TradSesEndTime' required='N'/>
    <field name='TotalVolumeTraded' required='N'/>
    <field name='Text' required='N'/>
    <field name='EncodedTextLen' required='N'/>
    <field name='EncodedText' required='N'/>
   </group>
  </component>
  <component name='MsgTypeGrp'>
   <group name='NoMsgTypes' required='N'>
    <field name='RefMsgType' required='N'/>
    <field name='MsgDirection' required='N'/>
    <field name='RefApplVerID' required='N'/>
    <field name='RefCstmApplVerID' required='N'/>
   </group>
  </component>
 </components>
 <fields>
  <field number='1' name='Account' type='STRING'/>
  <field number='2' name='AdvId' type='STRING'/>
  <field number='3' name='AdvRefID' type='STRING'/>
  <field number='4' name='AdvSide' type='CHAR'>
   <value enum='B' description='BUY'/>
   <value enum='S' description='SELL'/>
   <value enum='T' description='TRADE'/>
   <value enum='X' description='CROSS'/>
  </field>
  <field number='5' name='AdvTransType' type='STRING'>
   <value enum='C' description='CANCEL'/>
   <value enum='N' description='NEW'/>
   <value enum='R' description='REPLACE'/>
  </field>
  <field number='6' name='AvgPx' type='PRICE'/>
  <field number='7' name='BeginSeqNo' type='SEQNUM'/>
  <field number='8' name='BeginString' type='STRING'/>
  <field number='9' name='BodyLength' type='LENGTH'/>
  <field number='10' name='CheckSum' type='STRING'/>
  <field number='11' name='ClOrdID' type='STRING'/>
  <field number='12' name='Commission' type='AMT'/>
  <field number='13' name='CommType' type='CHAR'>
   <value enum='1' description='PER_UNIT'/>
   <value enum='2' description='PERCENT'/>
   <value enum='3' description='ABSOLUTE'/>
   <value enum='4' description='PERCENTAGE_WAIVED_CASH_DISCOUNT'/>
   <value enum='5' description='PERCENTAGE_WAIVED_ENHANCED_UNITS'/>
   <value enum='6' description='POINTS_PER_BOND_OR_CONTRACT'/>
  </field>
  <field number='14' name='CumQty' type='QTY'/>
  <field number='15' name='Currency' type='CURRENCY'/>
  <field number='16' name='EndSeqNo' type='SEQNUM'/>
  <field number='17' name='ExecID' type='STRING'/>
  <field number='18' name='ExecInst' type='MULTIPLECHARVALUE'>
   <value enum='0' description='STAY_ON_OFFER_SIDE'/>
   <value enum='1' description='NOT_HELD'/>
   <value enum='2' description='WORK'/>
   <value enum='3' description='GO_ALONG'/>
   <value enum='4' description='OVER_THE_DAY'/>
   <value enum='5' description='HELD'/>
   <value enum='6' description='PARTICIPATE_DO_NOT_INITIATE'/>
   <value enum='7' description='STRICT_SCALE'/>
   <value enum='8' description='TRY_TO_SCALE'/>
   <value enum='9' description='STAY_ON_BID_SIDE'/>
   <value enum='A' description='NO_CROSS'/>
   <value enum='B' description='OK_TO_CROSS'/>
   <value enum='C' description='CALL_FIRST'/>
   <value enum='D' description='PERCENT_OF_VOLUME'/>
   <value enum='E' description='DO_NOT_INCREASE'/>
   <value enum='F' description='DO_NOT_REDUCE'/>
   <value enum='G' description='ALL_OR_NONE'/>
   <value enum='H' description='REINSTATE_ON_SYSTEM_FAILURE'/>
   <value enum='I' description='INSTITUTIONS_ONLY'/>
   <value enum='J' description='REINSTATE_ON_TRADING_HALT'/>
   <value enum='K' description='CANCEL_ON_TRADING_HALT'/>
   <value enum='L' description='LAST_PEG'/>
   <value enum='M' description='MID_PRICE_PEG'/>
   <value enum='N' description='NON_NEGOTIABLE'/>
   <value enum='O' description='OPENING_PEG'/>
   <value enum='P' description='MARKET_PEG'/>
   <value enum='Q' description='CANCEL_ON_SYSTEM_FAILURE'/>
   <value enum='R' description='PRIMARY_PEG'/>
   <value enum='S' description='SUSPEND'/>
   <value enum='T' description='FIXED_PEG_TO_LOCAL_BEST_BID_OR_OFFER_AT_TIME_OF_ORDER'/>
   <value enum='U' description='CUSTOMER_DISPLAY_INSTRUCTION'/>
   <value enum='V' description='NETTING'/>
   <value enum='W' description='PEG_TO_VWAP'/>
   <value enum='X' description='TRADE_ALONG'/>
   <value enum='Y' description='TRY_TO_STOP'/>
   <value enum='Z' description='CANCEL_IF_NOT_BEST'/>
   <value enum='a' description='TRAILING_STOP_PEG'/>
   <value enum='b' description='STRICT_LIMIT'/>
   <value enum='c' description='IGNORE_PRICE_VALIDITY_CHECKS'/>
   <value enum='d' description='PEG_TO_LIMIT_PRICE'/>
   <value enum='e' description='WORK_TO_TARGET_STRATEGY'/>
   <value enum='f' description='INTERMARKET_SWEEP'/>
   <value enum='g' description='EXTERNAL_ROUTING_ALLOWED'/>
   <value enum='h' description='EXTERNAL_ROUTING_NOT_ALLOWED'/>
   <value enum='i' description='IMBALANCE_ONLY'/>
   <value enum='j' description='SINGLE_EXECUTION_REQUESTED_FOR_BLOCK_TRADE'/>
   <value enum='k' description='BEST_EXECUTION'/>
  </field>
  <field number='19' name='ExecRefID' type='STRING'/>
  <field number='21' name='HandlInst' type='CHAR'>
   <value enum='1' description='AUTOMATED_EXECUTION_NO_INTERVENTION'/>
   <value enum='2' description='AUTOMATED_EXECUTION_INTERVENTION_OK'/>
   <value enum='3' description='MANUAL_ORDER'/>
  </field>
  <field number='22' name='SecurityIDSource' type='STRING'>
   <value enum='1' description='CUSIP'/>
   <value enum='2' description='SEDOL'/>
   <value enum='3' description='QUIK'/>
   <value enum='4' description='ISIN_NUMBER'/>
   <value enum='5' description='RIC_CODE'/>
   <value enum='6' description='ISO_CURRENCY_CODE'/>
   <value enum='7' description='ISO_COUNTRY_CODE'/>
   <value enum='8' description='EXCHANGE_SYMBOL'/>
   <value enum='9' description='CONSOLIDATED_TAPE_ASSOCIATION'/>
   <value enum='A' description='BLOOMBERG_SYMBOL'/>
   <value enum='B' description='WERTPAPIER'/>
   <value enum='C' description='DUTCH'/>
   <value enum='D' description='VALOREN'/>
   <value enum='E' description='SICOVAM'/>
   <value enum='F' description='BELGIAN'/>
   <value enum='G' description='COMMON'/>
   <value enum='H' description='CLEARING_HOUSE'/>
   <value enum='I' description='ISDA_FP_ML_SPECIFICATION'/>
   <value enum='J' description='OPTION_PRICE_REPORTING_AUTHORITY'/>
   <value enum='K' description='ISDA_FP_MLURL'/>
   <value enum='L' description='LETTER_OF_CREDIT'/>
  </field>
  <field number='23' name='IOIID' type='STRING'/>
  <field number='25' name='IOIQltyInd' type='CHAR'>
   <value enum='H' description='HIGH'/>
   <value enum='L' description='LOW'/>
   <value enum='M' description='MEDIUM'/>
  </field>
  <field number='26' name='IOIRefID' type='STRING'/>
  <field number='27' name='IOIQty' type='STRING'>
   <value enum='L' description='LARGE'/>
   <value enum='M' description='MEDIUM'/>
   <value enum='S' description='SMALL'/>
   <value enum='U' description='UNDISCLOSED_QUANTITY'/>
  </field>
  <field number='28' name='IOITransType' type='CHAR'>
   <value enum='C' description='CANCEL'/>
   <value enum='N' description='NEW'/>
   <value enum='R' description='REPLACE'/>
  </field>
  <field number='29' name='LastCapacity' type='CHAR'>
   <value enum='1' description='AGENT'/>
   <value enum='2' description='CROSS_AS_AGENT'/>
   <value enum='3' description='CROSS_AS_PRINCIPAL'/>
   <value enum='4' description='PRINCIPAL'/>
  </field>
  <field number='30' name='LastMkt' type='EXCHANGE'/>
  <field number='31' name='LastPx' type='PRICE'/>
  <field number='32' name='LastQty' type='QTY'/>
  <field number='33' name='NoLinesOfText' type='NUMINGROUP'/>
  <field number='34' name='MsgSeqNum' type='SEQNUM'/>
  <field number='35' name='MsgType' type='STRING'>
   <value enum='0' description='HEARTBEAT'/>
   <value enum='1' description='TEST_REQUEST'/>
   <value enum='2' description='RESEND_REQUEST'/>
   <value enum='3' description='REJECT'/>
   <value enum='4' description='SEQUENCE_RESET'/>
   <value enum='5' description='LOGOUT'/>
   <value enum='6' description='IOI'/>
   <value enum='7' description='ADVERTISEMENT'/>
   <value enum='8' description='EXECUTION_REPORT'/>
   <value enum='9' description='ORDER_CANCEL_REJECT'/>
   <value enum='A' description='LOGON'/>
   <value enum='B' description='NEWS'/>
   <value enum='C' description='EMAIL'/>
   <value enum='D' description='NEW_ORDER_SINGLE'/>
   <value enum='E' description='NEW_ORDER_LIST'/>
   <value enum='F' description='ORDER_CANCEL_REQUEST'/>
   <value enum='G' description='ORDER_CANCEL_REPLACE_REQUEST'/>
   <value enum='H' description='ORDER_STATUS_REQUEST'/>
   <value enum='J' description='ALLOCATION_INSTRUCTION'/>
   <value enum='K' description='LIST_CANCEL_REQUEST'/>
   <value enum='L' description='LIST_EXECUTE'/>
   <value enum='M' description='LIST_STATUS_REQUEST'/>
   <value enum='N' description='LIST_STATUS'/>
   <value enum='P' description='ALLOCATION_INSTRUCTION_ACK'/>
   <value enum='Q' description='DONT_KNOW_TRADE'/>
   <value enum='R' description='QUOTE_REQUEST'/>
   <value enum='S' description='QUOTE'/>
   <value enum='T' description='SETTLEMENT_INSTRUCTIONS'/>
   <value enum='V' description='MARKET_DATA_REQUEST'/>
   <value enum='W' description='MARKET_DATA_SNAPSHOT_FULL_REFRESH'/>
   <value enum='X' description='MARKET_DATA_INCREMENTAL_REFRESH'/>
   <value enum='Y' description='MARKET_DATA_REQUEST_REJECT'/>
   <value enum='Z' description='QUOTE_CANCEL'/>
   <value enum='a' description='QUOTE_STATUS_REQUEST'/>
   <value enum='b' description='MASS_QUOTE_ACKNOWLEDGEMENT'/>
   <value enum='c' description='SECURITY_DEFINITION_REQUEST'/>
   <value enum='d' description='SECURITY_DEFINITION'/>
   <value enum='e' description='SECURITY_STATUS_REQUEST'/>
   <value enum='f' description='SECURITY_STATUS'/>
   <value enum='g' description='TRADING_SESSION_STATUS_REQUEST'/>
   <value enum='h' description='TRADING_SESSION_STATUS'/>
   <value enum='i' description='MASS_QUOTE'/>
   <value enum='j' description='BUSINESS_MESSAGE_REJECT'/>
   <value enum='k' description='BID_REQUEST'/>
   <value enum='l' description='BID_RESPONSE'/>
   <value enum='m' description='LIST_STRIKE_PRICE'/>
   <value enum='n' description='XML_NON_FIX'/>
   <value enum='o' description='REGISTRATION_INSTRUCTIONS'/>
   <value enum='p' description='REGISTRATION_INSTRUCTIONS_RESPONSE'/>
   <value enum='q' description='ORDER_MASS_CANCEL_REQUEST'/>
   <value enum='r' description='ORDER_MASS_CANCEL_REPORT'/>
   <value enum='s' description='NEW_ORDER_CROSS'/>
   <value enum='t' description='CROSS_ORDER_CANCEL_REPLACE_REQUEST'/>
   <value enum='u' description='CROSS_ORDER_CANCEL_REQUEST'/>
   <value enum='v' description='SECURITY_TYPE_REQUEST'/>
   <value enum='w' description='SECURITY_TYPES'/>
   <value enum='x' description='SECURITY_LIST_REQUEST'/>
   <value enum='y' description='SECURITY_LIST'/>
   <value enum='z' description='DERIVATIVE_SECURITY_LIST_REQUEST'/>
   <value enum='AA' description='DERIVATIVE_SECURITY_LIST'/>
   <value enum='AB' description='NEW_ORDER_MULTILEG'/>
   <value enum='AC' description='MULTILEG_ORDER_CANCEL_REPLACE'/>
   <value enum='AD' description='TRADE_CAPTURE_REPORT_REQUEST'/>
   <value enum='AE' description='TRADE_CAPTURE_REPORT'/>
   <value enum='AF' description='ORDER_MASS_STATUS_REQUEST'/>
   <value enum='AG' description='QUOTE_REQUEST_REJECT'/>
   <value enum='AH' description='RFQ_REQUEST'/>
   <value enum='AI' description='QUOTE_STATUS_REPORT'/>
   <value enum='AJ' description='QUOTE_RESPONSE'/>
   <value enum='AK' description='CONFIRMATION'/>
   <value enum='AL' description='POSITION_MAINTENANCE_REQUEST'/>
   <value enum='AM' description='POSITION_MAINTENANCE_REPORT'/>
   <value enum='AN' description='REQUEST_FOR_POSITIONS'/>
   <value enum='AO' description='REQUEST_FOR_POSITIONS_ACK'/>
   <value enum='AP' description='POSITION_REPORT'/>
   <value enum='AQ' description='TRADE_CAPTURE_REPORT_REQUEST_ACK'/>
   <value enum='AR' description='TRADE_CAPTURE_REPORT_ACK'/>
   <value enum='AS' description='ALLOCATION_REPORT'/>
   <value enum='AT' description='ALLOCATION_REPORT_ACK'/>
   <value enum='AU' description='CONFIRMATION_ACK'/>
   <value enum='AV' description='SETTLEMENT_INSTRUCTION_REQUEST'/>
   <value enum='AW' description='ASSIGNMENT_REPORT'/>
   <value enum='AX' description='COLLATERAL_REQUEST'/>
   <value enum='AY' description='COLLATERAL_ASSIGNMENT'/>
   <value enum='AZ' description='COLLATERAL_RESPONSE'/>
   <value enum='BA' description='COLLATERAL_REPORT'/>
   <value enum='BB' description='COLLATERAL_INQUIRY'/>
   <value enum='BC' description='NETWORK_COUNTERPARTY_SYSTEM_STATUS_REQUEST'/>
   <value enum='BD' description='NETWORK_COUNTERPARTY_SYSTEM_STATUS_RESPONSE'/>
   <value enum='BE' description='USER_REQUEST'/>
   <value enum='BF' description='USER_RESPONSE'/>
   <value enum='BG' description='COLLATERAL_INQUIRY_ACK'/>
   <value enum='BH' description='CONFIRMATION_REQUEST'/>
   <value enum='BI' description='TRADING_SESSION_LIST_REQUEST'/>
   <value enum='BJ' description='TRADING_SESSION_LIST'/>
   <value enum='BK' description='SECURITY_LIST_UPDATE_REPORT'/>
   <value enum='BL' description='ADJUSTED_POSITION_REPORT'/>
   <value enum='BM' description='ALLOCATION_INSTRUCTION_ALERT'/>
   <value enum='BN' description='EXECUTION_ACKNOWLEDGEMENT'/>
   <value enum='BO' description='CONTRARY_INTENTION_REPORT'/>
   <value enum='BP' description='SECURITY_DEFINITION_UPDATE_REPORT'/>
  </field>
  <field number='36' name='NewSeqNo' type='SEQNUM'/>
  <field number='37' name='OrderID' type='STRING'/>
  <field number='38' name='OrderQty' type='QTY'/>
  <field number='39' name='OrdStatus' type='CHAR'>
   <value enum='0' description='NEW'/>
   <value enum='1' description='PARTIALLY_FILLED'/>
   <value enum='2' description='FILLED'/>
   <value enum='3' description='DONE_FOR_DAY'/>
   <value enum='4' description='CANCELED'/>
   <value enum='5' description='REPLACED'/>
   <value enum='6' description='PENDING_CANCEL'/>
   <value enum='7' description='STOPPED'/>
   <value enum='8' description='REJECTED'/>
   <value enum='9' description='SUSPENDED'/>
   <value enum='A' description='PENDING_NEW'/>
   <value enum='B' description='CALCULATED'/>
   <value enum='C' description='EXPIRED'/>
   <value enum='D' description='ACCEPTED_FOR_BIDDING'/>
   <value enum='E' description='PENDING_REPLACE'/>
  </field>
  <field number='40' name='OrdType' type='CHAR'>
   <value enum='1' description='MARKET'/>
   <value enum='2' description='LIMIT'/>
   <value enum='3' description='STOP'/>
   <value enum='4' description='STOP_LIMIT'/>
   <value enum='5' description='MARKET_ON_CLOSE'/>
   <value enum='6' description='WITH_OR_WITHOUT'/>
   <value enum='7' description='LIMIT_OR_BETTER'/>
   <value enum='8' description='LIMIT_WITH_OR_WITHOUT'/>
   <value enum='9' description='ON_BASIS'/>
   <value enum='A' description='ON_CLOSE'/>
   <value enum='B' description='LIMIT_ON_CLOSE'/>
   <value enum='C' description='FOREX_MARKET'/>
   <value enum='D' description='PREVIOUSLY_QUOTED'/>
   <value enum='E' description='PREVIOUSLY_INDICATED'/>
   <value enum='F' description='FOREX_LIMIT'/>
   <value enum='G' description='FOREX_SWAP'/>
   <value enum='H' description='FOREX_PREVIOUSLY_QUOTED'/>
   <value enum='I' description='FUNARI'/>
   <value enum='J' description='MARKET_IF_TOUCHED'/>
   <value enum='K' description='MARKET_WITH_LEFT_OVER_AS_LIMIT'/>
   <value enum='L' description='PREVIOUS_FUND_VALUATION_POINT'/>
   <value enum='M' description='NEXT_FUND_VALUATION_POINT'/>
   <value enum='P' description='PEGGED'/>
   <value enum='Q' description='COUNTER_ORDER_SELECTION'/>
  </field>
  <field number='41' name='OrigClOrdID' type='STRING'/>
  <field number='42' name='OrigTime' type='UTCTIMESTAMP'/>
  <field number='43' name='PossDupFlag' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='44' name='Price' type='PRICE'/>
  <field number='45' name='RefSeqNum' type='SEQNUM'/>
  <field number='48' name='SecurityID' type='STRING'/>
  <field number='49' name='SenderCompID' type='STRING'/>
  <field number='50' name='SenderSubID' type='STRING'/>
  <field number='52' name='SendingTime' type='UTCTIMESTAMP'/>
  <field number='53' name='Quantity' type='QTY'/>
  <field number='54' name='Side' type='CHAR'>
   <value enum='1' description='BUY'/>
   <value enum='2' description='SELL'/>
   <value enum='3' description='BUY_MINUS'/>
   <value enum='4' description='SELL_PLUS'/>
   <value enum='5' description='SELL_SHORT'/>
   <value enum='6' description='SELL_SHORT_EXEMPT'/>
   <value enum='7' description='UNDISCLOSED'/>
   <value enum='8' description='CROSS'/>
   <value enum='9' description='CROSS_SHORT'/>
   <value enum='A' description='CROSS_SHORT_EXEMPT'/>
   <value enum='B' description='AS_DEFINED'/>
   <value enum='C' description='OPPOSITE'/>
   <value enum='D' description='SUBSCRIBE'/>
   <value enum='E' description='REDEEM'/>
   <value enum='F' description='LEND'/>
   <value enum='G' description='BORROW'/>
  </field>
  <field number='55' name='Symbol' type='STRING'/>
  <field number='56' name='TargetCompID' type='STRING'/>
  <field number='57' name='TargetSubID' type='STRING'/>
  <field number='58' name='Text' type='STRING'/>
  <field number='59' name='TimeInForce' type='CHAR'>
   <value enum='0' description='DAY'/>
   <value enum='1' description='GOOD_TILL_CANCEL'/>
   <value enum='2' description='AT_THE_OPENING'/>
   <value enum='3' description='IMMEDIATE_OR_CANCEL'/>
   <value enum='4' description='FILL_OR_KILL'/>
   <value enum='5' description='GOOD_TILL_CROSSING'/>
   <value enum='6' description='GOOD_TILL_DATE'/>
   <value enum='7' description='AT_THE_CLOSE'/>
  </field>
  <field number='60' name='TransactTime' type='UTCTIMESTAMP'/>
  <field number='61' name='Urgency' type='CHAR'>
   <value enum='0' description='NORMAL'/>
   <value enum='1' description='FLASH'/>
   <value enum='2' description='BACKGROUND'/>
  </field>
  <field number='62' name='ValidUntilTime' type='UTCTIMESTAMP'/>
  <field number='63' name='SettlType' type='STRING'>
   <value enum='0' description='REGULAR'/>
   <value enum='1' description='CASH'/>
   <value enum='2' description='NEXT_DAY'/>
   <value enum='3' description='T_PLUS2'/>
   <value enum='4' description='T_PLUS3'/>
   <value enum='5' description='T_PLUS4'/>
   <value enum='6' description='FUTURE'/>
   <value enum='7' description='WHEN_AND_IF_ISSUED'/>
   <value enum='8' description='SELLERS_OPTION'/>
   <value enum='9' description='T_PLUS5'/>
   <value enum='B' description='BROKEN_DATE'/>
   <value enum='C' description='FX_SPOT_NEXT_SETTLEMENT'/>
  </field>
  <field number='64' name='SettlDate' type='LOCALMKTDATE'/>
  <field number='65' name='SymbolSfx' type='STRING'>
   <value enum='CD' description='EUCP_WITH_LUMP_SUM_INTEREST'/>
   <value enum='WI' description='WHEN_ISSUED'/>
  </field>
  <field number='66' name='ListID' type='STRING'/>
  <field number='67' name='ListSeqNo' type='INT'/>
  <field number='68' name='TotNoOrders' type='INT'/>
  <field number='69' name='ListExecInst' type='STRING'/>
  <field number='70' name='AllocID' type='STRING'/>
  <field number='71' name='AllocTransType' type='CHAR'>
   <value enum='0' description='NEW'/>
   <value enum='1' description='REPLACE'/>
   <value enum='2' description='CANCEL'/>
   <value enum='3' description='PRELIMINARY'/>
   <value enum='4' description='CALCULATED'/>
   <value enum='5' description='CALCULATED_WITHOUT_PRELIMINARY'/>
   <value enum='6' description='REVERSAL'/>
  </field>
  <field number='72' name='RefAllocID' type='STRING'/>
  <field number='73' name='NoOrders' type='NUMINGROUP'/>
  <field number='74' name='AvgPxPrecision' type='INT'/>
  <field number='75' name='TradeDate' type='LOCALMKTDATE'/>
  <field number='77' name='PositionEffect' type='CHAR'>
   <value enum='C' description='CLOSE'/>
   <value enum='F' description='FIFO'/>
   <value enum='O' description='OPEN'/>
   <value enum='R' description='ROLLED'/>
  </field>
  <field number='78' name='NoAllocs' type='NUMINGROUP'/>
  <field number='79' name='AllocAccount' type='STRING'/>
  <field number='80' name='AllocQty' type='QTY'/>
  <field number='81' name='ProcessCode' type='CHAR'>
   <value enum='0' description='REGULAR'/>
   <value enum='1' description='SOFT_DOLLAR'/>
   <value enum='2' description='STEP_IN'/>
   <value enum='3' description='STEP_OUT'/>
   <value enum='4' description='SOFT_DOLLAR_STEP_IN'/>
   <value enum='5' description='SOFT_DOLLAR_STEP_OUT'/>
   <value enum='6' description='PLAN_SPONSOR'/>
  </field>
  <field number='82' name='NoRpts' type='INT'/>
  <field number='83' name='RptSeq' type='INT'/>
  <field number='84' name='CxlQty' type='QTY'/>
  <field number='85' name='NoDlvyInst' type='NUMINGROUP'/>
  <field number='87' name='AllocStatus' type='INT'>
   <value enum='0' description='ACCEPTED'/>
   <value enum='1' description='BLOCK_LEVEL_REJECT'/>
   <value enum='2' description='ACCOUNT_LEVEL_REJECT'/>
   <value enum='3' description='RECEIVED'/>
   <value enum='4' description='INCOMPLETE'/>
   <value enum='5' description='REJECTED_BY_INTERMEDIARY'/>
   <value enum='6' description='ALLOCATION_PENDING'/>
   <value enum='7' description='REVERSED'/>
  </field>
  <field number='88' name='AllocRejCode' type='INT'>
   <value enum='0' description='UNKNOWN_ACCOUNT'/>
   <value enum='1' description='INCORRECT_QUANTITY'/>
   <value enum='2' description='INCORRECT_AVERAGEG_PRICE'/>
   <value enum='3' description='UNKNOWN_EXECUTING_BROKER_MNEMONIC'/>
   <value enum='4' description='COMMISSION_DIFFERENCE'/>
   <value enum='5' description='UNKNOWN_ORDER_ID'/>
   <value enum='6' description='UNKNOWN_LIST_ID'/>
   <value enum='7' description='OTHER_SEE_TEXT'/>
   <value enum='8' description='INCORRECT_ALLOCATED_QUANTITY'/>
   <value enum='9' description='CALCULATION_DIFFERENCE'/>
   <value enum='10' description='UNKNOWN_OR_STALE_EXEC_ID'/>
   <value enum='11' description='MISMATCHED_DATA'/>
   <value enum='12' description='UNKNOWN_CL_ORD_ID'/>
   <value enum='13' description='WAREHOUSE_REQUEST_REJECTED'/>
  </field>
  <field number='89' name='Signature' type='DATA'/>
  <field number='90' name='SecureDataLen' type='LENGTH'/>
  <field number='91' name='SecureData' type='DATA'/>
  <field number='93' name='SignatureLength' type='LENGTH'/>
  <field number='94' name='EmailType' type='CHAR'>
   <value enum='0' description='NEW'/>
   <value enum='1' description='REPLY'/>
   <value enum='2' description='ADMIN_REPLY'/>
  </field>
  <field number='95' name='RawDataLength' type='LENGTH'/>
  <field number='96' name='RawData' type='DATA'/>
  <field number='97' name='PossResend' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='98' name='EncryptMethod' type='INT'>
   <value enum='0' description='NONE_OTHER'/>
   <value enum='1' description='PKCS'/>
   <value enum='2' description='DES'/>
   <value enum='3' description='PKCSDES'/>
   <value enum='4' description='PGPDES'/>
   <value enum='5' description='PGPDESMD5'/>
   <value enum='6' description='PEMDESMD5'/>
  </field>
  <field number='99' name='StopPx' type='PRICE'/>
  <field number='100' name='ExDestination' type='EXCHANGE'/>
  <field number='102' name='CxlRejReason' type='INT'>
   <value enum='0' description='TOO_LATE_TO_CANCEL'/>
   <value enum='1' description='UNKNOWN_ORDER'/>
   <value enum='2' description='BROKER_CREDIT'/>
   <value enum='3' description='ORDER_ALREADY_IN_PENDING_STATUS'/>
   <value enum='4' description='UNABLE_TO_PROCESS_ORDER_MASS_CANCEL_REQUEST'/>
   <value enum='5' description='ORIG_ORD_MOD_TIME'/>
   <value enum='6' description='DUPLICATE_CL_ORD_ID'/>
   <value enum='18' description='INVALID_PRICE_INCREMENT'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='103' name='OrdRejReason' type='INT'>
   <value enum='0' description='BROKER_CREDIT'/>
   <value enum='1' description='UNKNOWN_SYMBOL'/>
   <value enum='2' description='EXCHANGE_CLOSED'/>
   <value enum='3' description='ORDER_EXCEEDS_LIMIT'/>
   <value enum='4' description='TOO_LATE_TO_ENTER'/>
   <value enum='5' description='UNKNOWN_ORDER'/>
   <value enum='6' description='DUPLICATE_ORDER'/>
   <value enum='7' description='DUPLICATE_OF_A_VERBALLY_COMMUNICATED_ORDER'/>
   <value enum='8' description='STALE_ORDER'/>
   <value enum='9' description='TRADE_ALONG_REQUIRED'/>
   <value enum='10' description='INVALID_INVESTOR_ID'/>
   <value enum='11' description='UNSUPPORTED_ORDER_CHARACTERISTIC'/>
   <value enum='12' description='SURVEILLENCE_OPTION'/>
   <value enum='13' description='INCORRECT_QUANTITY'/>
   <value enum='14' description='INCORRECT_ALLOCATED_QUANTITY'/>
   <value enum='15' description='UNKNOWN_ACCOUNT'/>
   <value enum='18' description='INVALID_PRICE_INCREMENT'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='104' name='IOIQualifier' type='CHAR'>
   <value enum='A' description='ALL_OR_NONE'/>
   <value enum='B' description='MARKET_ON_CLOSE'/>
   <value enum='C' description='AT_THE_CLOSE'/>
   <value enum='D' description='VWAP'/>
   <value enum='I' description='IN_TOUCH_WITH'/>
   <value enum='L' description='LIMIT'/>
   <value enum='M' description='MORE_BEHIND'/>
   <value enum='O' description='AT_THE_OPEN'/>
   <value enum='P' description='TAKING_A_POSITION'/>
   <value enum='Q' description='AT_THE_MARKET'/>
   <value enum='R' description='READY_TO_TRADE'/>
   <value enum='S' description='PORTFOLIO_SHOWN'/>
   <value enum='T' description='THROUGH_THE_DAY'/>
   <value enum='V' description='VERSUS'/>
   <value enum='W' description='INDICATION'/>
   <value enum='X' description='CROSSING_OPPORTUNITY'/>
   <value enum='Y' description='AT_THE_MIDPOINT'/>
   <value enum='Z' description='PRE_OPEN'/>
  </field>
  <field number='106' name='Issuer' type='STRING'/>
  <field number='107' name='SecurityDesc' type='STRING'/>
  <field number='108' name='HeartBtInt' type='INT'/>
  <field number='110' name='MinQty' type='QTY'/>
  <field number='111' name='MaxFloor' type='QTY'/>
  <field number='112' name='TestReqID' type='STRING'/>
  <field number='113' name='ReportToExch' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='114' name='LocateReqd' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='115' name='OnBehalfOfCompID' type='STRING'/>
  <field number='116' name='OnBehalfOfSubID' type='STRING'/>
  <field number='117' name='QuoteID' type='STRING'/>
  <field number='118' name='NetMoney' type='AMT'/>
  <field number='119' name='SettlCurrAmt' type='AMT'/>
  <field number='120' name='SettlCurrency' type='CURRENCY'/>
  <field number='121' name='ForexReq' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='122' name='OrigSendingTime' type='UTCTIMESTAMP'/>
  <field number='123' name='GapFillFlag' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='124' name='NoExecs' type='NUMINGROUP'/>
  <field number='126' name='ExpireTime' type='UTCTIMESTAMP'/>
  <field number='127' name='DKReason' type='CHAR'>
   <value enum='A' description='UNKNOWN_SYMBOL'/>
   <value enum='B' description='WRONG_SIDE'/>
   <value enum='C' description='QUANTITY_EXCEEDS_ORDER'/>
   <value enum='D' description='NO_MATCHING_ORDER'/>
   <value enum='E' description='PRICE_EXCEEDS_LIMIT'/>
   <value enum='F' description='CALCULATION_DIFFERENCE'/>
   <value enum='Z' description='OTHER'/>
  </field>
  <field number='128' name='DeliverToCompID' type='STRING'/>
  <field number='129' name='DeliverToSubID' type='STRING'/>
  <field number='130' name='IOINaturalFlag' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='131' name='QuoteReqID' type='STRING'/>
  <field number='132' name='BidPx' type='PRICE'/>
  <field number='133' name='OfferPx' type='PRICE'/>
  <field number='134' name='BidSize' type='QTY'/>
  <field number='135' name='OfferSize' type='QTY'/>
  <field number='136' name='NoMiscFees' type='NUMINGROUP'/>
  <field number='137' name='MiscFeeAmt' type='AMT'/>
  <field number='138' name='MiscFeeCurr' type='CURRENCY'/>
  <field number='139' name='MiscFeeType' type='STRING'>
   <value enum='1' description='REGULATORY'/>
   <value enum='2' description='TAX'/>
   <value enum='3' description='LOCAL_COMMISSION'/>
   <value enum='4' description='EXCHANGE_FEES'/>
   <value enum='5' description='STAMP'/>
   <value enum='6' description='LEVY'/>
   <value enum='7' description='OTHER'/>
   <value enum='8' description='MARKUP'/>
   <value enum='9' description='CONSUMPTION_TAX'/>
   <value enum='10' description='PER_TRANSACTION'/>
   <value enum='11' description='CONVERSION'/>
   <value enum='12' description='AGENT'/>
   <value enum='13' description='TRANSFER_FEE'/>
   <value enum='14' description='SECURITY_LENDING'/>
  </field>
  <field number='140' name='PrevClosePx' type='PRICE'/>
  <field number='141' name='ResetSeqNumFlag' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='142' name='SenderLocationID' type='STRING'/>
  <field number='143' name='TargetLocationID' type='STRING'/>
  <field number='144' name='OnBehalfOfLocationID' type='STRING'/>
  <field number='145' name='DeliverToLocationID' type='STRING'/>
  <field number='146' name='NoRelatedSym' type='NUMINGROUP'/>
  <field number='147' name='Subject' type='STRING'/>
  <field number='148' name='Headline' type='STRING'/>
  <field number='149' name='URLLink' type='STRING'/>
  <field number='150' name='ExecType' type='CHAR'>
   <value enum='0' description='NEW'/>
   <value enum='3' description='DONE_FOR_DAY'/>
   <value enum='4' description='CANCELED'/>
   <value enum='5' description='REPLACED'/>
   <value enum='6' description='PENDING_CANCEL'/>
   <value enum='7' description='STOPPED'/>
   <value enum='8' description='REJECTED'/>
   <value enum='9' description='SUSPENDED'/>
   <value enum='A' description='PENDING_NEW'/>
   <value enum='B' description='CALCULATED'/>
   <value enum='C' description='EXPIRED'/>
   <value enum='D' description='RESTATED'/>
   <value enum='E' description='PENDING_REPLACE'/>
   <value enum='F' description='TRADE'/>
   <value enum='G' description='TRADE_CORRECT'/>
   <value enum='H' description='TRADE_CANCEL'/>
   <value enum='I' description='ORDER_STATUS'/>
   <value enum='J' description='TRADE_IN_A_CLEARING_HOLD'/>
   <value enum='K' description='TRADE_HAS_BEEN_RELEASED_TO_CLEARING'/>
   <value enum='L' description='TRIGGERED_OR_ACTIVATED_BY_SYSTEM'/>
  </field>
  <field number='151' name='LeavesQty' type='QTY'/>
  <field number='152' name='CashOrderQty' type='QTY'/>
  <field number='153' name='AllocAvgPx' type='PRICE'/>
  <field number='154' name='AllocNetMoney' type='AMT'/>
  <field number='155' name='SettlCurrFxRate' type='FLOAT'/>
  <field number='156' name='SettlCurrFxRateCalc' type='CHAR'>
   <value enum='D' description='DIVIDE'/>
   <value enum='M' description='MULTIPLY'/>
  </field>
  <field number='157' name='NumDaysInterest' type='INT'/>
  <field number='158' name='AccruedInterestRate' type='PERCENTAGE'/>
  <field number='159' name='AccruedInterestAmt' type='AMT'/>
  <field number='160' name='SettlInstMode' type='CHAR'>
   <value enum='0' description='DEFAULT'/>
   <value enum='1' description='STANDING_INSTRUCTIONS_PROVIDED'/>
   <value enum='2' description='SPECIFIC_ALLOCATION_ACCOUNT_OVERRIDING'/>
   <value enum='3' description='SPECIFIC_ALLOCATION_ACCOUNT_STANDING'/>
   <value enum='4' description='SPECIFIC_ORDER_FOR_A_SINGLE_ACCOUNT'/>
   <value enum='5' description='REQUEST_REJECT'/>
  </field>
  <field number='161' name='AllocText' type='STRING'/>
  <field number='162' name='SettlInstID' type='STRING'/>
  <field number='163' name='SettlInstTransType' type='CHAR'>
   <value enum='C' description='CANCEL'/>
   <value enum='N' description='NEW'/>
   <value enum='R' description='REPLACE'/>
   <value enum='T' description='RESTATE'/>
  </field>
  <field number='164' name='EmailThreadID' type='STRING'/>
  <field number='165' name='SettlInstSource' type='CHAR'>
   <value enum='1' description='BROKER_CREDIT'/>
   <value enum='2' description='INSTITUTION'/>
   <value enum='3' description='INVESTOR'/>
  </field>
  <field number='167' name='SecurityType' type='STRING'>
   <value enum='AN' description='OTHER_ANTICIPATION_NOTES'/>
   <value enum='BA' description='BANKERS_ACCEPTANCE'/>
   <value enum='BN' description='BANK_NOTES'/>
   <value enum='CB' description='CONVERTIBLE_BOND'/>
   <value enum='CD' description='CERTIFICATE_OF_DEPOSIT'/>
   <value enum='CL' description='CALL_LOANS'/>
   <value enum='CP' description='COMMERCIAL_PAPER'/>
   <value enum='CS' description='COMMON_STOCK'/>
   <value enum='DN' description='DEPOSIT_NOTES'/>
   <value enum='GO' description='GENERAL_OBLIGATION_BONDS'/>
   <value enum='MF' description='MUTUAL_FUND'/>
   <value enum='MT' description='MANDATORY_TENDER'/>
   <value enum='PN' description='PROMISSORY_NOTE'/>
   <value enum='PS' description='PREFERRED_STOCK'/>
   <value enum='TD' description='TIME_DEPOSIT'/>
   <value enum='ABS' description='ASSET_BACKED_SECURITIES'/>
   <value enum='BOX' description='BILL_OF_EXCHANGES'/>
   <value enum='CMO' description='COLLATERALIZED_MORTGAGE_OBLIGATION'/>
   <value enum='CPP' description='CORPORATE_PRIVATE_PLACEMENT'/>
   <value enum='FAC' description='FEDERAL_AGENCY_COUPON'/>
   <value enum='FOR' description='FOREIGN_EXCHANGE_CONTRACT'/>
   <value enum='FUT' description='FUTURE'/>
   <value enum='IET' description='IOETTE_MORTGAGE'/>
   <value enum='LQN' description='LIQUIDITY_NOTE'/>
   <value enum='MBS' description='MORTGAGE_BACKED_SECURITIES'/>
   <value enum='MIO' description='MORTGAGE_INTEREST_ONLY'/>
   <value enum='MPO' description='MORTGAGE_PRINCIPAL_ONLY'/>
   <value enum='MPP' description='MORTGAGE_PRIVATE_PLACEMENT'/>
   <value enum='MPT' description='MISCELLANEOUS_PASS_THROUGH'/>
   <value enum='MTN' description='MEDIUM_TERM_NOTES'/>
   <value enum='OOF' description='OPTIONS_ON_FUTURES'/>
   <value enum='OOP' description='OPTIONS_ON_PHYSICAL'/>
   <value enum='OPT' description='OPTION'/>
   <value enum='PEF' description='PRIVATE_EXPORT_FUNDING'/>
   <value enum='RAN' description='REVENUE_ANTICIPATION_NOTE'/>
   <value enum='REV' description='REVENUE_BONDS'/>
   <value enum='STN' description='SHORT_TERM_LOAN_NOTE'/>
   <value enum='TAN' description='TAX_ANTICIPATION_NOTE'/>
   <value enum='TBA' description='TO_BE_ANNOUNCED'/>
   <value enum='UST' description='US_TREASURY_NOTE_OLD'/>
   <value enum='WAR' description='WARRANT'/>
   <value enum='WLD' description='WILDCARD_ENTRY'/>
   <value enum='XCN' description='EXTENDED_COMM_NOTE'/>
   <value enum='YCD' description='YANKEE_CERTIFICATE_OF_DEPOSIT'/>
   <value enum='CASH' description='CASH'/>
   <value enum='CMBS' description='CORP'/>
   <value enum='COFO' description='CERTIFICATE_OF_OBLIGATION'/>
   <value enum='COFP' description='CERTIFICATE_OF_PARTICIPATION'/>
   <value enum='CORP' description='CORPORATE_BOND'/>
   <value enum='DINP' description='DEBTOR_IN_POSSESSION'/>
   <value enum='DUAL' description='DUAL_CURRENCY'/>
   <value enum='EUCD' description='EURO_CERTIFICATE_OF_DEPOSIT'/>
   <value enum='EUCP' description='EURO_COMMERCIAL_PAPER'/>
   <value enum='FADN' description='FEDERAL_AGENCY_DISCOUNT_NOTE'/>
   <value enum='LOFC' description='LETTER_OF_CREDIT'/>
   <value enum='MLEG' description='MULTILEG_INSTRUMENT'/>
   <value enum='NONE' description='NO_SECURITY_TYPE'/>
   <value enum='PZFJ' description='PLAZOS_FIJOS'/>
   <value enum='REPO' description='REPURCHASE'/>
   <value enum='RVLV' description='REVOLVER_LOAN'/>
   <value enum='TAXA' description='TAX_ALLOCATION'/>
   <value enum='TCAL' description='PRINCIPAL_STRIP_OF_A_CALLABLE_BOND_OR_NOTE'/>
   <value enum='TECP' description='TAX_EXEMPT_COMMERCIAL_PAPER'/>
   <value enum='TERM' description='TERM_LOAN'/>
   <value enum='TINT' description='INTEREST_STRIP_FROM_ANY_BOND_OR_NOTE'/>
   <value enum='TIPS' description='TREASURY_INFLATION_PROTECTED_SECURITIES'/>
   <value enum='TPRN' description='PRINCIPAL_STRIP_FROM_A_NON_CALLABLE_BOND_OR_NOTE'/>
   <value enum='TRAN' description='TAX_REVENUE_ANTICIPATION_NOTE'/>
   <value enum='USTB' description='US_TREASURY_BILL_OLD'/>
   <value enum='VRDN' description='VARIABLE_RATE_DEMAND_NOTE'/>
   <value enum='YANK' description='YANKEE_CORPORATE_BOND'/>
   <value enum='BRADY' description='BRADY_BOND'/>
   <value enum='EUSOV' description='EURO_SOVEREIGNS'/>
   <value enum='ONITE' description='OVERNIGHT'/>
   <value enum='PFAND' description='PFANDBRIEFE'/>
   <value enum='SPCLA' description='SPECIAL_ASSESSMENT'/>
   <value enum='SPCLO' description='SPECIAL_OBLIGATION'/>
   <value enum='SPCLT' description='SPECIAL_TAX'/>
   <value enum='SUPRA' description='USD_SUPRANATIONAL_COUPONS'/>
   <value enum='SWING' description='SWING_LINE_FACILITY'/>
   <value enum='TBILL' description='US_TREASURY_BILL'/>
   <value enum='TBOND' description='US_TREASURY_BOND'/>
   <value enum='TNOTE' description='US_TREASURY_NOTE'/>
   <value enum='BRIDGE' description='BRIDGE_LOAN'/>
   <value enum='EUCORP' description='EURO_CORPORATE_BOND'/>
   <value enum='STRUCT' description='STRUCTURED_NOTES'/>
   <value enum='XLINKD' description='INDEXED_LINKED'/>
   <value enum='AMENDED' description='AMENDED'/>
   <value enum='BUYSELL' description='BUY_SELLBACK'/>
   <value enum='DEFLTED' description='DEFAULTED'/>
   <value enum='EUSUPRA' description='EURO_SUPRANATIONAL_COUPONS'/>
   <value enum='FORWARD' description='FORWARD'/>
   <value enum='MATURED' description='MATURED'/>
   <value enum='REPLACD' description='REPLACED'/>
   <value enum='RETIRED' description='RETIRED'/>
   <value enum='RVLVTRM' description='REVOLVER'/>
   <value enum='SECLOAN' description='SECURITIES_LOAN'/>
   <value enum='WITHDRN' description='WITHDRAWN'/>
   <value enum='SECPLEDGE' description='SECURITIES_PLEDGE'/>
  </field>
  <field number='168' name='EffectiveTime' type='UTCTIMESTAMP'/>
  <field number='169' name='StandInstDbType' type='INT'>
   <value enum='0' description='OTHER'/>
   <value enum='1' description='DTCSID'/>
   <value enum='2' description='THOMSON_ALERT'/>
   <value enum='3' description='A_GLOBAL_CUSTODIAN'/>
   <value enum='4' description='ACCOUNT_NET'/>
  </field>
  <field number='170' name='StandInstDbName' type='STRING'/>
  <field number='171' name='StandInstDbID' type='STRING'/>
  <field number='172' name='SettlDeliveryType' type='INT'>
   <value enum='0' description='VERSUS'/>
   <value enum='1' description='FREE'/>
   <value enum='2' description='TRI_PARTY'/>
   <value enum='3' description='HOLD_IN_CUSTODY'/>
  </field>
  <field number='188' name='BidSpotRate' type='PRICE'/>
  <field number='189' name='BidForwardPoints' type='PRICEOFFSET'/>
  <field number='190' name='OfferSpotRate' type='PRICE'/>
  <field number='191' name='OfferForwardPoints' type='PRICEOFFSET'/>
  <field number='192' name='OrderQty2' type='QTY'/>
  <field number='193' name='SettlDate2' type='LOCALMKTDATE'/>
  <field number='194' name='LastSpotRate' type='PRICE'/>
  <field number='195' name='LastForwardPoints' type='PRICEOFFSET'/>
  <field number='196' name='AllocLinkID' type='STRING'/>
  <field number='197' name='AllocLinkType' type='INT'>
   <value enum='0' description='FX_NETTING'/>
   <value enum='1' description='FX_SWAP'/>
  </field>
  <field number='198' name='SecondaryOrderID' type='STRING'/>
  <field number='199' name='NoIOIQualifiers' type='NUMINGROUP'/>
  <field number='200' name='MaturityMonthYear' type='MONTHYEAR'/>
  <field number='201' name='PutOrCall' type='INT'>
   <value enum='0' description='PUT'/>
   <value enum='1' description='CALL'/>
  </field>
  <field number='202' name='StrikePrice' type='PRICE'/>
  <field number='203' name='CoveredOrUncovered' type='INT'>
   <value enum='0' description='COVERED'/>
   <value enum='1' description='UNCOVERED'/>
  </field>
  <field number='206' name='OptAttribute' type='CHAR'/>
  <field number='207' name='SecurityExchange' type='EXCHANGE'/>
  <field number='208' name='NotifyBrokerOfCredit' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='209' name='AllocHandlInst' type='INT'>
   <value enum='1' description='MATCH'/>
   <value enum='2' description='FORWARD'/>
   <value enum='3' description='FORWARD_AND_MATCH'/>
  </field>
  <field number='210' name='MaxShow' type='QTY'/>
  <field number='211' name='PegOffsetValue' type='FLOAT'/>
  <field number='212' name='XmlDataLen' type='LENGTH'/>
  <field number='213' name='XmlData' type='DATA'/>
  <field number='214' name='SettlInstRefID' type='STRING'/>
  <field number='215' name='NoRoutingIDs' type='NUMINGROUP'/>
  <field number='216' name='RoutingType' type='INT'>
   <value enum='1' description='TARGET_FIRM'/>
   <value enum='2' description='TARGET_LIST'/>
   <value enum='3' description='BLOCK_FIRM'/>
   <value enum='4' description='BLOCK_LIST'/>
  </field>
  <field number='217' name='RoutingID' type='STRING'/>
  <field number='218' name='Spread' type='PRICEOFFSET'/>
  <field number='220' name='BenchmarkCurveCurrency' type='CURRENCY'/>
  <field number='221' name='BenchmarkCurveName' type='STRING'>
   <value enum='SWAP' description='SWAP'/>
   <value enum='EONIA' description='EONIA'/>
   <value enum='LIBID' description='LIBID'/>
   <value enum='LIBOR' description='LIBOR'/>
   <value enum='OTHER' description='OTHER'/>
   <value enum='SONIA' description='SONIA'/>
   <value enum='EUREPO' description='EUREPO'/>
   <value enum='Euribor' description='EURIBOR'/>
   <value enum='MuniAAA' description='MUNI_AAA'/>
   <value enum='Treasury' description='TREASURY'/>
   <value enum='FutureSWAP' description='FUTURE_SWAP'/>
   <value enum='Pfandbriefe' description='PFANDBRIEFE'/>
  </field>
  <field number='222' name='BenchmarkCurvePoint' type='STRING'/>
  <field number='223' name='CouponRate' type='PERCENTAGE'/>
  <field number='224' name='CouponPaymentDate' type='LOCALMKTDATE'/>
  <field number='225' name='IssueDate' type='LOCALMKTDATE'/>
  <field number='226' name='RepurchaseTerm' type='INT'/>
  <field number='227' name='RepurchaseRate' type='PERCENTAGE'/>
  <field number='228' name='Factor' type='FLOAT'/>
  <field number='229' name='TradeOriginationDate' type='LOCALMKTDATE'/>
  <field number='230' name='ExDate' type='LOCALMKTDATE'/>
  <field number='231' name='ContractMultiplier' type='FLOAT'/>
  <field number='232' name='NoStipulations' type='NUMINGROUP'/>
  <field number='233' name='StipulationType' type='STRING'>
   <value enum='ABS' description='ABSOLUTE_PREPAYMENT_SPEED'/>
   <value enum='AMT' description='ALTERNATIVE_MINIMUM_TAX'/>
   <value enum='CPP' description='CONSTANT_PREPAYMENT_PENALTY'/>
   <value enum='CPR' description='CONSTANT_PREPAYMENT_RATE'/>
   <value enum='CPY' description='CONSTANT_PREPAYMENT_YIELD'/>
   <value enum='HEP' description='FINAL_CPR_OF_HOME_EQUITY_PREPAYMENT_CURVE'/>
   <value enum='LOT' description='EXPLICIT_LOT_IDENTIFIER'/>
   <value enum='MAT' description='MATURITY_YEAR_AND_MONTH'/>
   <value enum='MHP' description='PERCENT_OF_MANUFACTURED_HOUSING_PREPAYMENT_CURVE'/>
   <value enum='MPR' description='MONTHLY_PREPAYMENT_RATE'/>
   <value enum='PPC' description='PERCENT_OF_PROSPECTUS_PREPAYMENT_CURVE'/>
   <value enum='PPL' description='POOLS_PER_LOT'/>
   <value enum='PPM' description='POOLS_PER_MILLION'/>
   <value enum='PPT' description='POOLS_PER_TRADE'/>
   <value enum='PSA' description='PERCENT_OF_BMA_PREPAYMENT_CURVE'/>
   <value enum='SMM' description='SINGLE_MONTHLY_MORTALITY'/>
   <value enum='WAC' description='WEIGHTED_AVERAGE_COUPON'/>
   <value enum='WAL' description='WEIGHTED_AVERAGE_LIFE_COUPON'/>
   <value enum='WAM' description='WEIGHTED_AVERAGE_MATURITY'/>
   <value enum='GEOG' description='GEOGRAPHICS'/>
   <value enum='PMAX' description='POOLS_MAXIMUM'/>
   <value enum='PROD' description='PRODUCTION_YEAR'/>
   <value enum='TEXT' description='FREEFORM_TEXT'/>
   <value enum='WALA' description='WEIGHTED_AVERAGE_LOAN_AGE'/>
   <value enum='ISSUE' description='ISSUE_DATE'/>
   <value enum='PRICE' description='PRICE_RANGE'/>
   <value enum='WHOLE' description='WHOLE_POOL'/>
   <value enum='YIELD' description='YIELD_RANGE'/>
   <value enum='BGNCON' description='BARGAIN_CONDITIONS'/>
   <value enum='COUPON' description='COUPON_RANGE'/>
   <value enum='ISSUER' description='ISSUER'/>
   <value enum='LOTVAR' description='LOT_VARIANCE'/>
   <value enum='MINQTY' description='MINIMUM_QUANTITY'/>
   <value enum='PIECES' description='NUMBER_OF_PIECES'/>
   <value enum='RATING' description='RATING_SOURCE_AND_RANGE'/>
   <value enum='SECTOR' description='MARKET_SECTOR'/>
   <value enum='STRUCT' description='STRUCTURE'/>
   <value enum='TRDVAR' description='TRADE_VARIANCE'/>
   <value enum='HAIRCUT' description='VALUATION_DISCOUNT'/>
   <value enum='INSURED' description='INSURED'/>
   <value enum='MAXSUBS' description='MAXIMUM_SUBSTITUTIONS'/>
   <value enum='MINDNOM' description='MINIMUM_DENOMINATION'/>
   <value enum='MININCR' description='MINIMUM_INCREMENT'/>
   <value enum='PAYFREQ' description='PAYMENT_FREQUENCY'/>
   <value enum='PROTECT' description='CALL_PROTECTION'/>
   <value enum='PURPOSE' description='PURPOSE'/>
   <value enum='SECTYPE' description='SECURITY_TYPE_INCLUDED_OR_EXCLUDED'/>
   <value enum='BANKQUAL' description='BANK_QUALIFIED'/>
   <value enum='CURRENCY' description='ISO_CURRENCY_CODE'/>
   <value enum='LOOKBACK' description='LOOKBACK_DAYS'/>
   <value enum='MATURITY' description='MATURITY_RANGE'/>
   <value enum='PXSOURCE' description='BENCHMARK_PRICE_SOURCE'/>
   <value enum='SUBSFREQ' description='SUBSTITUTIONS_FREQUENCY'/>
   <value enum='SUBSLEFT' description='SUBSTITUTIONS_LEFT'/>
   <value enum='AUTOREINV' description='AUTO_REINVESTMENT'/>
   <value enum='ISSUESIZE' description='ISSUE_SIZE_RANGE'/>
   <value enum='PRICEFREQ' description='PRICING_FREQUENCY'/>
   <value enum='CUSTOMDATE' description='CUSTOM_START'/>
   <value enum='REDEMPTION' description='TYPE_OF_REDEMPTION'/>
   <value enum='RESTRICTED' description='RESTRICTED'/>
  </field>
  <field number='234' name='StipulationValue' type='STRING'/>
  <field number='235' name='YieldType' type='STRING'>
   <value enum='PUT' description='YIELD_TO_NEXT_PUT'/>
   <value enum='BOOK' description='BOOK_YIELD'/>
   <value enum='CALL' description='YIELD_TO_NEXT_CALL'/>
   <value enum='MARK' description='MARK_TO_MARKET_YIELD'/>
   <value enum='TRUE' description='TRUE_YIELD'/>
   <value enum='CLOSE' description='CLOSING_YIELD'/>
   <value enum='GROSS' description='TRUE_GROSS_YIELD'/>
   <value enum='WORST' description='YIELD_TO_WORST'/>
   <value enum='ANNUAL' description='ANNUAL_YIELD'/>
   <value enum='CHANGE' description='YIELD_CHANGE_SINCE_CLOSE'/>
   <value enum='SIMPLE' description='SIMPLE_YIELD'/>
   <value enum='TENDER' description='YIELD_TO_TENDER_DATE'/>
   <value enum='ATISSUE' description='YIELD_AT_ISSUE'/>
   <value enum='CURRENT' description='CURRENT_YIELD'/>
   <value enum='OPENAVG' description='OPEN_AVERAGE_YIELD'/>
   <value enum='AFTERTAX' description='AFTER_TAX_YIELD'/>
   <value enum='COMPOUND' description='COMPOUND_YIELD'/>
   <value enum='LASTYEAR' description='CLOSING_YIELD_MOST_RECENT_YEAR'/>
   <value enum='MATURITY' description='YIELD_TO_MATURITY'/>
   <value enum='PROCEEDS' description='PROCEEDS_YIELD'/>
   <value enum='TAXEQUIV' description='TAX_EQUIVALENT_YIELD'/>
   <value enum='GOVTEQUIV' description='GVNT_EQUIVALENT_YIELD'/>
   <value enum='INFLATION' description='YIELD_WITH_INFLATION_ASSUMPTION'/>
   <value enum='LASTCLOSE' description='MOST_RECENT_CLOSING_YIELD'/>
   <value enum='LASTMONTH' description='CLOSING_YIELD_MOST_RECENT_MONTH'/>
   <value enum='PREVCLOSE' description='PREVIOUS_CLOSE_YIELD'/>
   <value enum='VALUE1_32' description='YIELD_VALUE_OF32NDS'/>
   <value enum='NEXTREFUND' description='YIELD_TO_NEXT_REFUND'/>
   <value enum='SEMIANNUAL' description='SEMI_ANNUAL_YIELD'/>
   <value enum='AVGMATURITY' description='YIELD_TO_AVERAGE_MATURITY'/>
   <value enum='LASTQUARTER' description='CLOSING_YIELD_MOST_RECENT_QUARTER'/>
   <value enum='LONGAVGLIFE' description='YIELD_TO_LONGEST_AVERAGE_LIFE'/>
   <value enum='SHORTAVGLIFE' description='YIELD_TO_SHORTEST_AVERAGE_LIFE'/>
   <value enum='INVERSEFLOATER' description='INVERSE_FLOATER_BOND_YIELD'/>
  </field>
  <field number='236' name='Yield' type='PERCENTAGE'/>
  <field number='237' name='TotalTakedown' type='AMT'/>
  <field number='238' name='Concession' type='AMT'/>
  <field number='239' name='RepoCollateralSecurityType' type='STRING'/>
  <field number='240' name='RedemptionDate' type='LOCALMKTDATE'/>
  <field number='241' name='UnderlyingCouponPaymentDate' type='LOCALMKTDATE'/>
  <field number='242' name='UnderlyingIssueDate' type='LOCALMKTDATE'/>
  <field number='243' name='UnderlyingRepoCollateralSecurityType' type='STRING'/>
  <field number='244' name='UnderlyingRepurchaseTerm' type='INT'/>
  <field number='245' name='UnderlyingRepurchaseRate' type='PERCENTAGE'/>
  <field number='246' name='UnderlyingFactor' type='FLOAT'/>
  <field number='247' name='UnderlyingRedemptionDate' type='LOCALMKTDATE'/>
  <field number='248' name='LegCouponPaymentDate' type='LOCALMKTDATE'/>
  <field number='249' name='LegIssueDate' type='LOCALMKTDATE'/>
  <field number='250' name='LegRepoCollateralSecurityType' type='STRING'/>
  <field number='251' name='LegRepurchaseTerm' type='INT'/>
  <field number='252' name='LegRepurchaseRate' type='PERCENTAGE'/>
  <field number='253' name='LegFactor' type='FLOAT'/>
  <field number='254' name='LegRedemptionDate' type='LOCALMKTDATE'/>
  <field number='255' name='CreditRating' type='STRING'/>
  <field number='256' name='UnderlyingCreditRating' type='STRING'/>
  <field number='257' name='LegCreditRating' type='STRING'/>
  <field number='258' name='TradedFlatSwitch' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='259' name='BasisFeatureDate' type='LOCALMKTDATE'/>
  <field number='260' name='BasisFeaturePrice' type='PRICE'/>
  <field number='262' name='MDReqID' type='STRING'/>
  <field number='263' name='SubscriptionRequestType' type='CHAR'>
   <value enum='0' description='SNAPSHOT'/>
   <value enum='1' description='SNAPSHOT_AND_UPDATES'/>
   <value enum='2' description='DISABLE_PREVIOUS_SNAPSHOT'/>
  </field>
  <field number='264' name='MarketDepth' type='INT'/>
  <field number='265' name='MDUpdateType' type='INT'>
   <value enum='0' description='FULL_REFRESH'/>
   <value enum='1' description='INCREMENTAL_REFRESH'/>
  </field>
  <field number='266' name='AggregatedBook' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='267' name='NoMDEntryTypes' type='NUMINGROUP'/>
  <field number='268' name='NoMDEntries' type='NUMINGROUP'/>
  <field number='269' name='MDEntryType' type='CHAR'>
   <value enum='0' description='BID'/>
   <value enum='1' description='OFFER'/>
   <value enum='2' description='TRADE'/>
   <value enum='3' description='INDEX_VALUE'/>
   <value enum='4' description='OPENING_PRICE'/>
   <value enum='5' description='CLOSING_PRICE'/>
   <value enum='6' description='SETTLEMENT_PRICE'/>
   <value enum='7' description='TRADING_SESSION_HIGH_PRICE'/>
   <value enum='8' description='TRADING_SESSION_LOW_PRICE'/>
   <value enum='9' description='TRADING_SESSION_VWAP_PRICE'/>
   <value enum='A' description='IMBALANCE'/>
   <value enum='B' description='TRADE_VOLUME'/>
   <value enum='C' description='OPEN_INTEREST'/>
   <value enum='D' description='COMPOSITE_UNDERLYING_PRICE'/>
   <value enum='E' description='SIMULATED_SELL_PRICE'/>
   <value enum='F' description='SIMULATED_BUY_PRICE'/>
   <value enum='G' description='MARGIN_RATE'/>
   <value enum='H' description='MID_PRICE'/>
   <value enum='J' description='EMPTY_BOOK'/>
   <value enum='K' description='SETTLE_HIGH_PRICE'/>
   <value enum='L' description='SETTLE_LOW_PRICE'/>
   <value enum='M' description='PRIOR_SETTLE_PRICE'/>
   <value enum='N' description='SESSION_HIGH_BID'/>
   <value enum='O' description='SESSION_LOW_OFFER'/>
   <value enum='P' description='EARLY_PRICES'/>
   <value enum='Q' description='AUCTION_CLEARING_PRICE'/>
  </field>
  <field number='270' name='MDEntryPx' type='PRICE'/>
  <field number='271' name='MDEntrySize' type='QTY'/>
  <field number='272' name='MDEntryDate' type='UTCDATEONLY'/>
  <field number='273' name='MDEntryTime' type='UTCTIMEONLY'/>
  <field number='274' name='TickDirection' type='CHAR'>
   <value enum='0' description='PLUS_TICK'/>
   <value enum='1' description='ZERO_PLUS_TICK'/>
   <value enum='2' description='MINUS_TICK'/>
   <value enum='3' description='ZERO_MINUS_TICK'/>
  </field>
  <field number='275' name='MDMkt' type='EXCHANGE'/>
  <field number='276' name='QuoteCondition' type='MULTIPLESTRINGVALUE'>
   <value enum='0' description='RESERVED_SAM'/>
   <value enum='1' description='NO_ACTIVE_SAM'/>
   <value enum='2' description='RESTRICTED'/>
   <value enum='A' description='OPEN'/>
   <value enum='B' description='CLOSED'/>
   <value enum='C' description='EXCHANGE_BEST'/>
   <value enum='D' description='CONSOLIDATED_BEST'/>
   <value enum='E' description='LOCKED'/>
   <value enum='F' description='CROSSED'/>
   <value enum='G' description='DEPTH'/>
   <value enum='H' description='FAST_TRADING'/>
   <value enum='I' description='NON_FIRM'/>
   <value enum='J' description='OUTRIGHT_PRICE'/>
   <value enum='K' description='IMPLIED_PRICE'/>
   <value enum='L' description='MANUAL'/>
   <value enum='M' description='DEPTH_ON_OFFER'/>
   <value enum='N' description='DEPTH_ON_BID'/>
   <value enum='O' description='CLOSING'/>
   <value enum='P' description='NEWS_DISSEMINATION'/>
   <value enum='Q' description='TRADING_RANGE'/>
   <value enum='R' description='ORDER_INFLUX'/>
   <value enum='S' description='DUE_TO_RELATED'/>
   <value enum='T' description='NEWS_PENDING'/>
   <value enum='U' description='ADDITIONAL_INFO'/>
   <value enum='V' description='ADDITIONAL_INFO_DUE_TO_RELATED'/>
   <value enum='W' description='RESUME'/>
   <value enum='X' description='VIEW_OF_COMMON'/>
   <value enum='Y' description='VOLUME_ALERT'/>
   <value enum='Z' description='ORDER_IMBALANCE'/>
   <value enum='a' description='EQUIPMENT_CHANGEOVER'/>
   <value enum='b' description='NO_OPEN'/>
   <value enum='c' description='REGULAR_ETH'/>
   <value enum='d' description='AUTOMATIC_EXECUTION'/>
   <value enum='e' description='AUTOMATIC_EXECUTION_ETH'/>
   <value enum='g' description='INACTIVE_ETH'/>
   <value enum='h' description='ROTATION'/>
   <value enum='i' description='ROTATION_ETH'/>
   <value enum='j' description='HALT'/>
   <value enum='k' description='HALT_ETH'/>
   <value enum='l' description='DUE_TO_NEWS_DISSEMINATION'/>
   <value enum='m' description='DUE_TO_NEWS_PENDING'/>
   <value enum='n' description='TRADING_RESUME'/>
   <value enum='o' description='OUT_OF_SEQUENCE'/>
   <value enum='p' description='BID_SPECIALIST'/>
   <value enum='q' description='OFFER_SPECIALIST'/>
   <value enum='r' description='BID_OFFER_SPECIALIST'/>
   <value enum='s' description='END_OF_DAY_SAM'/>
   <value enum='t' description='FORBIDDEN_SAM'/>
   <value enum='u' description='FROZEN_SAM'/>
   <value enum='v' description='PRE_OPENING_SAM'/>
   <value enum='w' description='OPENING_SAM'/>
   <value enum='x' description='OPEN_SAM'/>
   <value enum='y' description='SURVEILLANCE_SAM'/>
   <value enum='z' description='SUSPENDED_SAM'/>
   <value enum='f ' description='FAST_MARKET_ETH'/>
  </field>
  <field number='277' name='TradeCondition' type='MULTIPLESTRINGVALUE'>
   <value enum='0' description='CANCEL'/>
   <value enum='A' description='CASH'/>
   <value enum='B' description='AVERAGE_PRICE_TRADE'/>
   <value enum='C' description='CASH_TRADE'/>
   <value enum='D' description='NEXT_DAY'/>
   <value enum='E' description='OPENING'/>
   <value enum='F' description='INTRADAY_TRADE_DETAIL'/>
   <value enum='G' description='RULE127_TRADE'/>
   <value enum='H' description='RULE155_TRADE'/>
   <value enum='I' description='SOLD_LAST'/>
   <value enum='J' description='NEXT_DAY_TRADE'/>
   <value enum='K' description='OPENED'/>
   <value enum='L' description='SELLER'/>
   <value enum='M' description='SOLD'/>
   <value enum='N' description='STOPPED_STOCK'/>
   <value enum='P' description='IMBALANCE_MORE_BUYERS'/>
   <value enum='Q' description='IMBALANCE_MORE_SELLERS'/>
   <value enum='R' description='OPENING_PRICE'/>
   <value enum='S' description='BARGAIN_CONDITION'/>
   <value enum='T' description='CONVERTED_PRICE_INDICATOR'/>
   <value enum='U' description='EXCHANGE_LAST'/>
   <value enum='V' description='FINAL_PRICE_OF_SESSION'/>
   <value enum='W' description='EX_PIT'/>
   <value enum='X' description='CROSSED'/>
   <value enum='Y' description='TRADES_RESULTING_FROM_MANUAL'/>
   <value enum='Z' description='TRADES_RESULTING_FROM_INTERMARKET_SWEEP'/>
   <value enum='a' description='VOLUME_ONLY'/>
   <value enum='b' description='DIRECT_PLUS'/>
   <value enum='c' description='ACQUISITION'/>
   <value enum='d' description='BUNCHED'/>
   <value enum='e' description='DISTRIBUTION'/>
   <value enum='f' description='BUNCHED_SALE'/>
   <value enum='g' description='SPLIT_TRADE'/>
   <value enum='h' description='CANCEL_STOPPED'/>
   <value enum='i' description='CANCEL_ETH'/>
   <value enum='j' description='CANCEL_STOPPED_ETH'/>
   <value enum='k' description='OUT_OF_SEQUENCE_ETH'/>
   <value enum='l' description='CANCEL_LAST_ETH'/>
   <value enum='m' description='SOLD_LAST_SALE_ETH'/>
   <value enum='n' description='CANCEL_LAST'/>
   <value enum='o' description='SOLD_LAST_SALE'/>
   <value enum='p' description='CANCEL_OPEN'/>
   <value enum='q' description='CANCEL_OPEN_ETH'/>
   <value enum='r' description='OPENED_SALE_ETH'/>
   <value enum='s' description='CANCEL_ONLY'/>
   <value enum='t' description='CANCEL_ONLY_ETH'/>
   <value enum='u' description='LATE_OPEN_ETH'/>
   <value enum='v' description='AUTO_EXECUTION_ETH'/>
   <value enum='w' description='REOPEN'/>
   <value enum='x' description='REOPEN_ETH'/>
   <value enum='y' description='ADJUSTED'/>
   <value enum='z' description='ADJUSTED_ETH'/>
   <value enum='AA' description='SPREAD'/>
   <value enum='AB' description='SPREAD_ETH'/>
   <value enum='AC' description='STRADDLE'/>
   <value enum='AD' description='STRADDLE_ETH'/>
   <value enum='AE' description='STOPPED'/>
   <value enum='AF' description='STOPPED_ETH'/>
   <value enum='AG' description='REGULAR_ETH'/>
   <value enum='AH' description='COMBO'/>
   <value enum='AI' description='COMBO_ETH'/>
   <value enum='AJ' description='OFFICIAL_CLOSING_PRICE'/>
   <value enum='AK' description='PRIOR_REFERENCE_PRICE'/>
   <value enum='AL' description='STOPPED_SOLD_LAST'/>
   <value enum='AM' description='STOPPED_OUT_OF_SEQUENCE'/>
   <value enum='AN' description='OFFICAL_CLOSING_PRICE'/>
   <value enum='AO' description='CROSSED_OLD'/>
   <value enum='AP' description='FAST_MARKET'/>
   <value enum='AQ' description='AUTOMATIC_EXECUTION'/>
   <value enum='AR' description='FORM_T'/>
   <value enum='AS' description='BASKET_INDEX'/>
   <value enum='AT' description='BURST_BASKET'/>
  </field>
  <field number='278' name='MDEntryID' type='STRING'/>
  <field number='279' name='MDUpdateAction' type='CHAR'>
   <value enum='0' description='NEW'/>
   <value enum='1' description='CHANGE'/>
   <value enum='2' description='DELETE'/>
   <value enum='3' description='DELETE_THRU'/>
   <value enum='4' description='DELETE_FROM'/>
  </field>
  <field number='280' name='MDEntryRefID' type='STRING'/>
  <field number='281' name='MDReqRejReason' type='CHAR'>
   <value enum='0' description='UNKNOWN_SYMBOL'/>
   <value enum='1' description='DUPLICATE_MD_REQ_ID'/>
   <value enum='2' description='INSUFFICIENT_BANDWIDTH'/>
   <value enum='3' description='INSUFFICIENT_PERMISSIONS'/>
   <value enum='4' description='UNSUPPORTED_SUBSCRIPTION_REQUEST_TYPE'/>
   <value enum='5' description='UNSUPPORTED_MARKET_DEPTH'/>
   <value enum='6' description='UNSUPPORTED_MD_UPDATE_TYPE'/>
   <value enum='7' description='UNSUPPORTED_AGGREGATED_BOOK'/>
   <value enum='8' description='UNSUPPORTED_MD_ENTRY_TYPE'/>
   <value enum='9' description='UNSUPPORTED_TRADING_SESSION_ID'/>
   <value enum='A' description='UNSUPPORTED_SCOPE'/>
   <value enum='B' description='UNSUPPORTED_OPEN_CLOSE_SETTLE_FLAG'/>
   <value enum='C' description='UNSUPPORTED_MD_IMPLICIT_DELETE'/>
   <value enum='D' description='INSUFFICIENT_CREDIT'/>
  </field>
  <field number='282' name='MDEntryOriginator' type='STRING'/>
  <field number='283' name='LocationID' type='STRING'/>
  <field number='284' name='DeskID' type='STRING'/>
  <field number='285' name='DeleteReason' type='CHAR'>
   <value enum='0' description='CANCELLATION'/>
   <value enum='1' description='ERROR'/>
  </field>
  <field number='286' name='OpenCloseSettlFlag' type='MULTIPLECHARVALUE'>
   <value enum='0' description='DAILY_OPEN'/>
   <value enum='1' description='SESSION_OPEN'/>
   <value enum='2' description='DELIVERY_SETTLEMENT_ENTRY'/>
   <value enum='3' description='EXPECTED_ENTRY'/>
   <value enum='4' description='ENTRY_FROM_PREVIOUS_BUSINESS_DAY'/>
   <value enum='5' description='THEORETICAL_PRICE_VALUE'/>
  </field>
  <field number='287' name='SellerDays' type='INT'/>
  <field number='288' name='MDEntryBuyer' type='STRING'/>
  <field number='289' name='MDEntrySeller' type='STRING'/>
  <field number='290' name='MDEntryPositionNo' type='INT'/>
  <field number='291' name='FinancialStatus' type='MULTIPLECHARVALUE'>
   <value enum='1' description='BANKRUPT'/>
   <value enum='2' description='PENDING_DELISTING'/>
   <value enum='3' description='RESTRICTED'/>
  </field>
  <field number='292' name='CorporateAction' type='MULTIPLECHARVALUE'>
   <value enum='A' description='EX_DIVIDEND'/>
   <value enum='B' description='EX_DISTRIBUTION'/>
   <value enum='C' description='EX_RIGHTS'/>
   <value enum='D' description='NEW'/>
   <value enum='E' description='EX_INTEREST'/>
   <value enum='F' description='CASH_DIVIDEND'/>
   <value enum='G' description='STOCK_DIVIDEND'/>
   <value enum='H' description='NON_INTEGER_STOCK_SPLIT'/>
   <value enum='I' description='REVERSE_STOCK_SPLIT'/>
   <value enum='J' description='STANDARD_INTEGER_STOCK_SPLIT'/>
   <value enum='K' description='POSITION_CONSOLIDATION'/>
   <value enum='L' description='LIQUIDATION_REORGANIZATION'/>
   <value enum='M' description='MERGER_REORGANIZATION'/>
   <value enum='N' description='RIGHTS_OFFERING'/>
   <value enum='O' description='SHAREHOLDER_MEETING'/>
   <value enum='P' description='SPINOFF'/>
   <value enum='Q' description='TENDER_OFFER'/>
   <value enum='R' description='WARRANT'/>
   <value enum='S' description='SPECIAL_ACTION'/>
   <value enum='T' description='SYMBOL_CONVERSION'/>
   <value enum='U' description='CUSIP'/>
   <value enum='V' description='LEAP_ROLLOVER'/>
  </field>
  <field number='293' name='DefBidSize' type='QTY'/>
  <field number='294' name='DefOfferSize' type='QTY'/>
  <field number='295' name='NoQuoteEntries' type='NUMINGROUP'/>
  <field number='296' name='NoQuoteSets' type='NUMINGROUP'/>
  <field number='297' name='QuoteStatus' type='INT'>
   <value enum='0' description='ACCEPTED'/>
   <value enum='1' description='CANCEL_FOR_SYMBOL'/>
   <value enum='2' description='CANCELED_FOR_SECURITY_TYPE'/>
   <value enum='3' description='CANCELED_FOR_UNDERLYING'/>
   <value enum='4' description='CANCELED_ALL'/>
   <value enum='5' description='REJECTED'/>
   <value enum='6' description='REMOVED_FROM_MARKET'/>
   <value enum='7' description='EXPIRED'/>
   <value enum='8' description='QUERY'/>
   <value enum='9' description='QUOTE_NOT_FOUND'/>
   <value enum='10' description='PENDING'/>
   <value enum='11' description='PASS'/>
   <value enum='12' description='LOCKED_MARKET_WARNING'/>
   <value enum='13' description='CROSS_MARKET_WARNING'/>
   <value enum='14' description='CANCELED_DUE_TO_LOCK_MARKET'/>
   <value enum='15' description='CANCELED_DUE_TO_CROSS_MARKET'/>
  </field>
  <field number='298' name='QuoteCancelType' type='INT'>
   <value enum='1' description='CANCEL_FOR_ONE_OR_MORE_SECURITIES'/>
   <value enum='2' description='CANCEL_FOR_SECURITY_TYPE'/>
   <value enum='3' description='CANCEL_FOR_UNDERLYING_SECURITY'/>
   <value enum='4' description='CANCEL_ALL_QUOTES'/>
   <value enum='5' description='CANCEL_QUOTE_SPECIFIED_IN_QUOTE_ID'/>
  </field>
  <field number='299' name='QuoteEntryID' type='STRING'/>
  <field number='300' name='QuoteRejectReason' type='INT'>
   <value enum='1' description='UNKNOWN_SYMBOL'/>
   <value enum='2' description='EXCHANGE'/>
   <value enum='3' description='QUOTE_REQUEST_EXCEEDS_LIMIT'/>
   <value enum='4' description='TOO_LATE_TO_ENTER'/>
   <value enum='5' description='UNKNOWN_QUOTE'/>
   <value enum='6' description='DUPLICATE_QUOTE'/>
   <value enum='7' description='INVALID_BID'/>
   <value enum='8' description='INVALID_PRICE'/>
   <value enum='9' description='NOT_AUTHORIZED_TO_QUOTE_SECURITY'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='301' name='QuoteResponseLevel' type='INT'>
   <value enum='0' description='NO_ACKNOWLEDGEMENT'/>
   <value enum='1' description='ACKNOWLEDGE_ONLY_NEGATIVE_OR_ERRONEOUS_QUOTES'/>
   <value enum='2' description='ACKNOWLEDGE_EACH_QUOTE_MESSAGE'/>
  </field>
  <field number='302' name='QuoteSetID' type='STRING'/>
  <field number='303' name='QuoteRequestType' type='INT'>
   <value enum='1' description='MANUAL'/>
   <value enum='2' description='AUTOMATIC'/>
  </field>
  <field number='304' name='TotNoQuoteEntries' type='INT'/>
  <field number='305' name='UnderlyingSecurityIDSource' type='STRING'/>
  <field number='306' name='UnderlyingIssuer' type='STRING'/>
  <field number='307' name='UnderlyingSecurityDesc' type='STRING'/>
  <field number='308' name='UnderlyingSecurityExchange' type='EXCHANGE'/>
  <field number='309' name='UnderlyingSecurityID' type='STRING'/>
  <field number='310' name='UnderlyingSecurityType' type='STRING'/>
  <field number='311' name='UnderlyingSymbol' type='STRING'/>
  <field number='312' name='UnderlyingSymbolSfx' type='STRING'/>
  <field number='313' name='UnderlyingMaturityMonthYear' type='MONTHYEAR'/>
  <field number='315' name='UnderlyingPutOrCall' type='INT'/>
  <field number='316' name='UnderlyingStrikePrice' type='PRICE'/>
  <field number='317' name='UnderlyingOptAttribute' type='CHAR'/>
  <field number='318' name='UnderlyingCurrency' type='CURRENCY'/>
  <field number='320' name='SecurityReqID' type='STRING'/>
  <field number='321' name='SecurityRequestType' type='INT'>
   <value enum='0' description='REQUEST_SECURITY_IDENTITY_AND_SPECIFICATIONS'/>
   <value enum='1' description='REQUEST_SECURITY_IDENTITY_FOR_SPECIFICATIONS'/>
   <value enum='2' description='REQUEST_LIST_SECURITY_TYPES'/>
   <value enum='3' description='REQUEST_LIST_SECURITIES'/>
  </field>
  <field number='322' name='SecurityResponseID' type='STRING'/>
  <field number='323' name='SecurityResponseType' type='INT'>
   <value enum='1' description='ACCEPT_AS_IS'/>
   <value enum='2' description='ACCEPT_WITH_REVISIONS'/>
   <value enum='3' description='LIST_OF_SECURITY_TYPES_RETURNED_PER_REQUEST'/>
   <value enum='4' description='LIST_OF_SECURITIES_RETURNED_PER_REQUEST'/>
   <value enum='5' description='REJECT_SECURITY_PROPOSAL'/>
   <value enum='6' description='CANNOT_MATCH_SELECTION_CRITERIA'/>
  </field>
  <field number='324' name='SecurityStatusReqID' type='STRING'/>
  <field number='325' name='UnsolicitedIndicator' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='326' name='SecurityTradingStatus' type='INT'>
   <value enum='1' description='OPENING_DELAY'/>
   <value enum='2' description='TRADING_HALT'/>
   <value enum='3' description='RESUME'/>
   <value enum='4' description='NO_OPEN'/>
   <value enum='5' description='PRICE_INDICATION'/>
   <value enum='6' description='TRADING_RANGE_INDICATION'/>
   <value enum='7' description='MARKET_IMBALANCE_BUY'/>
   <value enum='8' description='MARKET_IMBALANCE_SELL'/>
   <value enum='9' description='MARKET_ON_CLOSE_IMBALANCE_BUY'/>
   <value enum='10' description='MARKET_ON_CLOSE_IMBALANCE_SELL'/>
   <value enum='12' description='NO_MARKET_IMBALANCE'/>
   <value enum='13' description='NO_MARKET_ON_CLOSE_IMBALANCE'/>
   <value enum='14' description='ITS_PRE_OPENING'/>
   <value enum='15' description='NEW_PRICE_INDICATION'/>
   <value enum='16' description='TRADE_DISSEMINATION_TIME'/>
   <value enum='17' description='READY_TO_TRADE'/>
   <value enum='18' description='NOT_AVAILABLE_FOR_TRADING'/>
   <value enum='19' description='NOT_TRADED_ON_THIS_MARKET'/>
   <value enum='20' description='UNKNOWN_OR_INVALID'/>
   <value enum='21' description='PRE_OPEN'/>
   <value enum='22' description='OPENING_ROTATION'/>
   <value enum='23' description='FAST_MARKET'/>
  </field>
  <field number='327' name='HaltReasonChar' type='CHAR'>
   <value enum='D' description='NEWS_DISSEMINATION'/>
   <value enum='E' description='ORDER_INFLUX'/>
   <value enum='I' description='ORDER_IMBALANCE'/>
   <value enum='M' description='ADDITIONAL_INFORMATION'/>
   <value enum='P' description='NEW_PENDING'/>
   <value enum='X' description='EQUIPMENT_CHANGEOVER'/>
  </field>
  <field number='328' name='InViewOfCommon' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='329' name='DueToRelated' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='330' name='BuyVolume' type='QTY'/>
  <field number='331' name='SellVolume' type='QTY'/>
  <field number='332' name='HighPx' type='PRICE'/>
  <field number='333' name='LowPx' type='PRICE'/>
  <field number='334' name='Adjustment' type='INT'>
   <value enum='1' description='CANCEL'/>
   <value enum='2' description='ERROR'/>
   <value enum='3' description='CORRECTION'/>
  </field>
  <field number='335' name='TradSesReqID' type='STRING'/>
  <field number='336' name='TradingSessionID' type='STRING'/>
  <field number='337' name='ContraTrader' type='STRING'/>
  <field number='338' name='TradSesMethod' type='INT'>
   <value enum='1' description='ELECTRONIC'/>
   <value enum='2' description='OPEN_OUTCRY'/>
   <value enum='3' description='TWO_PARTY'/>
  </field>
  <field number='339' name='TradSesMode' type='INT'>
   <value enum='1' description='TESTING'/>
   <value enum='2' description='SIMULATED'/>
   <value enum='3' description='PRODUCTION'/>
  </field>
  <field number='340' name='TradSesStatus' type='INT'>
   <value enum='0' description='UNKNOWN'/>
   <value enum='1' description='HALTED'/>
   <value enum='2' description='OPEN'/>
   <value enum='3' description='CLOSED'/>
   <value enum='4' description='PRE_OPEN'/>
   <value enum='5' description='PRE_CLOSE'/>
   <value enum='6' description='REQUEST_REJECTED'/>
  </field>
  <field number='341' name='TradSesStartTime' type='UTCTIMESTAMP'/>
  <field number='342' name='TradSesOpenTime' type='UTCTIMESTAMP'/>
  <field number='343' name='TradSesPreCloseTime' type='UTCTIMESTAMP'/>
  <field number='344' name='TradSesCloseTime' type='UTCTIMESTAMP'/>
  <field number='345' name='TradSesEndTime' type='UTCTIMESTAMP'/>
  <field number='346' name='NumberOfOrders' type='INT'/>
  <field number='347' name='MessageEncoding' type='STRING'/>
  <field number='348' name='EncodedIssuerLen' type='LENGTH'/>
  <field number='349' name='EncodedIssuer' type='DATA'/>
  <field number='350' name='EncodedSecurityDescLen' type='LENGTH'/>
  <field number='351' name='EncodedSecurityDesc' type='DATA'/>
  <field number='352' name='EncodedListExecInstLen' type='LENGTH'/>
  <field number='353' name='EncodedListExecInst' type='DATA'/>
  <field number='354' name='EncodedTextLen' type='LENGTH'/>
  <field number='355' name='EncodedText' type='DATA'/>
  <field number='356' name='EncodedSubjectLen' type='LENGTH'/>
  <field number='357' name='EncodedSubject' type='DATA'/>
  <field number='358' name='EncodedHeadlineLen' type='LENGTH'/>
  <field number='359' name='EncodedHeadline' type='DATA'/>
  <field number='360' name='EncodedAllocTextLen' type='LENGTH'/>
  <field number='361' name='EncodedAllocText' type='DATA'/>
  <field number='362' name='EncodedUnderlyingIssuerLen' type='LENGTH'/>
  <field number='363' name='EncodedUnderlyingIssuer' type='DATA'/>
  <field number='364' name='EncodedUnderlyingSecurityDescLen' type='LENGTH'/>
  <field number='365' name='EncodedUnderlyingSecurityDesc' type='DATA'/>
  <field number='366' name='AllocPrice' type='PRICE'/>
  <field number='367' name='QuoteSetValidUntilTime' type='UTCTIMESTAMP'/>
  <field number='368' name='QuoteEntryRejectReason' type='INT'/>
  <field number='369' name='LastMsgSeqNumProcessed' type='SEQNUM'/>
  <field number='371' name='RefTagID' type='INT'/>
  <field number='372' name='RefMsgType' type='STRING'/>
  <field number='373' name='SessionRejectReason' type='INT'>
   <value enum='0' description='INVALID_TAG_NUMBER'/>
   <value enum='1' description='REQUIRED_TAG_MISSING'/>
   <value enum='2' description='TAG_NOT_DEFINED_FOR_THIS_MESSAGE_TYPE'/>
   <value enum='3' description='UNDEFINED_TAG'/>
   <value enum='4' description='TAG_SPECIFIED_WITHOUT_A_VALUE'/>
   <value enum='5' description='VALUE_IS_INCORRECT'/>
   <value enum='6' description='INCORRECT_DATA_FORMAT_FOR_VALUE'/>
   <value enum='7' description='DECRYPTION_PROBLEM'/>
   <value enum='8' description='SIGNATURE_PROBLEM'/>
   <value enum='9' description='COMPID_PROBLEM'/>
   <value enum='10' description='SENDINGTIME_ACCURACY_PROBLEM'/>
   <value enum='11' description='INVALID_MSGTYPE'/>
   <value enum='12' description='XML_VALIDATION_ERROR'/>
   <value enum='13' description='TAG_APPEARS_MORE_THAN_ONCE'/>
   <value enum='14' description='TAG_SPECIFIED_OUT_OF_REQUIRED_ORDER'/>
   <value enum='15' description='REPEATING_GROUP_FIELDS_OUT_OF_ORDER'/>
   <value enum='16' description='INCORRECT_NUMINGROUP_COUNT_FOR_REPEATING_GROUP'/>
   <value enum='17' description='NON_DATA_VALUE_INCLUDES_FIELD_DELIMITER'/>
   <value enum='18' description='INVALID_UNSUPPORTED_APPLICATION_VERSION'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='374' name='BidRequestTransType' type='CHAR'>
   <value enum='C' description='CANCEL'/>
   <value enum='N' description='NEW'/>
  </field>
  <field number='375' name='ContraBroker' type='STRING'/>
  <field number='376' name='ComplianceID' type='STRING'/>
  <field number='377' name='SolicitedFlag' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='378' name='ExecRestatementReason' type='INT'>
   <value enum='0' description='GT_CORPORATE_ACTION'/>
   <value enum='1' description='GT_RENEWAL'/>
   <value enum='2' description='VERBAL_CHANGE'/>
   <value enum='3' description='REPRICING_OF_ORDER'/>
   <value enum='4' description='BROKER_OPTION'/>
   <value enum='5' description='PARTIAL_DECLINE_OF_ORDER_QTY'/>
   <value enum='6' description='CANCEL_ON_TRADING_HALT'/>
   <value enum='7' description='CANCEL_ON_SYSTEM_FAILURE'/>
   <value enum='8' description='MARKET'/>
   <value enum='9' description='CANCELED'/>
   <value enum='10' description='WAREHOUSE_RECAP'/>
   <value enum='11' description='PEG_REFRESH'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='379' name='BusinessRejectRefID' type='STRING'/>
  <field number='380' name='BusinessRejectReason' type='INT'>
   <value enum='0' description='OTHER'/>
   <value enum='1' description='UNKNOWN_ID'/>
   <value enum='2' description='UNKNOWN_SECURITY'/>
   <value enum='3' description='UNSUPPORTED_MESSAGE_TYPE'/>
   <value enum='4' description='APPLICATION_NOT_AVAILABLE'/>
   <value enum='5' description='CONDITIONALLY_REQUIRED_FIELD_MISSING'/>
   <value enum='6' description='NOT_AUTHORIZED'/>
   <value enum='7' description='DELIVER_TO_FIRM_NOT_AVAILABLE_AT_THIS_TIME'/>
   <value enum='18' description='INVALID_PRICE_INCREMENT'/>
  </field>
  <field number='381' name='GrossTradeAmt' type='AMT'/>
  <field number='382' name='NoContraBrokers' type='NUMINGROUP'/>
  <field number='383' name='MaxMessageSize' type='LENGTH'/>
  <field number='384' name='NoMsgTypes' type='NUMINGROUP'/>
  <field number='385' name='MsgDirection' type='CHAR'>
   <value enum='R' description='RECEIVE'/>
   <value enum='S' description='SEND'/>
  </field>
  <field number='386' name='NoTradingSessions' type='NUMINGROUP'/>
  <field number='387' name='TotalVolumeTraded' type='QTY'/>
  <field number='388' name='DiscretionInst' type='CHAR'>
   <value enum='0' description='RELATED_TO_DISPLAYED_PRICE'/>
   <value enum='1' description='RELATED_TO_MARKET_PRICE'/>
   <value enum='2' description='RELATED_TO_PRIMARY_PRICE'/>
   <value enum='3' description='RELATED_TO_LOCAL_PRIMARY_PRICE'/>
   <value enum='4' description='RELATED_TO_MIDPOINT_PRICE'/>
   <value enum='5' description='RELATED_TO_LAST_TRADE_PRICE'/>
   <value enum='6' description='RELATED_TO_VWAP'/>
   <value enum='7' description='AVERAGE_PRICE_GUARANTEE'/>
  </field>
  <field number='389' name='DiscretionOffsetValue' type='FLOAT'/>
  <field number='390' name='BidID' type='STRING'/>
  <field number='391' name='ClientBidID' type='STRING'/>
  <field number='392' name='ListName' type='STRING'/>
  <field number='393' name='TotNoRelatedSym' type='INT'/>
  <field number='394' name='BidType' type='INT'>
   <value enum='1' description='NON_DISCLOSED'/>
   <value enum='2' description='DISCLOSED'/>
   <value enum='3' description='NO_BIDDING_PROCESS'/>
  </field>
  <field number='395' name='NumTickets' type='INT'/>
  <field number='396' name='SideValue1' type='AMT'/>
  <field number='397' name='SideValue2' type='AMT'/>
  <field number='398' name='NoBidDescriptors' type='NUMINGROUP'/>
  <field number='399' name='BidDescriptorType' type='INT'>
   <value enum='1' description='SECTOR'/>
   <value enum='2' description='COUNTRY'/>
   <value enum='3' description='INDEX'/>
  </field>
  <field number='400' name='BidDescriptor' type='STRING'/>
  <field number='401' name='SideValueInd' type='INT'>
   <value enum='1' description='SIDE_VALUE1'/>
   <value enum='2' description='SIDE_VALUE2'/>
  </field>
  <field number='402' name='LiquidityPctLow' type='PERCENTAGE'/>
  <field number='403' name='LiquidityPctHigh' type='PERCENTAGE'/>
  <field number='404' name='LiquidityValue' type='AMT'/>
  <field number='405' name='EFPTrackingError' type='PERCENTAGE'/>
  <field number='406' name='FairValue' type='AMT'/>
  <field number='407' name='OutsideIndexPct' type='PERCENTAGE'/>
  <field number='408' name='ValueOfFutures' type='AMT'/>
  <field number='409' name='LiquidityIndType' type='INT'>
   <value enum='1' description='FIVE_DAY_MOVING_AVERAGE'/>
   <value enum='2' description='TWENTY_DAY_MOVING_AVERAGE'/>
   <value enum='3' description='NORMAL_MARKET_SIZE'/>
   <value enum='4' description='OTHER'/>
  </field>
  <field number='410' name='WtAverageLiquidity' type='PERCENTAGE'/>
  <field number='411' name='ExchangeForPhysical' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='412' name='OutMainCntryUIndex' type='AMT'/>
  <field number='413' name='CrossPercent' type='PERCENTAGE'/>
  <field number='414' name='ProgRptReqs' type='INT'>
   <value enum='1' description='BUY_SIDE_REQUESTS'/>
   <value enum='2' description='SELL_SIDE_SENDS'/>
   <value enum='3' description='REAL_TIME_EXECUTION_REPORTS'/>
  </field>
  <field number='415' name='ProgPeriodInterval' type='INT'/>
  <field number='416' name='IncTaxInd' type='INT'>
   <value enum='1' description='NET'/>
   <value enum='2' description='GROSS'/>
  </field>
  <field number='417' name='NumBidders' type='INT'/>
  <field number='418' name='BidTradeType' type='CHAR'>
   <value enum='A' description='AGENCY'/>
   <value enum='G' description='VWAP_GUARANTEE'/>
   <value enum='J' description='GUARANTEED_CLOSE'/>
   <value enum='R' description='RISK_TRADE'/>
  </field>
  <field number='419' name='BasisPxType' type='CHAR'>
   <value enum='2' description='CLOSING_PRICE_AT_MORNING_SESSION'/>
   <value enum='3' description='CLOSING_PRICE'/>
   <value enum='4' description='CURRENT_PRICE'/>
   <value enum='5' description='SQ'/>
   <value enum='6' description='VWAP_THROUGH_A_DAY'/>
   <value enum='7' description='VWAP_THROUGH_A_MORNING_SESSION'/>
   <value enum='8' description='VWAP_THROUGH_AN_AFTERNOON_SESSION'/>
   <value enum='9' description='VWAP_THROUGH_A_DAY_EXCEPT'/>
   <value enum='A' description='VWAP_THROUGH_A_MORNING_SESSION_EXCEPT'/>
   <value enum='B' description='VWAP_THROUGH_AN_AFTERNOON_SESSION_EXCEPT'/>
   <value enum='C' description='STRIKE'/>
   <value enum='D' description='OPEN'/>
   <value enum='Z' description='OTHERS'/>
  </field>
  <field number='420' name='NoBidComponents' type='NUMINGROUP'/>
  <field number='421' name='Country' type='COUNTRY'/>
  <field number='422' name='TotNoStrikes' type='INT'/>
  <field number='423' name='PriceType' type='INT'>
   <value enum='1' description='PERCENTAGE'/>
   <value enum='2' description='PER_UNIT'/>
   <value enum='3' description='FIXED_AMOUNT'/>
   <value enum='4' description='DISCOUNT'/>
   <value enum='5' description='PREMIUM'/>
   <value enum='6' description='SPREAD'/>
   <value enum='7' description='TED_PRICE'/>
   <value enum='8' description='TED_YIELD'/>
   <value enum='9' description='YIELD'/>
   <value enum='10' description='FIXED_CABINET_TRADE_PRICE'/>
   <value enum='11' description='VARIABLE_CABINET_TRADE_PRICE'/>
   <value enum='13' description='PRODUCT_TICKS_IN_HALFS'/>
   <value enum='14' description='PRODUCT_TICKS_IN_FOURTHS'/>
   <value enum='15' description='PRODUCT_TICKS_IN_EIGHTS'/>
   <value enum='16' description='PRODUCT_TICKS_IN_SIXTEENTHS'/>
   <value enum='17' description='PRODUCT_TICKS_IN_THIRTY_SECONDS'/>
   <value enum='18' description='PRODUCT_TICKS_IN_SIXTY_FORTHS'/>
   <value enum='19' description='PRODUCT_TICKS_IN_ONE_TWENTY_EIGHTS'/>
  </field>
  <field number='424' name='DayOrderQty' type='QTY'/>
  <field number='425' name='DayCumQty' type='QTY'/>
  <field number='426' name='DayAvgPx' type='PRICE'/>
  <field number='427' name='GTBookingInst' type='INT'>
   <value enum='0' description='BOOK_OUT_ALL_TRADES_ON_DAY_OF_EXECUTION'/>
   <value enum='1' description='ACCUMULATE_UNTIL_FILLED_OR_EXPIRED'/>
   <value enum='2' description='ACCUMULATE_UNTIL_VERBALLY_NOTIFIED_OTHERWISE'/>
  </field>
  <field number='428' name='NoStrikes' type='NUMINGROUP'/>
  <field number='429' name='ListStatusType' type='INT'>
   <value enum='1' description='ACK'/>
   <value enum='2' description='RESPONSE'/>
   <value enum='3' description='TIMED'/>
   <value enum='4' description='EXEC_STARTED'/>
   <value enum='5' description='ALL_DONE'/>
   <value enum='6' description='ALERT'/>
  </field>
  <field number='430' name='NetGrossInd' type='INT'>
   <value enum='1' description='NET'/>
   <value enum='2' description='GROSS'/>
  </field>
  <field number='431' name='ListOrderStatus' type='INT'>
   <value enum='1' description='IN_BIDDING_PROCESS'/>
   <value enum='2' description='RECEIVED_FOR_EXECUTION'/>
   <value enum='3' description='EXECUTING'/>
   <value enum='4' description='CANCELLING'/>
   <value enum='5' description='ALERT'/>
   <value enum='6' description='ALL_DONE'/>
   <value enum='7' description='REJECT'/>
  </field>
  <field number='432' name='ExpireDate' type='LOCALMKTDATE'/>
  <field number='433' name='ListExecInstType' type='CHAR'>
   <value enum='1' description='IMMEDIATE'/>
   <value enum='2' description='WAIT_FOR_INSTRUCTION'/>
   <value enum='3' description='SELL_DRIVEN'/>
   <value enum='4' description='BUY_DRIVEN_CASH_TOP_UP'/>
   <value enum='5' description='BUY_DRIVEN_CASH_WITHDRAW'/>
  </field>
  <field number='434' name='CxlRejResponseTo' type='CHAR'>
   <value enum='1' description='ORDER_CANCEL_REQUEST'/>
   <value enum='2' description='ORDER_CANCEL'/>
  </field>
  <field number='435' name='UnderlyingCouponRate' type='PERCENTAGE'/>
  <field number='436' name='UnderlyingContractMultiplier' type='FLOAT'/>
  <field number='437' name='ContraTradeQty' type='QTY'/>
  <field number='438' name='ContraTradeTime' type='UTCTIMESTAMP'/>
  <field number='441' name='LiquidityNumSecurities' type='INT'/>
  <field number='442' name='MultiLegReportingType' type='CHAR'>
   <value enum='1' description='SINGLE_SECURITY'/>
   <value enum='2' description='INDIVIDUAL_LEG_OF_A_MULTI_LEG_SECURITY'/>
   <value enum='3' description='MULTI_LEG_SECURITY'/>
  </field>
  <field number='443' name='StrikeTime' type='UTCTIMESTAMP'/>
  <field number='444' name='ListStatusText' type='STRING'/>
  <field number='445' name='EncodedListStatusTextLen' type='LENGTH'/>
  <field number='446' name='EncodedListStatusText' type='DATA'/>
  <field number='447' name='PartyIDSource' type='CHAR'>
   <value enum='1' description='KOREAN_INVESTOR_ID'/>
   <value enum='2' description='TAIWANESE_FOREIGN_INVESTOR_ID'/>
   <value enum='3' description='TAIWANESE_TRADING_ACCT'/>
   <value enum='4' description='MALAYSIAN_CENTRAL_DEPOSITORY'/>
   <value enum='5' description='CHINESE_INVESTOR_ID'/>
   <value enum='6' description='UK_NATIONAL_INSURANCE_OR_PENSION_NUMBER'/>
   <value enum='7' description='US_SOCIAL_SECURITY_NUMBER'/>
   <value enum='8' description='US_EMPLOYER_OR_TAX_ID_NUMBER'/>
   <value enum='9' description='AUSTRALIAN_BUSINESS_NUMBER'/>
   <value enum='A' description='AUSTRALIAN_TAX_FILE_NUMBER'/>
   <value enum='B' description='BIC'/>
   <value enum='C' description='GENERAL_IDENTIFIER'/>
   <value enum='D' description='PROPRIETARY'/>
   <value enum='E' description='ISO_COUNTRY_CODE'/>
   <value enum='F' description='SETTLEMENT_ENTITY_LOCATION'/>
   <value enum='G' description='MIC'/>
   <value enum='H' description='CSD_PARTICIPANT'/>
   <value enum='I' description='ISITC_ACRONYM'/>
  </field>
  <field number='448' name='PartyID' type='STRING'/>
  <field number='451' name='NetChgPrevDay' type='PRICEOFFSET'/>
  <field number='452' name='PartyRole' type='INT'>
   <value enum='1' description='EXECUTING_FIRM'/>
   <value enum='2' description='BROKER_OF_CREDIT'/>
   <value enum='3' description='CLIENT_ID'/>
   <value enum='4' description='CLEARING_FIRM'/>
   <value enum='5' description='INVESTOR_ID'/>
   <value enum='6' description='INTRODUCING_FIRM'/>
   <value enum='7' description='ENTERING_FIRM'/>
   <value enum='8' description='LOCATE'/>
   <value enum='9' description='FUND_MANAGER_CLIENT_ID'/>
   <value enum='10' description='SETTLEMENT_LOCATION'/>
   <value enum='11' description='ORDER_ORIGINATION_TRADER'/>
   <value enum='12' description='EXECUTING_TRADER'/>
   <value enum='13' description='ORDER_ORIGINATION_FIRM'/>
   <value enum='14' description='GIVEUP_CLEARING_FIRM'/>
   <value enum='15' description='CORRESPONDANT_CLEARING_FIRM'/>
   <value enum='16' description='EXECUTING_SYSTEM'/>
   <value enum='17' description='CONTRA_FIRM'/>
   <value enum='18' description='CONTRA_CLEARING_FIRM'/>
   <value enum='19' description='SPONSORING_FIRM'/>
   <value enum='20' description='UNDERLYING_CONTRA_FIRM'/>
   <value enum='21' description='CLEARING_ORGANIZATION'/>
   <value enum='22' description='EXCHANGE'/>
   <value enum='24' description='CUSTOMER_ACCOUNT'/>
   <value enum='25' description='CORRESPONDENT_CLEARING_ORGANIZATION'/>
   <value enum='26' description='CORRESPONDENT_BROKER'/>
   <value enum='27' description='BUYER'/>
   <value enum='28' description='CUSTODIAN'/>
   <value enum='29' description='INTERMEDIARY'/>
   <value enum='30' description='AGENT'/>
   <value enum='31' description='SUB_CUSTODIAN'/>
   <value enum='32' description='BENEFICIARY'/>
   <value enum='33' description='INTERESTED_PARTY'/>
   <value enum='34' description='REGULATORY_BODY'/>
   <value enum='35' description='LIQUIDITY_PROVIDER'/>
   <value enum='36' description='ENTERING_TRADER'/>
   <value enum='37' description='CONTRA_TRADER'/>
   <value enum='38' description='POSITION_ACCOUNT'/>
   <value enum='39' description='CONTRA_INVESTOR_ID'/>
   <value enum='40' description='TRANSFER_TO_FIRM'/>
   <value enum='41' description='CONTRA_POSITION_ACCOUNT'/>
   <value enum='42' description='CONTRA_EXCHANGE'/>
   <value enum='43' description='INTERNAL_CARRY_ACCOUNT'/>
   <value enum='44' description='ORDER_ENTRY_OPERATOR_ID'/>
   <value enum='45' description='SECONDARY_ACCOUNT_NUMBER'/>
   <value enum='46' description='FOREIGN_FIRM'/>
   <value enum='47' description='THIRD_PARTY_ALLOCATION_FIRM'/>
   <value enum='48' description='CLAIMING_ACCOUNT'/>
   <value enum='49' description='ASSET_MANAGER'/>
   <value enum='50' description='PLEDGOR_ACCOUNT'/>
   <value enum='51' description='PLEDGEE_ACCOUNT'/>
   <value enum='52' description='LARGE_TRADER_REPORTABLE_ACCOUNT'/>
   <value enum='53' description='TRADER_MNEMONIC'/>
   <value enum='54' description='SENDER_LOCATION'/>
   <value enum='55' description='SESSION_ID'/>
   <value enum='56' description='ACCEPTABLE_COUNTERPARTY'/>
   <value enum='57' description='UNACCEPTABLE_COUNTERPARTY'/>
   <value enum='58' description='ENTERING_UNIT'/>
   <value enum='59' description='EXECUTING_UNIT'/>
   <value enum='60' description='INTRODUCING_BROKER'/>
   <value enum='61' description='QUOTE_ORIGINATOR'/>
   <value enum='62' description='REPORT_ORIGINATOR'/>
   <value enum='63' description='SYSTEMATIC_INTERNALISER'/>
   <value enum='64' description='MULTILATERAL_TRADING_FACILITY'/>
   <value enum='65' description='REGULATED_MARKET'/>
   <value enum='66' description='MARKET_MAKER'/>
   <value enum='67' description='INVESTMENT_FIRM'/>
   <value enum='68' description='HOST_COMPETENT_AUTHORITY'/>
   <value enum='69' description='HOME_COMPETENT_AUTHORITY'/>
   <value enum='70' description='COMPETENT_AUTHORITY_LIQUIDITY'/>
   <value enum='71' description='COMPETENT_AUTHORITY_TRANSACTION_VENUE'/>
   <value enum='72' description='REPORTING_INTERMEDIARY'/>
   <value enum='73' description='EXECUTION_VENUE'/>
   <value enum='74' description='MARKET_DATA_ENTRY_ORIGINATOR'/>
   <value enum='75' description='LOCATION_ID'/>
   <value enum='76' description='DESK_ID'/>
   <value enum='77' description='MARKET_DATA_MARKET'/>
   <value enum='78' description='ALLOCATION_ENTITY'/>
  </field>
  <field number='453' name='NoPartyIDs' type='NUMINGROUP'/>
  <field number='454' name='NoSecurityAltID' type='NUMINGROUP'/>
  <field number='455' name='SecurityAltID' type='STRING'/>
  <field number='456' name='SecurityAltIDSource' type='STRING'/>
  <field number='457' name='NoUnderlyingSecurityAltID' type='NUMINGROUP'/>
  <field number='458' name='UnderlyingSecurityAltID' type='STRING'/>
  <field number='459' name='UnderlyingSecurityAltIDSource' type='STRING'/>
  <field number='460' name='Product' type='INT'>
   <value enum='1' description='AGENCY'/>
   <value enum='2' description='COMMODITY'/>
   <value enum='3' description='CORPORATE'/>
   <value enum='4' description='CURRENCY'/>
   <value enum='5' description='EQUITY'/>
   <value enum='6' description='GOVERNMENT'/>
   <value enum='7' description='INDEX'/>
   <value enum='8' description='LOAN'/>
   <value enum='9' description='MONEYMARKET'/>
   <value enum='10' description='MORTGAGE'/>
   <value enum='11' description='MUNICIPAL'/>
   <value enum='12' description='OTHER'/>
   <value enum='13' description='FINANCING'/>
  </field>
  <field number='461' name='CFICode' type='STRING'/>
  <field number='462' name='UnderlyingProduct' type='INT'/>
  <field number='463' name='UnderlyingCFICode' type='STRING'/>
  <field number='464' name='TestMessageIndicator' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='466' name='BookingRefID' type='STRING'/>
  <field number='467' name='IndividualAllocID' type='STRING'/>
  <field number='468' name='RoundingDirection' type='CHAR'>
   <value enum='0' description='ROUND_TO_NEAREST'/>
   <value enum='1' description='ROUND_DOWN'/>
   <value enum='2' description='ROUND_UP'/>
  </field>
  <field number='469' name='RoundingModulus' type='FLOAT'/>
  <field number='470' name='CountryOfIssue' type='COUNTRY'/>
  <field number='471' name='StateOrProvinceOfIssue' type='STRING'/>
  <field number='472' name='LocaleOfIssue' type='STRING'/>
  <field number='473' name='NoRegistDtls' type='NUMINGROUP'/>
  <field number='474' name='MailingDtls' type='STRING'/>
  <field number='475' name='InvestorCountryOfResidence' type='COUNTRY'/>
  <field number='476' name='PaymentRef' type='STRING'/>
  <field number='477' name='DistribPaymentMethod' type='INT'>
   <value enum='1' description='CREST'/>
   <value enum='2' description='NSCC'/>
   <value enum='3' description='EUROCLEAR'/>
   <value enum='4' description='CLEARSTREAM'/>
   <value enum='5' description='CHEQUE'/>
   <value enum='6' description='TELEGRAPHIC_TRANSFER'/>
   <value enum='7' description='FED_WIRE'/>
   <value enum='8' description='DIRECT_CREDIT'/>
   <value enum='9' description='ACH_CREDIT'/>
   <value enum='10' description='BPAY'/>
   <value enum='11' description='HIGH_VALUE_CLEARING_SYSTEM_HVACS'/>
   <value enum='12' description='REINVEST_IN_FUND'/>
  </field>
  <field number='478' name='CashDistribCurr' type='CURRENCY'/>
  <field number='479' name='CommCurrency' type='CURRENCY'/>
  <field number='480' name='CancellationRights' type='CHAR'>
   <value enum='M' description='NO_WAIVER_AGREEMENT'/>
   <value enum='N' description='NO_EXECUTION_ONLY'/>
   <value enum='O' description='NO_INSTITUTIONAL'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='481' name='MoneyLaunderingStatus' type='CHAR'>
   <value enum='1' description='EXEMPT_BELOW_LIMIT'/>
   <value enum='2' description='EXEMPT_MONEY_TYPE'/>
   <value enum='3' description='EXEMPT_AUTHORISED'/>
   <value enum='N' description='NOT_CHECKED'/>
   <value enum='Y' description='PASSED'/>
  </field>
  <field number='482' name='MailingInst' type='STRING'/>
  <field number='483' name='TransBkdTime' type='UTCTIMESTAMP'/>
  <field number='484' name='ExecPriceType' type='CHAR'>
   <value enum='B' description='BID_PRICE'/>
   <value enum='C' description='CREATION_PRICE'/>
   <value enum='D' description='CREATION_PRICE_PLUS_ADJUSTMENT_PERCENT'/>
   <value enum='E' description='CREATION_PRICE_PLUS_ADJUSTMENT_AMOUNT'/>
   <value enum='O' description='OFFER_PRICE'/>
   <value enum='P' description='OFFER_PRICE_MINUS_ADJUSTMENT_PERCENT'/>
   <value enum='Q' description='OFFER_PRICE_MINUS_ADJUSTMENT_AMOUNT'/>
   <value enum='S' description='SINGLE_PRICE'/>
  </field>
  <field number='485' name='ExecPriceAdjustment' type='FLOAT'/>
  <field number='486' name='DateOfBirth' type='LOCALMKTDATE'/>
  <field number='487' name='TradeReportTransType' type='INT'>
   <value enum='0' description='NEW'/>
   <value enum='1' description='CANCEL'/>
   <value enum='2' description='REPLACE'/>
   <value enum='3' description='RELEASE'/>
   <value enum='4' description='REVERSE'/>
   <value enum='5' description='CANCEL_DUE_TO_BACK_OUT_OF_TRADE'/>
  </field>
  <field number='488' name='CardHolderName' type='STRING'/>
  <field number='489' name='CardNumber' type='STRING'/>
  <field number='490' name='CardExpDate' type='LOCALMKTDATE'/>
  <field number='491' name='CardIssNum' type='STRING'/>
  <field number='492' name='PaymentMethod' type='INT'>
   <value enum='1' description='CREST'/>
   <value enum='2' description='NSCC'/>
   <value enum='3' description='EUROCLEAR'/>
   <value enum='4' description='CLEARSTREAM'/>
   <value enum='5' description='CHEQUE'/>
   <value enum='6' description='TELEGRAPHIC_TRANSFER'/>
   <value enum='7' description='FED_WIRE'/>
   <value enum='8' description='DEBIT_CARD'/>
   <value enum='9' description='DIRECT_DEBIT'/>
   <value enum='10' description='DIRECT_CREDIT'/>
   <value enum='11' description='CREDIT_CARD'/>
   <value enum='12' description='ACH_DEBIT'/>
   <value enum='13' description='ACH_CREDIT'/>
   <value enum='14' description='BPAY'/>
   <value enum='15' description='HIGH_VALUE_CLEARING_SYSTEM'/>
  </field>
  <field number='493' name='RegistAcctType' type='STRING'/>
  <field number='494' name='Designation' type='STRING'/>
  <field number='495' name='TaxAdvantageType' type='INT'>
   <value enum='0' description='NONE'/>
   <value enum='1' description='MAXI_ISA'/>
   <value enum='2' description='TESSA'/>
   <value enum='3' description='MINI_CASH_ISA'/>
   <value enum='4' description='MINI_STOCKS_AND_SHARES_ISA'/>
   <value enum='5' description='MINI_INSURANCE_ISA'/>
   <value enum='6' description='CURRENT_YEAR_PAYMENT'/>
   <value enum='7' description='PRIOR_YEAR_PAYMENT'/>
   <value enum='8' description='ASSET_TRANSFER'/>
   <value enum='9' description='EMPLOYEE_PRIOR_YEAR'/>
   <value enum='10' description='EMPLOYEE_CURRENT_YEAR'/>
   <value enum='11' description='EMPLOYER_PRIOR_YEAR'/>
   <value enum='12' description='EMPLOYER_CURRENT_YEAR'/>
   <value enum='13' description='NON_FUND_PROTOTYPE_IRA'/>
   <value enum='14' description='NON_FUND_QUALIFIED_PLAN'/>
   <value enum='15' description='DEFINED_CONTRIBUTION_PLAN'/>
   <value enum='16' description='IRA'/>
   <value enum='17' description='IRA_ROLLOVER'/>
   <value enum='18' description='KEOGH'/>
   <value enum='19' description='PROFIT_SHARING_PLAN'/>
   <value enum='20' description='US401_K'/>
   <value enum='21' description='SELF_DIRECTED_IRA'/>
   <value enum='22' description='US403B'/>
   <value enum='23' description='US457'/>
   <value enum='24' description='ROTH_IRA_PROTOTYPE'/>
   <value enum='25' description='ROTH_IRA_NON_PROTOTYPE'/>
   <value enum='26' description='ROTH_CONVERSION_IRA_PROTOTYPE'/>
   <value enum='27' description='ROTH_CONVERSION_IRA_NON_PROTOTYPE'/>
   <value enum='28' description='EDUCATION_IRA_PROTOTYPE'/>
   <value enum='29' description='EDUCATION_IRA_NON_PROTOTYPE'/>
   <value enum='999' description='OTHER'/>
  </field>
  <field number='496' name='RegistRejReasonText' type='STRING'/>
  <field number='497' name='FundRenewWaiv' type='CHAR'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='498' name='CashDistribAgentName' type='STRING'/>
  <field number='499' name='CashDistribAgentCode' type='STRING'/>
  <field number='500' name='CashDistribAgentAcctNumber' type='STRING'/>
  <field number='501' name='CashDistribPayRef' type='STRING'/>
  <field number='502' name='CashDistribAgentAcctName' type='STRING'/>
  <field number='503' name='CardStartDate' type='LOCALMKTDATE'/>
  <field number='504' name='PaymentDate' type='LOCALMKTDATE'/>
  <field number='505' name='PaymentRemitterID' type='STRING'/>
  <field number='506' name='RegistStatus' type='CHAR'>
   <value enum='A' description='ACCEPTED'/>
   <value enum='H' description='HELD'/>
   <value enum='N' description='REMINDER'/>
   <value enum='R' description='REJECTED'/>
  </field>
  <field number='507' name='RegistRejReasonCode' type='INT'>
   <value enum='1' description='INVALID_ACCOUNT_TYPE'/>
   <value enum='2' description='INVALID_TAX_EXEMPT_TYPE'/>
   <value enum='3' description='INVALID_OWNERSHIP_TYPE'/>
   <value enum='4' description='NO_REG_DETAILS'/>
   <value enum='5' description='INVALID_REG_SEQ_NO'/>
   <value enum='6' description='INVALID_REG_DETAILS'/>
   <value enum='7' description='INVALID_MAILING_DETAILS'/>
   <value enum='8' description='INVALID_MAILING_INSTRUCTIONS'/>
   <value enum='9' description='INVALID_INVESTOR_ID'/>
   <value enum='10' description='INVALID_INVESTOR_ID_SOURCE'/>
   <value enum='11' description='INVALID_DATE_OF_BIRTH'/>
   <value enum='12' description='INVALID_COUNTRY'/>
   <value enum='13' description='INVALID_DISTRIB_INSTNS'/>
   <value enum='14' description='INVALID_PERCENTAGE'/>
   <value enum='15' description='INVALID_PAYMENT_METHOD'/>
   <value enum='16' description='INVALID_ACCOUNT_NAME'/>
   <value enum='17' description='INVALID_AGENT_CODE'/>
   <value enum='18' description='INVALID_ACCOUNT_NUM'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='508' name='RegistRefID' type='STRING'/>
  <field number='509' name='RegistDtls' type='STRING'/>
  <field number='510' name='NoDistribInsts' type='NUMINGROUP'/>
  <field number='511' name='RegistEmail' type='STRING'/>
  <field number='512' name='DistribPercentage' type='PERCENTAGE'/>
  <field number='513' name='RegistID' type='STRING'/>
  <field number='514' name='RegistTransType' type='CHAR'>
   <value enum='0' description='NEW'/>
   <value enum='1' description='REPLACE'/>
   <value enum='2' description='CANCEL'/>
  </field>
  <field number='515' name='ExecValuationPoint' type='UTCTIMESTAMP'/>
  <field number='516' name='OrderPercent' type='PERCENTAGE'/>
  <field number='517' name='OwnershipType' type='CHAR'>
   <value enum='2' description='JOINT_TRUSTEES'/>
   <value enum='J' description='JOINT_INVESTORS'/>
   <value enum='T' description='TENANTS_IN_COMMON'/>
  </field>
  <field number='518' name='NoContAmts' type='NUMINGROUP'/>
  <field number='519' name='ContAmtType' type='INT'>
   <value enum='1' description='COMMISSION_AMOUNT'/>
   <value enum='2' description='COMMISSION_PERCENT'/>
   <value enum='3' description='INITIAL_CHARGE_AMOUNT'/>
   <value enum='4' description='INITIAL_CHARGE_PERCENT'/>
   <value enum='5' description='DISCOUNT_AMOUNT'/>
   <value enum='6' description='DISCOUNT_PERCENT'/>
   <value enum='7' description='DILUTION_LEVY_AMOUNT'/>
   <value enum='8' description='DILUTION_LEVY_PERCENT'/>
   <value enum='9' description='EXIT_CHARGE_AMOUNT'/>
   <value enum='10' description='EXIT_CHARGE_PERCENT'/>
   <value enum='11' description='FUND_BASED_RENEWAL_COMMISSION_PERCENT'/>
   <value enum='12' description='PROJECTED_FUND_VALUE'/>
   <value enum='13' description='FUND_BASED_RENEWAL_COMMISSION_ON_ORDER'/>
   <value enum='14' description='FUND_BASED_RENEWAL_COMMISSION_ON_FUND'/>
   <value enum='15' description='NET_SETTLEMENT_AMOUNT'/>
  </field>
  <field number='520' name='ContAmtValue' type='FLOAT'/>
  <field number='521' name='ContAmtCurr' type='CURRENCY'/>
  <field number='522' name='OwnerType' type='INT'>
   <value enum='1' description='INDIVIDUAL_INVESTOR'/>
   <value enum='2' description='PUBLIC_COMPANY'/>
   <value enum='3' description='PRIVATE_COMPANY'/>
   <value enum='4' description='INDIVIDUAL_TRUSTEE'/>
   <value enum='5' description='COMPANY_TRUSTEE'/>
   <value enum='6' description='PENSION_PLAN'/>
   <value enum='7' description='CUSTODIAN_UNDER_GIFTS_TO_MINORS_ACT'/>
   <value enum='8' description='TRUSTS'/>
   <value enum='9' description='FIDUCIARIES'/>
   <value enum='10' description='NETWORKING_SUB_ACCOUNT'/>
   <value enum='11' description='NON_PROFIT_ORGANIZATION'/>
   <value enum='12' description='CORPORATE_BODY'/>
   <value enum='13' description='NOMINEE'/>
  </field>
  <field number='523' name='PartySubID' type='STRING'/>
  <field number='524' name='NestedPartyID' type='STRING'/>
  <field number='525' name='NestedPartyIDSource' type='CHAR'/>
  <field number='526' name='SecondaryClOrdID' type='STRING'/>
  <field number='527' name='SecondaryExecID' type='STRING'/>
  <field number='528' name='OrderCapacity' type='CHAR'>
   <value enum='A' description='AGENCY'/>
   <value enum='G' description='PROPRIETARY'/>
   <value enum='I' description='INDIVIDUAL'/>
   <value enum='P' description='PRINCIPAL'/>
   <value enum='R' description='RISKLESS_PRINCIPAL'/>
   <value enum='W' description='AGENT_FOR_OTHER_MEMBER'/>
  </field>
  <field number='529' name='OrderRestrictions' type='MULTIPLECHARVALUE'>
   <value enum='1' description='PROGRAM_TRADE'/>
   <value enum='2' description='INDEX_ARBITRAGE'/>
   <value enum='3' description='NON_INDEX_ARBITRAGE'/>
   <value enum='4' description='COMPETING_MARKET_MAKER'/>
   <value enum='5' description='ACTING_AS_MARKET_MAKER_OR_SPECIALIST_IN_SECURITY'/>
   <value enum='6' description='ACTING_AS_MARKET_MAKER_OR_SPECIALIST_IN_UNDERLYING'/>
   <value enum='7' description='FOREIGN_ENTITY'/>
   <value enum='8' description='EXTERNAL_MARKET_PARTICIPANT'/>
   <value enum='9' description='EXTERNAL_INTER_CONNECTED_MARKET_LINKAGE'/>
   <value enum='A' description='RISKLESS_ARBITRAGE'/>
  </field>
  <field number='530' name='MassCancelRequestType' type='CHAR'>
   <value enum='1' description='CANCEL_ORDERS_FOR_A_SECURITY'/>
   <value enum='2' description='CANCEL_ORDERS_FOR_AN_UNDERLYING_SECURITY'/>
   <value enum='3' description='CANCEL_ORDERS_FOR_A_PRODUCT'/>
   <value enum='4' description='CANCEL_ORDERS_FOR_ACFI_CODE'/>
   <value enum='5' description='CANCEL_ORDERS_FOR_A_SECURITY_TYPE'/>
   <value enum='6' description='CANCEL_ORDERS_FOR_A_TRADING_SESSION'/>
   <value enum='7' description='CANCEL_ALL_ORDERS'/>
  </field>
  <field number='531' name='MassCancelResponse' type='CHAR'>
   <value enum='0' description='CANCEL_REQUEST_REJECTED'/>
   <value enum='1' description='CANCEL_ORDERS_FOR_A_SECURITY'/>
   <value enum='2' description='CANCEL_ORDERS_FOR_AN_UNDERLYING_SECURITY'/>
   <value enum='3' description='CANCEL_ORDERS_FOR_A_PRODUCT'/>
   <value enum='4' description='CANCEL_ORDERS_FOR_ACFI_CODE'/>
   <value enum='5' description='CANCEL_ORDERS_FOR_A_SECURITY_TYPE'/>
   <value enum='6' description='CANCEL_ORDERS_FOR_A_TRADING_SESSION'/>
   <value enum='7' description='CANCEL_ALL_ORDERS'/>
  </field>
  <field number='532' name='MassCancelRejectReason' type='INT'>
   <value enum='0' description='MASS_CANCEL_NOT_SUPPORTED'/>
   <value enum='1' description='INVALID_OR_UNKNOWN_SECURITY'/>
   <value enum='2' description='INVALID_OR_UNKOWN_UNDERLYING_SECURITY'/>
   <value enum='3' description='INVALID_OR_UNKNOWN_PRODUCT'/>
   <value enum='4' description='INVALID_OR_UNKNOWN_CFI_CODE'/>
   <value enum='5' description='INVALID_OR_UNKNOWN_SECURITY_TYPE'/>
   <value enum='6' description='INVALID_OR_UNKNOWN_TRADING_SESSION'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='533' name='TotalAffectedOrders' type='INT'/>
  <field number='534' name='NoAffectedOrders' type='NUMINGROUP'/>
  <field number='535' name='AffectedOrderID' type='STRING'/>
  <field number='536' name='AffectedSecondaryOrderID' type='STRING'/>
  <field number='537' name='QuoteType' type='INT'>
   <value enum='0' description='INDICATIVE'/>
   <value enum='1' description='TRADEABLE'/>
   <value enum='2' description='RESTRICTED_TRADEABLE'/>
   <value enum='3' description='COUNTER'/>
  </field>
  <field number='538' name='NestedPartyRole' type='INT'/>
  <field number='539' name='NoNestedPartyIDs' type='NUMINGROUP'/>
  <field number='540' name='TotalAccruedInterestAmt' type='AMT'/>
  <field number='541' name='MaturityDate' type='LOCALMKTDATE'/>
  <field number='542' name='UnderlyingMaturityDate' type='LOCALMKTDATE'/>
  <field number='543' name='InstrRegistry' type='STRING'/>
  <field number='544' name='CashMargin' type='CHAR'>
   <value enum='1' description='CASH'/>
   <value enum='2' description='MARGIN_OPEN'/>
   <value enum='3' description='MARGIN_CLOSE'/>
  </field>
  <field number='545' name='NestedPartySubID' type='STRING'/>
  <field number='546' name='Scope' type='MULTIPLECHARVALUE'>
   <value enum='1' description='LOCAL_MARKET'/>
   <value enum='2' description='NATIONAL'/>
   <value enum='3' description='GLOBAL'/>
  </field>
  <field number='547' name='MDImplicitDelete' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='548' name='CrossID' type='STRING'/>
  <field number='549' name='CrossType' type='INT'>
   <value enum='1' description='CROSS_AON'/>
   <value enum='2' description='CROSS_IOC'/>
   <value enum='3' description='CROSS_ONE_SIDE'/>
   <value enum='4' description='CROSS_SAME_PRICE'/>
  </field>
  <field number='550' name='CrossPrioritization' type='INT'>
   <value enum='0' description='NONE'/>
   <value enum='1' description='BUY_SIDE_IS_PRIORITIZED'/>
   <value enum='2' description='SELL_SIDE_IS_PRIORITIZED'/>
  </field>
  <field number='551' name='OrigCrossID' type='STRING'/>
  <field number='552' name='NoSides' type='NUMINGROUP'>
   <value enum='1' description='ONE_SIDE'/>
   <value enum='2' description='BOTH_SIDES'/>
  </field>
  <field number='553' name='Username' type='STRING'/>
  <field number='554' name='Password' type='STRING'/>
  <field number='555' name='NoLegs' type='NUMINGROUP'/>
  <field number='556' name='LegCurrency' type='CURRENCY'/>
  <field number='557' name='TotNoSecurityTypes' type='INT'/>
  <field number='558' name='NoSecurityTypes' type='NUMINGROUP'/>
  <field number='559' name='SecurityListRequestType' type='INT'>
   <value enum='0' description='SYMBOL'/>
   <value enum='1' description='SECURITY_TYPE_AND'/>
   <value enum='2' description='PRODUCT'/>
   <value enum='3' description='TRADING_SESSION_ID'/>
   <value enum='4' description='ALL_SECURITIES'/>
  </field>
  <field number='560' name='SecurityRequestResult' type='INT'>
   <value enum='0' description='VALID_REQUEST'/>
   <value enum='1' description='INVALID_OR_UNSUPPORTED_REQUEST'/>
   <value enum='2' description='NO_INSTRUMENTS_FOUND'/>
   <value enum='3' description='NOT_AUTHORIZED_TO_RETRIEVE_INSTRUMENT_DATA'/>
   <value enum='4' description='INSTRUMENT_DATA_TEMPORARILY_UNAVAILABLE'/>
   <value enum='5' description='REQUEST_FOR_INSTRUMENT_DATA_NOT_SUPPORTED'/>
  </field>
  <field number='561' name='RoundLot' type='QTY'/>
  <field number='562' name='MinTradeVol' type='QTY'/>
  <field number='563' name='MultiLegRptTypeReq' type='INT'>
   <value enum='0' description='REPORT_BY_MULITLEG_SECURITY_ONLY'/>
   <value enum='1' description='REPORT_BY_MULTILEG_SECURITY_AND_INSTRUMENT_LEGS'/>
   <value enum='2' description='REPORT_BY_INSTRUMENT_LEGS_ONLY'/>
  </field>
  <field number='564' name='LegPositionEffect' type='CHAR'/>
  <field number='565' name='LegCoveredOrUncovered' type='INT'/>
  <field number='566' name='LegPrice' type='PRICE'/>
  <field number='567' name='TradSesStatusRejReason' type='INT'>
   <value enum='1' description='UNKNOWN_OR_INVALID_TRADING_SESSION_ID'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='568' name='TradeRequestID' type='STRING'/>
  <field number='569' name='TradeRequestType' type='INT'>
   <value enum='0' description='ALL_TRADES'/>
   <value enum='1' description='MATCHED_TRADES_MATCHING_CRITERIA'/>
   <value enum='2' description='UNMATCHED_TRADES_THAT_MATCH_CRITERIA'/>
   <value enum='3' description='UNREPORTED_TRADES_THAT_MATCH_CRITERIA'/>
   <value enum='4' description='ADVISORIES_THAT_MATCH_CRITERIA'/>
  </field>
  <field number='570' name='PreviouslyReported' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='571' name='TradeReportID' type='STRING'/>
  <field number='572' name='TradeReportRefID' type='STRING'/>
  <field number='573' name='MatchStatus' type='CHAR'>
   <value enum='0' description='COMPARED'/>
   <value enum='1' description='UNCOMPARED'/>
   <value enum='2' description='ADVISORY_OR_ALERT'/>
  </field>
  <field number='574' name='MatchType' type='STRING'>
   <value enum='1' description='OMX_ONE_PARTY_TRADE_REPORT'/>
   <value enum='2' description='OMX_TWO_PARTY_TRADE_REPORT'/>
   <value enum='3' description='OMX_CONFIRMED_TRADE_REPORT'/>
   <value enum='4' description='OMX_AUTO_MATCH'/>
   <value enum='5' description='OMX_CROSS_AUCTION'/>
   <value enum='6' description='OMX_COUNTER_ORDER_SELECTION'/>
   <value enum='7' description='OMX_CALL_AUCTION'/>
   <value enum='60' description='ONE_PARTY_PRIVATELY_NEGOTIATED_TRADE_REPORT'/>
   <value enum='61' description='TWO_PARTY_PRIVATELY_NEGOTIATED_TRADE_REPORT'/>
   <value enum='62' description='CONTINUOUS_AUTO_MATCH'/>
   <value enum='63' description='CROSS_AUCTION'/>
   <value enum='64' description='COUNTER_ORDER_SELECTION'/>
   <value enum='65' description='CALL_AUCTION'/>
   <value enum='A1' description='EXACT_MATCH_PLUS4_BADGES_EXEC_TIME'/>
   <value enum='A2' description='EXACT_MATCH_PLUS4_BADGES'/>
   <value enum='A3' description='EXACT_MATCH_PLUS2_BADGES_EXEC_TIME'/>
   <value enum='A4' description='EXACT_MATCH_PLUS2_BADGES'/>
   <value enum='A5' description='EXACT_MATCH_PLUS_EXEC_TIME'/>
   <value enum='AQ' description='STAMPED_ADVISORIES_OR_SPECIALIST_ACCEPTS'/>
   <value enum='M1' description='EXACT_MATCH_MINUS_BADGES_TIMES'/>
   <value enum='M2' description='SUMMARIZED_MATCH_MINUS_BADGES_TIMES'/>
   <value enum='M3' description='ACT_ACCEPTED_TRADE'/>
   <value enum='M4' description='ACT_DEFAULT_TRADE'/>
   <value enum='M5' description='ACT_DEFAULT_AFTER_M2'/>
   <value enum='M6' description='ACTM6_MATCH'/>
   <value enum='MT' description='OCS_LOCKED_IN'/>
   <value enum='S1' description='A1_EXACT_MATCH_SUMMARIZED_QUANTITY'/>
   <value enum='S2' description='A2_EXACT_MATCH_SUMMARIZED_QUANTITY'/>
   <value enum='S3' description='A3_EXACT_MATCH_SUMMARIZED_QUANTITY'/>
   <value enum='S4' description='A4_EXACT_MATCH_SUMMARIZED_QUANTITY'/>
   <value enum='S5' description='A5_EXACT_MATCH_SUMMARIZED_QUANTITY'/>
  </field>
  <field number='575' name='OddLot' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='576' name='NoClearingInstructions' type='NUMINGROUP'/>
  <field number='577' name='ClearingInstruction' type='INT'>
   <value enum='0' description='PROCESS_NORMALLY'/>
   <value enum='1' description='EXCLUDE_FROM_ALL_NETTING'/>
   <value enum='2' description='BILATERAL_NETTING_ONLY'/>
   <value enum='3' description='EX_CLEARING'/>
   <value enum='4' description='SPECIAL_TRADE'/>
   <value enum='5' description='MULTILATERAL_NETTING'/>
   <value enum='6' description='CLEAR_AGAINST_CENTRAL_COUNTERPARTY'/>
   <value enum='7' description='EXCLUDE_FROM_CENTRAL_COUNTERPARTY'/>
   <value enum='8' description='MANUAL_MODE'/>
   <value enum='9' description='AUTOMATIC_POSTING_MODE'/>
   <value enum='10' description='AUTOMATIC_GIVE_UP_MODE'/>
   <value enum='11' description='QUALIFIED_SERVICE_REPRESENTATIVE_QSR'/>
   <value enum='12' description='CUSTOMER_TRADE'/>
   <value enum='13' description='SELF_CLEARING'/>
  </field>
  <field number='578' name='TradeInputSource' type='STRING'/>
  <field number='579' name='TradeInputDevice' type='STRING'/>
  <field number='580' name='NoDates' type='NUMINGROUP'/>
  <field number='581' name='AccountType' type='INT'>
   <value enum='1' description='CARRIED_CUSTOMER_SIDE'/>
   <value enum='2' description='CARRIED_NON_CUSTOMER_SIDE'/>
   <value enum='3' description='HOUSE_TRADER'/>
   <value enum='4' description='FLOOR_TRADER'/>
   <value enum='6' description='CARRIED_NON_CUSTOMER_SIDE_CROSS_MARGINED'/>
   <value enum='7' description='HOUSE_TRADER_CROSS_MARGINED'/>
   <value enum='8' description='JOINT_BACK_OFFICE_ACCOUNT'/>
  </field>
  <field number='582' name='CustOrderCapacity' type='INT'>
   <value enum='1' description='MEMBER_TRADING_FOR_THEIR_OWN_ACCOUNT'/>
   <value enum='2' description='CLEARING_FIRM_TRADING_FOR_ITS_PROPRIETARY_ACCOUNT'/>
   <value enum='3' description='MEMBER_TRADING_FOR_ANOTHER_MEMBER'/>
   <value enum='4' description='ALL_OTHER'/>
  </field>
  <field number='583' name='ClOrdLinkID' type='STRING'/>
  <field number='584' name='MassStatusReqID' type='STRING'/>
  <field number='585' name='MassStatusReqType' type='INT'>
   <value enum='1' description='STATUS_FOR_ORDERS_FOR_A_SECURITY'/>
   <value enum='2' description='STATUS_FOR_ORDERS_FOR_AN_UNDERLYING_SECURITY'/>
   <value enum='3' description='STATUS_FOR_ORDERS_FOR_A_PRODUCT'/>
   <value enum='4' description='STATUS_FOR_ORDERS_FOR_ACFI_CODE'/>
   <value enum='5' description='STATUS_FOR_ORDERS_FOR_A_SECURITY_TYPE'/>
   <value enum='6' description='STATUS_FOR_ORDERS_FOR_A_TRADING_SESSION'/>
   <value enum='7' description='STATUS_FOR_ALL_ORDERS'/>
   <value enum='8' description='STATUS_FOR_ORDERS_FOR_A_PARTY_ID'/>
  </field>
  <field number='586' name='OrigOrdModTime' type='UTCTIMESTAMP'/>
  <field number='587' name='LegSettlType' type='CHAR'/>
  <field number='588' name='LegSettlDate' type='LOCALMKTDATE'/>
  <field number='589' name='DayBookingInst' type='CHAR'>
   <value enum='0' description='AUTO'/>
   <value enum='1' description='SPEAK_WITH_ORDER_INITIATOR_BEFORE_BOOKING'/>
   <value enum='2' description='ACCUMULATE'/>
  </field>
  <field number='590' name='BookingUnit' type='CHAR'>
   <value enum='0' description='EACH_PARTIAL_EXECUTION_IS_A_BOOKABLE_UNIT'/>
   <value enum='1' description='AGGREGATE_PARTIAL_EXECUTIONS_ON_THIS_ORDER'/>
   <value enum='2' description='AGGREGATE_EXECUTIONS_FOR_THIS_SYMBOL'/>
  </field>
  <field number='591' name='PreallocMethod' type='CHAR'>
   <value enum='0' description='PRO_RATA'/>
   <value enum='1' description='DO_NOT_PRO_RATA'/>
  </field>
  <field number='592' name='UnderlyingCountryOfIssue' type='COUNTRY'/>
  <field number='593' name='UnderlyingStateOrProvinceOfIssue' type='STRING'/>
  <field number='594' name='UnderlyingLocaleOfIssue' type='STRING'/>
  <field number='595' name='UnderlyingInstrRegistry' type='STRING'/>
  <field number='596' name='LegCountryOfIssue' type='COUNTRY'/>
  <field number='597' name='LegStateOrProvinceOfIssue' type='STRING'/>
  <field number='598' name='LegLocaleOfIssue' type='STRING'/>
  <field number='599' name='LegInstrRegistry' type='STRING'/>
  <field number='600' name='LegSymbol' type='STRING'/>
  <field number='601' name='LegSymbolSfx' type='STRING'/>
  <field number='602' name='LegSecurityID' type='STRING'/>
  <field number='603' name='LegSecurityIDSource' type='STRING'/>
  <field number='604' name='NoLegSecurityAltID' type='NUMINGROUP'/>
  <field number='605' name='LegSecurityAltID' type='STRING'/>
  <field number='606' name='LegSecurityAltIDSource' type='STRING'/>
  <field number='607' name='LegProduct' type='INT'/>
  <field number='608' name='LegCFICode' type='STRING'/>
  <field number='609' name='LegSecurityType' type='STRING'/>
  <field number='610' name='LegMaturityMonthYear' type='MONTHYEAR'/>
  <field number='611' name='LegMaturityDate' type='LOCALMKTDATE'/>
  <field number='612' name='LegStrikePrice' type='PRICE'/>
  <field number='613' name='LegOptAttribute' type='CHAR'/>
  <field number='614' name='LegContractMultiplier' type='FLOAT'/>
  <field number='615' name='LegCouponRate' type='PERCENTAGE'/>
  <field number='616' name='LegSecurityExchange' type='EXCHANGE'/>
  <field number='617' name='LegIssuer' type='STRING'/>
  <field number='618' name='EncodedLegIssuerLen' type='LENGTH'/>
  <field number='619' name='EncodedLegIssuer' type='DATA'/>
  <field number='620' name='LegSecurityDesc' type='STRING'/>
  <field number='621' name='EncodedLegSecurityDescLen' type='LENGTH'/>
  <field number='622' name='EncodedLegSecurityDesc' type='DATA'/>
  <field number='623' name='LegRatioQty' type='FLOAT'/>
  <field number='624' name='LegSide' type='CHAR'/>
  <field number='625' name='TradingSessionSubID' type='STRING'/>
  <field number='626' name='AllocType' type='INT'>
   <value enum='1' description='CALCULATED'/>
   <value enum='2' description='PRELIMINARY'/>
   <value enum='3' description='SELLSIDE_CALCULATED_USING_PRELIMINARY'/>
   <value enum='4' description='SELLSIDE_CALCULATED_WITHOUT_PRELIMINARY'/>
   <value enum='5' description='READY_TO_BOOK'/>
   <value enum='6' description='BUYSIDE_READY_TO_BOOK'/>
   <value enum='7' description='WAREHOUSE_INSTRUCTION'/>
   <value enum='8' description='REQUEST_TO_INTERMEDIARY'/>
   <value enum='9' description='ACCEPT'/>
   <value enum='10' description='REJECT'/>
   <value enum='11' description='ACCEPT_PENDING'/>
   <value enum='12' description='INCOMPLETE_GROUP'/>
   <value enum='13' description='COMPLETE_GROUP'/>
   <value enum='14' description='REVERSAL_PENDING'/>
  </field>
  <field number='627' name='NoHops' type='NUMINGROUP'/>
  <field number='628' name='HopCompID' type='STRING'/>
  <field number='629' name='HopSendingTime' type='UTCTIMESTAMP'/>
  <field number='630' name='HopRefID' type='SEQNUM'/>
  <field number='631' name='MidPx' type='PRICE'/>
  <field number='632' name='BidYield' type='PERCENTAGE'/>
  <field number='633' name='MidYield' type='PERCENTAGE'/>
  <field number='634' name='OfferYield' type='PERCENTAGE'/>
  <field number='635' name='ClearingFeeIndicator' type='STRING'>
   <value enum='1' description='FIRST_YEAR_DELEGATE'/>
   <value enum='2' description='SECOND_YEAR_DELEGATE'/>
   <value enum='3' description='THIRD_YEAR_DELEGATE'/>
   <value enum='4' description='FOURTH_YEAR_DELEGATE'/>
   <value enum='5' description='FIFTH_YEAR_DELEGATE'/>
   <value enum='9' description='SIXTH_YEAR_DELEGATE'/>
   <value enum='B' description='CBOE_MEMBER'/>
   <value enum='C' description='NON_MEMBER_AND_CUSTOMER'/>
   <value enum='E' description='EQUITY_MEMBER_AND_CLEARING_MEMBER'/>
   <value enum='F' description='FULL_AND_ASSOCIATE_MEMBER'/>
   <value enum='H' description='FIRMS106_H_AND106_J'/>
   <value enum='I' description='GIM'/>
   <value enum='L' description='LESSEE106_F_EMPLOYEES'/>
   <value enum='M' description='ALL_OTHER_OWNERSHIP_TYPES'/>
  </field>
  <field number='636' name='WorkingIndicator' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='637' name='LegLastPx' type='PRICE'/>
  <field number='638' name='PriorityIndicator' type='INT'>
   <value enum='0' description='PRIORITY_UNCHANGED'/>
   <value enum='1' description='LOST_PRIORITY_AS_RESULT_OF_ORDER_CHANGE'/>
  </field>
  <field number='639' name='PriceImprovement' type='PRICEOFFSET'/>
  <field number='640' name='Price2' type='PRICE'/>
  <field number='641' name='LastForwardPoints2' type='PRICEOFFSET'/>
  <field number='642' name='BidForwardPoints2' type='PRICEOFFSET'/>
  <field number='643' name='OfferForwardPoints2' type='PRICEOFFSET'/>
  <field number='644' name='RFQReqID' type='STRING'/>
  <field number='645' name='MktBidPx' type='PRICE'/>
  <field number='646' name='MktOfferPx' type='PRICE'/>
  <field number='647' name='MinBidSize' type='QTY'/>
  <field number='648' name='MinOfferSize' type='QTY'/>
  <field number='649' name='QuoteStatusReqID' type='STRING'/>
  <field number='650' name='LegalConfirm' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='651' name='UnderlyingLastPx' type='PRICE'/>
  <field number='652' name='UnderlyingLastQty' type='QTY'/>
  <field number='654' name='LegRefID' type='STRING'/>
  <field number='655' name='ContraLegRefID' type='STRING'/>
  <field number='656' name='SettlCurrBidFxRate' type='FLOAT'/>
  <field number='657' name='SettlCurrOfferFxRate' type='FLOAT'/>
  <field number='658' name='QuoteRequestRejectReason' type='INT'>
   <value enum='1' description='UNKNOWN_SYMBOL'/>
   <value enum='2' description='EXCHANGE'/>
   <value enum='3' description='QUOTE_REQUEST_EXCEEDS_LIMIT'/>
   <value enum='4' description='TOO_LATE_TO_ENTER'/>
   <value enum='5' description='INVALID_PRICE'/>
   <value enum='6' description='NOT_AUTHORIZED_TO_REQUEST_QUOTE'/>
   <value enum='7' description='NO_MATCH_FOR_INQUIRY'/>
   <value enum='8' description='NO_MARKET_FOR_INSTRUMENT'/>
   <value enum='9' description='NO_INVENTORY'/>
   <value enum='10' description='PASS'/>
   <value enum='11' description='INSUFFICIENT_CREDIT'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='659' name='SideComplianceID' type='STRING'/>
  <field number='660' name='AcctIDSource' type='INT'>
   <value enum='1' description='BIC'/>
   <value enum='2' description='SID_CODE'/>
   <value enum='3' description='TFM'/>
   <value enum='4' description='OMGEO'/>
   <value enum='5' description='DTCC_CODE'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='661' name='AllocAcctIDSource' type='INT'/>
  <field number='662' name='BenchmarkPrice' type='PRICE'/>
  <field number='663' name='BenchmarkPriceType' type='INT'/>
  <field number='664' name='ConfirmID' type='STRING'/>
  <field number='665' name='ConfirmStatus' type='INT'>
   <value enum='1' description='RECEIVED'/>
   <value enum='2' description='MISMATCHED_ACCOUNT'/>
   <value enum='3' description='MISSING_SETTLEMENT_INSTRUCTIONS'/>
   <value enum='4' description='CONFIRMED'/>
   <value enum='5' description='REQUEST_REJECTED'/>
  </field>
  <field number='666' name='ConfirmTransType' type='INT'>
   <value enum='0' description='NEW'/>
   <value enum='1' description='REPLACE'/>
   <value enum='2' description='CANCEL'/>
  </field>
  <field number='667' name='ContractSettlMonth' type='MONTHYEAR'/>
  <field number='668' name='DeliveryForm' type='INT'>
   <value enum='1' description='BOOK_ENTRY'/>
   <value enum='2' description='BEARER'/>
  </field>
  <field number='669' name='LastParPx' type='PRICE'/>
  <field number='670' name='NoLegAllocs' type='NUMINGROUP'/>
  <field number='671' name='LegAllocAccount' type='STRING'/>
  <field number='672' name='LegIndividualAllocID' type='STRING'/>
  <field number='673' name='LegAllocQty' type='QTY'/>
  <field number='674' name='LegAllocAcctIDSource' type='STRING'/>
  <field number='675' name='LegSettlCurrency' type='CURRENCY'/>
  <field number='676' name='LegBenchmarkCurveCurrency' type='CURRENCY'/>
  <field number='677' name='LegBenchmarkCurveName' type='STRING'/>
  <field number='678' name='LegBenchmarkCurvePoint' type='STRING'/>
  <field number='679' name='LegBenchmarkPrice' type='PRICE'/>
  <field number='680' name='LegBenchmarkPriceType' type='INT'/>
  <field number='681' name='LegBidPx' type='PRICE'/>
  <field number='682' name='LegIOIQty' type='STRING'/>
  <field number='683' name='NoLegStipulations' type='NUMINGROUP'/>
  <field number='684' name='LegOfferPx' type='PRICE'/>
  <field number='685' name='LegOrderQty' type='QTY'/>
  <field number='686' name='LegPriceType' type='INT'/>
  <field number='687' name='LegQty' type='QTY'/>
  <field number='688' name='LegStipulationType' type='STRING'/>
  <field number='689' name='LegStipulationValue' type='STRING'/>
  <field number='690' name='LegSwapType' type='INT'>
   <value enum='1' description='PAR_FOR_PAR'/>
   <value enum='2' description='MODIFIED_DURATION'/>
   <value enum='4' description='RISK'/>
   <value enum='5' description='PROCEEDS'/>
  </field>
  <field number='691' name='Pool' type='STRING'/>
  <field number='692' name='QuotePriceType' type='INT'>
   <value enum='1' description='PERCENT'/>
   <value enum='2' description='PER_SHARE'/>
   <value enum='3' description='FIXED_AMOUNT'/>
   <value enum='4' description='DISCOUNT'/>
   <value enum='5' description='PREMIUM'/>
   <value enum='6' description='SPREAD'/>
   <value enum='7' description='TED_PRICE'/>
   <value enum='8' description='TED_YIELD'/>
   <value enum='9' description='YIELD_SPREAD'/>
   <value enum='10' description='YIELD'/>
  </field>
  <field number='693' name='QuoteRespID' type='STRING'/>
  <field number='694' name='QuoteRespType' type='INT'>
   <value enum='1' description='HIT'/>
   <value enum='2' description='COUNTER'/>
   <value enum='3' description='EXPIRED'/>
   <value enum='4' description='COVER'/>
   <value enum='5' description='DONE_AWAY'/>
   <value enum='6' description='PASS'/>
  </field>
  <field number='695' name='QuoteQualifier' type='CHAR'/>
  <field number='696' name='YieldRedemptionDate' type='LOCALMKTDATE'/>
  <field number='697' name='YieldRedemptionPrice' type='PRICE'/>
  <field number='698' name='YieldRedemptionPriceType' type='INT'/>
  <field number='699' name='BenchmarkSecurityID' type='STRING'/>
  <field number='700' name='ReversalIndicator' type='BOOLEAN'/>
  <field number='701' name='YieldCalcDate' type='LOCALMKTDATE'/>
  <field number='702' name='NoPositions' type='NUMINGROUP'/>
  <field number='703' name='PosType' type='STRING'>
   <value enum='AS' description='OPTION_ASSIGNMENT'/>
   <value enum='DN' description='DELIVERY_NOTICE_QTY'/>
   <value enum='EP' description='EXCHANGE_FOR_PHYSICAL_QTY'/>
   <value enum='EX' description='OPTION_EXERCISE_QTY'/>
   <value enum='PA' description='ADJUSTMENT_QTY'/>
   <value enum='TA' description='TRANSACTION_FROM_ASSIGNMENT'/>
   <value enum='TQ' description='TRANSACTION_QUANTITY'/>
   <value enum='TX' description='TRANSACTION_FROM_EXERCISE'/>
   <value enum='XM' description='CROSS_MARGIN_QTY'/>
   <value enum='ALC' description='ALLOCATION_TRADE_QTY'/>
   <value enum='ASF' description='AS_OF_TRADE_QTY'/>
   <value enum='CAA' description='CORPORATE_ACTION_ADJUSTMENT'/>
   <value enum='DLV' description='DELIVERY_QTY'/>
   <value enum='ETR' description='ELECTRONIC_TRADE_QTY'/>
   <value enum='FIN' description='END_OF_DAY_QTY'/>
   <value enum='IAS' description='INTRA_SPREAD_QTY'/>
   <value enum='IES' description='INTER_SPREAD_QTY'/>
   <value enum='PIT' description='PIT_TRADE_QTY'/>
   <value enum='RCV' description='RECEIVE_QUANTITY'/>
   <value enum='SOD' description='START_OF_DAY_QTY'/>
   <value enum='SPL' description='INTEGRAL_SPLIT'/>
   <value enum='TOT' description='TOTAL_TRANSACTION_QTY'/>
   <value enum='TRF' description='TRANSFER_TRADE_QTY'/>
  </field>
  <field number='704' name='LongQty' type='QTY'/>
  <field number='705' name='ShortQty' type='QTY'/>
  <field number='706' name='PosQtyStatus' type='INT'>
   <value enum='0' description='SUBMITTED'/>
   <value enum='1' description='ACCEPTED'/>
   <value enum='2' description='REJECTED'/>
  </field>
  <field number='707' name='PosAmtType' type='STRING'>
   <value enum='CASH' description='CASH_AMOUNT'/>
   <value enum='CRES' description='CASH_RESIDUAL_AMOUNT'/>
   <value enum='FMTM' description='FINAL_MARK_TO_MARKET_AMOUNT'/>
   <value enum='IMTM' description='INCREMENTAL_MARK_TO_MARKET_AMOUNT'/>
   <value enum='PREM' description='PREMIUM_AMOUNT'/>
   <value enum='SETL' description='SETTLEMENT_VALUE'/>
   <value enum='SMTM' description='START_OF_DAY_MARK_TO_MARKET_AMOUNT'/>
   <value enum='TVAR' description='TRADE_VARIATION_AMOUNT'/>
   <value enum='VADJ' description='VALUE_ADJUSTED_AMOUNT'/>
  </field>
  <field number='708' name='PosAmt' type='AMT'/>
  <field number='709' name='PosTransType' type='INT'>
   <value enum='1' description='EXERCISE'/>
   <value enum='2' description='DO_NOT_EXERCISE'/>
   <value enum='3' description='POSITION_ADJUSTMENT'/>
   <value enum='4' description='POSITION_CHANGE_SUBMISSION'/>
   <value enum='5' description='PLEDGE'/>
   <value enum='6' description='LARGE_TRADER_SUBMISSION'/>
  </field>
  <field number='710' name='PosReqID' type='STRING'/>
  <field number='711' name='NoUnderlyings' type='NUMINGROUP'/>
  <field number='712' name='PosMaintAction' type='INT'>
   <value enum='1' description='NEW'/>
   <value enum='2' description='REPLACE'/>
   <value enum='3' description='CANCEL'/>
   <value enum='4' description='REVERSE'/>
  </field>
  <field number='713' name='OrigPosReqRefID' type='STRING'/>
  <field number='714' name='PosMaintRptRefID' type='STRING'/>
  <field number='715' name='ClearingBusinessDate' type='LOCALMKTDATE'/>
  <field number='716' name='SettlSessID' type='STRING'>
   <value enum='EOD' description='END_OF_DAY'/>
   <value enum='ETH' description='ELECTRONIC_TRADING_HOURS'/>
   <value enum='ITD' description='INTRADAY'/>
   <value enum='RTH' description='REGULAR_TRADING_HOURS'/>
  </field>
  <field number='717' name='SettlSessSubID' type='STRING'/>
  <field number='718' name='AdjustmentType' type='INT'>
   <value enum='0' description='PROCESS_REQUEST_AS_MARGIN_DISPOSITION'/>
   <value enum='1' description='DELTA_PLUS'/>
   <value enum='2' description='DELTA_MINUS'/>
   <value enum='3' description='FINAL'/>
  </field>
  <field number='719' name='ContraryInstructionIndicator' type='BOOLEAN'/>
  <field number='720' name='PriorSpreadIndicator' type='BOOLEAN'/>
  <field number='721' name='PosMaintRptID' type='STRING'/>
  <field number='722' name='PosMaintStatus' type='INT'>
   <value enum='0' description='ACCEPTED'/>
   <value enum='1' description='ACCEPTED_WITH_WARNINGS'/>
   <value enum='2' description='REJECTED'/>
   <value enum='3' description='COMPLETED'/>
   <value enum='4' description='COMPLETED_WITH_WARNINGS'/>
  </field>
  <field number='723' name='PosMaintResult' type='INT'>
   <value enum='0' description='SUCCESSFUL_COMPLETION'/>
   <value enum='1' description='REJECTED'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='724' name='PosReqType' type='INT'>
   <value enum='0' description='POSITIONS'/>
   <value enum='1' description='TRADES'/>
   <value enum='2' description='EXERCISES'/>
   <value enum='3' description='ASSIGNMENTS'/>
   <value enum='4' description='SETTLEMENT_ACTIVITY'/>
   <value enum='5' description='BACKOUT_MESSAGE'/>
  </field>
  <field number='725' name='ResponseTransportType' type='INT'>
   <value enum='0' description='INBAND'/>
   <value enum='1' description='OUT_OF_BAND'/>
  </field>
  <field number='726' name='ResponseDestination' type='STRING'/>
  <field number='727' name='TotalNumPosReports' type='INT'/>
  <field number='728' name='PosReqResult' type='INT'>
   <value enum='0' description='VALID_REQUEST'/>
   <value enum='1' description='INVALID_OR_UNSUPPORTED_REQUEST'/>
   <value enum='2' description='NO_POSITIONS_FOUND_THAT_MATCH_CRITERIA'/>
   <value enum='3' description='NOT_AUTHORIZED_TO_REQUEST_POSITIONS'/>
   <value enum='4' description='REQUEST_FOR_POSITION_NOT_SUPPORTED'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='729' name='PosReqStatus' type='INT'>
   <value enum='0' description='COMPLETED'/>
   <value enum='1' description='COMPLETED_WITH_WARNINGS'/>
   <value enum='2' description='REJECTED'/>
  </field>
  <field number='730' name='SettlPrice' type='PRICE'/>
  <field number='731' name='SettlPriceType' type='INT'>
   <value enum='1' description='FINAL'/>
   <value enum='2' description='THEORETICAL'/>
  </field>
  <field number='732' name='UnderlyingSettlPrice' type='PRICE'/>
  <field number='733' name='UnderlyingSettlPriceType' type='INT'/>
  <field number='734' name='PriorSettlPrice' type='PRICE'/>
  <field number='735' name='NoQuoteQualifiers' type='NUMINGROUP'/>
  <field number='736' name='AllocSettlCurrency' type='CURRENCY'/>
  <field number='737' name='AllocSettlCurrAmt' type='AMT'/>
  <field number='738' name='InterestAtMaturity' type='AMT'/>
  <field number='739' name='LegDatedDate' type='LOCALMKTDATE'/>
  <field number='740' name='LegPool' type='STRING'/>
  <field number='741' name='AllocInterestAtMaturity' type='AMT'/>
  <field number='742' name='AllocAccruedInterestAmt' type='AMT'/>
  <field number='743' name='DeliveryDate' type='LOCALMKTDATE'/>
  <field number='744' name='AssignmentMethod' type='CHAR'>
   <value enum='P' description='PRO_RATA'/>
   <value enum='R' description='RANDOM'/>
  </field>
  <field number='745' name='AssignmentUnit' type='QTY'/>
  <field number='746' name='OpenInterest' type='AMT'/>
  <field number='747' name='ExerciseMethod' type='CHAR'>
   <value enum='A' description='AUTOMATIC'/>
   <value enum='M' description='MANUAL'/>
  </field>
  <field number='748' name='TotNumTradeReports' type='INT'/>
  <field number='749' name='TradeRequestResult' type='INT'>
   <value enum='0' description='SUCCESSFUL'/>
   <value enum='1' description='INVALID_OR_UNKNOWN_INSTRUMENT'/>
   <value enum='2' description='INVALID_TYPE_OF_TRADE_REQUESTED'/>
   <value enum='3' description='INVALID_PARTIES'/>
   <value enum='4' description='INVALID_TRANSPORT_TYPE_REQUESTED'/>
   <value enum='5' description='INVALID_DESTINATION_REQUESTED'/>
   <value enum='8' description='TRADE_REQUEST_TYPE_NOT_SUPPORTED'/>
   <value enum='9' description='NOT_AUTHORIZED'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='750' name='TradeRequestStatus' type='INT'>
   <value enum='0' description='ACCEPTED'/>
   <value enum='1' description='COMPLETED'/>
   <value enum='2' description='REJECTED'/>
  </field>
  <field number='751' name='TradeReportRejectReason' type='INT'>
   <value enum='0' description='SUCCESSFUL'/>
   <value enum='1' description='INVALID_PARTY_ONFORMATION'/>
   <value enum='2' description='UNKNOWN_INSTRUMENT'/>
   <value enum='3' description='UNAUTHORIZED_TO_REPORT_TRADES'/>
   <value enum='4' description='INVALID_TRADE_TYPE'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='752' name='SideMultiLegReportingType' type='INT'>
   <value enum='1' description='SINGLE_SECURITY'/>
   <value enum='2' description='INDIVIDUAL_LEG_OF_A_MULTILEG_SECURITY'/>
   <value enum='3' description='MULTILEG_SECURITY'/>
  </field>
  <field number='753' name='NoPosAmt' type='NUMINGROUP'/>
  <field number='754' name='AutoAcceptIndicator' type='BOOLEAN'/>
  <field number='755' name='AllocReportID' type='STRING'/>
  <field number='756' name='NoNested2PartyIDs' type='NUMINGROUP'/>
  <field number='757' name='Nested2PartyID' type='STRING'/>
  <field number='758' name='Nested2PartyIDSource' type='CHAR'/>
  <field number='759' name='Nested2PartyRole' type='INT'/>
  <field number='760' name='Nested2PartySubID' type='STRING'/>
  <field number='761' name='BenchmarkSecurityIDSource' type='STRING'/>
  <field number='762' name='SecuritySubType' type='STRING'/>
  <field number='763' name='UnderlyingSecuritySubType' type='STRING'/>
  <field number='764' name='LegSecuritySubType' type='STRING'/>
  <field number='765' name='AllowableOneSidednessPct' type='PERCENTAGE'/>
  <field number='766' name='AllowableOneSidednessValue' type='AMT'/>
  <field number='767' name='AllowableOneSidednessCurr' type='CURRENCY'/>
  <field number='768' name='NoTrdRegTimestamps' type='NUMINGROUP'/>
  <field number='769' name='TrdRegTimestamp' type='UTCTIMESTAMP'/>
  <field number='770' name='TrdRegTimestampType' type='INT'>
   <value enum='1' description='EXECUTION_TIME'/>
   <value enum='2' description='TIME_IN'/>
   <value enum='3' description='TIME_OUT'/>
   <value enum='4' description='BROKER_RECEIPT'/>
   <value enum='5' description='BROKER_EXECUTION'/>
   <value enum='6' description='DESK_RECEIPT'/>
  </field>
  <field number='771' name='TrdRegTimestampOrigin' type='STRING'/>
  <field number='772' name='ConfirmRefID' type='STRING'/>
  <field number='773' name='ConfirmType' type='INT'>
   <value enum='1' description='STATUS'/>
   <value enum='2' description='CONFIRMATION'/>
   <value enum='3' description='CONFIRMATION_REQUEST_REJECTED'/>
  </field>
  <field number='774' name='ConfirmRejReason' type='INT'>
   <value enum='1' description='MISMATCHED_ACCOUNT'/>
   <value enum='2' description='MISSING_SETTLEMENT_INSTRUCTIONS'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='775' name='BookingType' type='INT'>
   <value enum='0' description='REGULAR_BOOKING'/>
   <value enum='1' description='CFD'/>
   <value enum='2' description='TOTAL_RETURN_SWAP'/>
  </field>
  <field number='776' name='IndividualAllocRejCode' type='INT'/>
  <field number='777' name='SettlInstMsgID' type='STRING'/>
  <field number='778' name='NoSettlInst' type='NUMINGROUP'/>
  <field number='779' name='LastUpdateTime' type='UTCTIMESTAMP'/>
  <field number='780' name='AllocSettlInstType' type='INT'>
   <value enum='0' description='USE_DEFAULT_INSTRUCTIONS'/>
   <value enum='1' description='DERIVE_FROM_PARAMETERS_PROVIDED'/>
   <value enum='2' description='FULL_DETAILS_PROVIDED'/>
   <value enum='3' description='SSIDBI_DS_PROVIDED'/>
   <value enum='4' description='PHONE_FOR_INSTRUCTIONS'/>
  </field>
  <field number='781' name='NoSettlPartyIDs' type='NUMINGROUP'/>
  <field number='782' name='SettlPartyID' type='STRING'/>
  <field number='783' name='SettlPartyIDSource' type='CHAR'/>
  <field number='784' name='SettlPartyRole' type='INT'/>
  <field number='785' name='SettlPartySubID' type='STRING'/>
  <field number='786' name='SettlPartySubIDType' type='INT'/>
  <field number='787' name='DlvyInstType' type='CHAR'>
   <value enum='C' description='CASH'/>
   <value enum='S' description='SECURITIES'/>
  </field>
  <field number='788' name='TerminationType' type='INT'>
   <value enum='1' description='OVERNIGHT'/>
   <value enum='2' description='TERM'/>
   <value enum='3' description='FLEXIBLE'/>
   <value enum='4' description='OPEN'/>
  </field>
  <field number='789' name='NextExpectedMsgSeqNum' type='SEQNUM'/>
  <field number='790' name='OrdStatusReqID' type='STRING'/>
  <field number='791' name='SettlInstReqID' type='STRING'/>
  <field number='792' name='SettlInstReqRejCode' type='INT'>
   <value enum='0' description='UNABLE_TO_PROCESS_REQUEST'/>
   <value enum='1' description='UNKNOWN_ACCOUNT'/>
   <value enum='2' description='NO_MATCHING_SETTLEMENT_INSTRUCTIONS_FOUND'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='793' name='SecondaryAllocID' type='STRING'/>
  <field number='794' name='AllocReportType' type='INT'>
   <value enum='2' description='PRELIMINARY_REQUEST_TO_INTERMEDIARY'/>
   <value enum='3' description='SELLSIDE_CALCULATED_USING_PRELIMINARY'/>
   <value enum='4' description='SELLSIDE_CALCULATED_WITHOUT_PRELIMINARY'/>
   <value enum='5' description='WAREHOUSE_RECAP'/>
   <value enum='8' description='REQUEST_TO_INTERMEDIARY'/>
   <value enum='9' description='ACCEPT'/>
   <value enum='10' description='REJECT'/>
   <value enum='11' description='ACCEPT_PENDING'/>
   <value enum='12' description='COMPLETE'/>
   <value enum='14' description='REVERSE_PENDING'/>
  </field>
  <field number='795' name='AllocReportRefID' type='STRING'/>
  <field number='796' name='AllocCancReplaceReason' type='INT'>
   <value enum='1' description='ORIGINAL_DETAILS_INCOMPLETE'/>
   <value enum='2' description='CHANGE_IN_UNDERLYING_ORDER_DETAILS'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='797' name='CopyMsgIndicator' type='BOOLEAN'/>
  <field number='798' name='AllocAccountType' type='INT'>
   <value enum='1' description='CARRIED_CUSTOMER_SIDE'/>
   <value enum='2' description='CARRIED_NON_CUSTOMER_SIDE'/>
   <value enum='3' description='HOUSE_TRADER'/>
   <value enum='4' description='FLOOR_TRADER'/>
   <value enum='6' description='CARRIED_NON_CUSTOMER_SIDE_CROSS_MARGINED'/>
   <value enum='7' description='HOUSE_TRADER_CROSS_MARGINED'/>
   <value enum='8' description='JOINT_BACK_OFFICE_ACCOUNT'/>
  </field>
  <field number='799' name='OrderAvgPx' type='PRICE'/>
  <field number='800' name='OrderBookingQty' type='QTY'/>
  <field number='801' name='NoSettlPartySubIDs' type='NUMINGROUP'/>
  <field number='802' name='NoPartySubIDs' type='NUMINGROUP'/>
  <field number='803' name='PartySubIDType' type='INT'>
   <value enum='1' description='FIRM'/>
   <value enum='2' description='PERSON'/>
   <value enum='3' description='SYSTEM'/>
   <value enum='4' description='APPLICATION'/>
   <value enum='5' description='FULL_LEGAL_NAME_OF_FIRM'/>
   <value enum='6' description='POSTAL_ADDRESS'/>
   <value enum='7' description='PHONE_NUMBER'/>
   <value enum='8' description='EMAIL_ADDRESS'/>
   <value enum='9' description='CONTACT_NAME'/>
   <value enum='10' description='SECURITIES_ACCOUNT_NUMBER'/>
   <value enum='11' description='REGISTRATION_NUMBER'/>
   <value enum='12' description='REGISTERED_ADDRESS_FOR_CONFIRMATION'/>
   <value enum='13' description='REGULATORY_STATUS'/>
   <value enum='14' description='REGISTRATION_NAME'/>
   <value enum='15' description='CASH_ACCOUNT_NUMBER'/>
   <value enum='16' description='BIC'/>
   <value enum='17' description='CSD_PARTICIPANT_MEMBER_CODE'/>
   <value enum='18' description='REGISTERED_ADDRESS'/>
   <value enum='19' description='FUND_ACCOUNT_NAME'/>
   <value enum='20' description='TELEX_NUMBER'/>
   <value enum='21' description='FAX_NUMBER'/>
   <value enum='22' description='SECURITIES_ACCOUNT_NAME'/>
   <value enum='23' description='CASH_ACCOUNT_NAME'/>
   <value enum='24' description='DEPARTMENT'/>
   <value enum='25' description='LOCATION_DESK'/>
   <value enum='26' description='POSITION_ACCOUNT_TYPE'/>
   <value enum='27' description='SECURITY_LOCATE_ID'/>
   <value enum='28' description='MARKET_MAKER'/>
   <value enum='29' description='ELIGIBLE_COUNTERPARTY'/>
   <value enum='30' description='PROFESSIONAL_CLIENT'/>
   <value enum='31' description='LOCATION'/>
   <value enum='32' description='EXECUTION_VENUE'/>
  </field>
  <field number='804' name='NoNestedPartySubIDs' type='NUMINGROUP'/>
  <field number='805' name='NestedPartySubIDType' type='INT'/>
  <field number='806' name='NoNested2PartySubIDs' type='NUMINGROUP'/>
  <field number='807' name='Nested2PartySubIDType' type='INT'/>
  <field number='808' name='AllocIntermedReqType' type='INT'>
   <value enum='1' description='PENDING_ACCEPT'/>
   <value enum='2' description='PENDING_RELEASE'/>
   <value enum='3' description='PENDING_REVERSAL'/>
   <value enum='4' description='ACCEPT'/>
   <value enum='5' description='BLOCK_LEVEL_REJECT'/>
   <value enum='6' description='ACCOUNT_LEVEL_REJECT'/>
  </field>
  <field number='810' name='UnderlyingPx' type='PRICE'/>
  <field number='811' name='PriceDelta' type='FLOAT'/>
  <field number='812' name='ApplQueueMax' type='INT'/>
  <field number='813' name='ApplQueueDepth' type='INT'/>
  <field number='814' name='ApplQueueResolution' type='INT'>
   <value enum='0' description='NO_ACTION_TAKEN'/>
   <value enum='1' description='QUEUE_FLUSHED'/>
   <value enum='2' description='OVERLAY_LAST'/>
   <value enum='3' description='END_SESSION'/>
  </field>
  <field number='815' name='ApplQueueAction' type='INT'>
   <value enum='0' description='NO_ACTION_TAKEN'/>
   <value enum='1' description='QUEUE_FLUSHED'/>
   <value enum='2' description='OVERLAY_LAST'/>
   <value enum='3' description='END_SESSION'/>
  </field>
  <field number='816' name='NoAltMDSource' type='NUMINGROUP'/>
  <field number='817' name='AltMDSourceID' type='STRING'/>
  <field number='818' name='SecondaryTradeReportID' type='STRING'/>
  <field number='819' name='AvgPxIndicator' type='INT'>
   <value enum='0' description='NO_AVERAGE_PRICING'/>
   <value enum='1' description='TRADE'/>
   <value enum='2' description='LAST_TRADE'/>
  </field>
  <field number='820' name='TradeLinkID' type='STRING'/>
  <field number='821' name='OrderInputDevice' type='STRING'/>
  <field number='822' name='UnderlyingTradingSessionID' type='STRING'/>
  <field number='823' name='UnderlyingTradingSessionSubID' type='STRING'/>
  <field number='824' name='TradeLegRefID' type='STRING'/>
  <field number='825' name='ExchangeRule' type='STRING'/>
  <field number='826' name='TradeAllocIndicator' type='INT'>
   <value enum='0' description='ALLOCATION_NOT_REQUIRED'/>
   <value enum='1' description='ALLOCATION_REQUIRED'/>
   <value enum='2' description='USE_ALLOCATION_PROVIDED_WITH_THE_TRADE'/>
   <value enum='3' description='ALLOCATION_GIVE_UP_EXECUTOR'/>
   <value enum='4' description='ALLOCATION_FROM_EXECUTOR'/>
   <value enum='5' description='ALLOCATION_TO_CLAIM_ACCOUNT'/>
  </field>
  <field number='827' name='ExpirationCycle' type='INT'>
   <value enum='0' description='EXPIRE_ON_TRADING_SESSION_CLOSE'/>
   <value enum='1' description='EXPIRE_ON_TRADING_SESSION_OPEN'/>
  </field>
  <field number='828' name='TrdType' type='INT'>
   <value enum='0' description='REGULAR_TRADE'/>
   <value enum='1' description='BLOCK_TRADE'/>
   <value enum='2' description='EFP'/>
   <value enum='3' description='TRANSFER'/>
   <value enum='4' description='LATE_TRADE'/>
   <value enum='5' description='T_TRADE'/>
   <value enum='6' description='WEIGHTED_AVERAGE_PRICE_TRADE'/>
   <value enum='7' description='BUNCHED_TRADE'/>
   <value enum='8' description='LATE_BUNCHED_TRADE'/>
   <value enum='9' description='PRIOR_REFERENCE_PRICE_TRADE'/>
   <value enum='10' description='AFTER_HOURS_TRADE'/>
   <value enum='11' description='EXCHANGE_FOR_RISK'/>
   <value enum='12' description='EXCHANGE_FOR_SWAP'/>
   <value enum='13' description='EXCHANGE_OF_FUTURES_FOR'/>
   <value enum='14' description='EXCHANGE_OF_OPTIONS_FOR_OPTIONS'/>
   <value enum='15' description='TRADING_AT_SETTLEMENT'/>
   <value enum='16' description='ALL_OR_NONE'/>
   <value enum='17' description='FUTURES_LARGE_ORDER_EXECUTION'/>
   <value enum='18' description='EXCHANGE_OF_FUTURES_FOR_FUTURES'/>
   <value enum='19' description='OPTION_INTERIM_TRADE'/>
   <value enum='20' description='OPTION_CABINET_TRADE'/>
   <value enum='22' description='PRIVATELY_NEGOTIATED_TRADES'/>
   <value enum='23' description='SUBSTITUTION_OF_FUTURES_FOR_FORWARDS'/>
   <value enum='24' description='ERROR_TRADE'/>
   <value enum='25' description='SPECIAL_CUM_DIVIDEND'/>
   <value enum='26' description='SPECIAL_EX_DIVIDEND'/>
   <value enum='27' description='SPECIAL_CUM_COUPON'/>
   <value enum='28' description='SPECIAL_EX_COUPON'/>
   <value enum='29' description='CASH_SETTLEMENT'/>
   <value enum='30' description='SPECIAL_PRICE'/>
   <value enum='31' description='GUARANTEED_DELIVERY'/>
   <value enum='32' description='SPECIAL_CUM_RIGHTS'/>
   <value enum='33' description='SPECIAL_EX_RIGHTS'/>
   <value enum='34' description='SPECIAL_CUM_CAPITAL_REPAYMENTS'/>
   <value enum='35' description='SPECIAL_EX_CAPITAL_REPAYMENTS'/>
   <value enum='36' description='SPECIAL_CUM_BONUS'/>
   <value enum='37' description='SPECIAL_EX_BONUS'/>
   <value enum='38' description='LARGE_TRADE'/>
   <value enum='39' description='WORKED_PRINCIPAL_TRADE'/>
   <value enum='40' description='BLOCK_TRADES'/>
   <value enum='41' description='NAME_CHANGE'/>
   <value enum='42' description='PORTFOLIO_TRANSFER'/>
   <value enum='43' description='PROROGATION_BUY'/>
   <value enum='44' description='PROROGATION_SELL'/>
   <value enum='45' description='OPTION_EXERCISE'/>
   <value enum='46' description='DELTA_NEUTRAL_TRANSACTION'/>
   <value enum='47' description='FINANCING_TRANSACTION'/>
  </field>
  <field number='829' name='TrdSubType' type='INT'>
   <value enum='0' description='CMTA'/>
   <value enum='1' description='INTERNAL_TRANSFER_OR_ADJUSTMENT'/>
   <value enum='2' description='EXTERNAL_TRANSFER_OR_TRANSFER_OF_ACCOUNT'/>
   <value enum='3' description='REJECT_FOR_SUBMITTING_SIDE'/>
   <value enum='4' description='ADVISORY_FOR_CONTRA_SIDE'/>
   <value enum='5' description='OFFSET_DUE_TO_AN_ALLOCATION'/>
   <value enum='6' description='ONSET_DUE_TO_AN_ALLOCATION'/>
   <value enum='7' description='DIFFERENTIAL_SPREAD'/>
   <value enum='8' description='IMPLIED_SPREAD_LEG_EXECUTED_AGAINST_AN_OUTRIGHT'/>
   <value enum='9' description='TRANSACTION_FROM_EXERCISE'/>
   <value enum='10' description='TRANSACTION_FROM_ASSIGNMENT'/>
   <value enum='11' description='ACATS'/>
   <value enum='14' description='AI'/>
   <value enum='15' description='B'/>
   <value enum='16' description='K'/>
   <value enum='17' description='LC'/>
   <value enum='18' description='M'/>
   <value enum='19' description='N'/>
   <value enum='20' description='NM'/>
   <value enum='21' description='NR'/>
   <value enum='22' description='P'/>
   <value enum='23' description='PA'/>
   <value enum='24' description='PC'/>
   <value enum='25' description='PN'/>
   <value enum='26' description='R'/>
   <value enum='27' description='RO'/>
   <value enum='28' description='RT'/>
   <value enum='29' description='SW'/>
   <value enum='30' description='T'/>
   <value enum='31' description='WN'/>
   <value enum='32' description='WT'/>
  </field>
  <field number='830' name='TransferReason' type='STRING'/>
  <field number='832' name='TotNumAssignmentReports' type='INT'/>
  <field number='833' name='AsgnRptID' type='STRING'/>
  <field number='834' name='ThresholdAmount' type='PRICEOFFSET'/>
  <field number='835' name='PegMoveType' type='INT'>
   <value enum='0' description='FLOATING'/>
   <value enum='1' description='FIXED'/>
  </field>
  <field number='836' name='PegOffsetType' type='INT'>
   <value enum='0' description='PRICE'/>
   <value enum='1' description='BASIS_POINTS'/>
   <value enum='2' description='TICKS'/>
   <value enum='3' description='PRICE_TIER'/>
  </field>
  <field number='837' name='PegLimitType' type='INT'>
   <value enum='0' description='OR_BETTER'/>
   <value enum='1' description='STRICT'/>
   <value enum='2' description='OR_WORSE'/>
  </field>
  <field number='838' name='PegRoundDirection' type='INT'>
   <value enum='1' description='MORE_AGGRESSIVE'/>
   <value enum='2' description='MORE_PASSIVE'/>
  </field>
  <field number='839' name='PeggedPrice' type='PRICE'/>
  <field number='840' name='PegScope' type='INT'>
   <value enum='1' description='LOCAL'/>
   <value enum='2' description='NATIONAL'/>
   <value enum='3' description='GLOBAL'/>
   <value enum='4' description='NATIONAL_EXCLUDING_LOCAL'/>
  </field>
  <field number='841' name='DiscretionMoveType' type='INT'>
   <value enum='0' description='FLOATING'/>
   <value enum='1' description='FIXED'/>
  </field>
  <field number='842' name='DiscretionOffsetType' type='INT'>
   <value enum='0' description='PRICE'/>
   <value enum='1' description='BASIS_POINTS'/>
   <value enum='2' description='TICKS'/>
   <value enum='3' description='PRICE_TIER'/>
  </field>
  <field number='843' name='DiscretionLimitType' type='INT'>
   <value enum='0' description='OR_BETTER'/>
   <value enum='1' description='STRICT'/>
   <value enum='2' description='OR_WORSE'/>
  </field>
  <field number='844' name='DiscretionRoundDirection' type='INT'>
   <value enum='1' description='MORE_AGGRESSIVE'/>
   <value enum='2' description='MORE_PASSIVE'/>
  </field>
  <field number='845' name='DiscretionPrice' type='PRICE'/>
  <field number='846' name='DiscretionScope' type='INT'>
   <value enum='1' description='LOCAL'/>
   <value enum='2' description='NATIONAL'/>
   <value enum='3' description='GLOBAL'/>
   <value enum='4' description='NATIONAL_EXCLUDING_LOCAL'/>
  </field>
  <field number='847' name='TargetStrategy' type='INT'>
   <value enum='1' description='VWAP'/>
   <value enum='2' description='PARTICIPATE'/>
   <value enum='3' description='MININIZE_MARKET_IMPACT'/>
  </field>
  <field number='848' name='TargetStrategyParameters' type='STRING'/>
  <field number='849' name='ParticipationRate' type='PERCENTAGE'/>
  <field number='850' name='TargetStrategyPerformance' type='FLOAT'/>
  <field number='851' name='LastLiquidityInd' type='INT'>
   <value enum='1' description='ADDED_LIQUIDITY'/>
   <value enum='2' description='REMOVED_LIQUIDITY'/>
   <value enum='3' description='LIQUIDITY_ROUTED_OUT'/>
  </field>
  <field number='852' name='PublishTrdIndicator' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='853' name='ShortSaleReason' type='INT'>
   <value enum='0' description='DEALER_SOLD_SHORT'/>
   <value enum='1' description='DEALER_SOLD_SHORT_EXEMPT'/>
   <value enum='2' description='SELLING_CUSTOMER_SOLD_SHORT'/>
   <value enum='3' description='SELLING_CUSTOMER_SOLD_SHORT_EXEMPT'/>
   <value enum='4' description='QUALIFIED_SERVICE_REPRESENTATIVE'/>
   <value enum='5' description='QSR_OR_AGU_CONTRA_SIDE_SOLD_SHORT_EXEMPT'/>
  </field>
  <field number='854' name='QtyType' type='INT'>
   <value enum='0' description='UNITS'/>
   <value enum='1' description='CONTRACTS'/>
   <value enum='2' description='UNITS_OF_MEASURE_PER_TIME_UNIT'/>
  </field>
  <field number='855' name='SecondaryTrdType' type='INT'/>
  <field number='856' name='TradeReportType' type='INT'>
   <value enum='0' description='SUBMIT'/>
   <value enum='1' description='ALLEGED'/>
   <value enum='2' description='ACCEPT'/>
   <value enum='3' description='DECLINE'/>
   <value enum='4' description='ADDENDUM'/>
   <value enum='5' description='NO'/>
   <value enum='6' description='TRADE_REPORT_CANCEL'/>
   <value enum='7' description='LOCKED_IN'/>
   <value enum='8' description='DEFAULTED'/>
   <value enum='9' description='INVALID_CMTA'/>
   <value enum='10' description='PENDED'/>
   <value enum='11' description='ALLEGED_NEW'/>
   <value enum='12' description='ALLEGED_ADDENDUM'/>
   <value enum='13' description='ALLEGED_NO'/>
   <value enum='14' description='ALLEGED_TRADE_REPORT_CANCEL'/>
   <value enum='15' description='ALLEGED_TRADE_BREAK'/>
  </field>
  <field number='857' name='AllocNoOrdersType' type='INT'>
   <value enum='0' description='NOT_SPECIFIED'/>
   <value enum='1' description='EXPLICIT_LIST_PROVIDED'/>
  </field>
  <field number='858' name='SharedCommission' type='AMT'/>
  <field number='859' name='ConfirmReqID' type='STRING'/>
  <field number='860' name='AvgParPx' type='PRICE'/>
  <field number='861' name='ReportedPx' type='PRICE'/>
  <field number='862' name='NoCapacities' type='NUMINGROUP'/>
  <field number='863' name='OrderCapacityQty' type='QTY'/>
  <field number='864' name='NoEvents' type='NUMINGROUP'/>
  <field number='865' name='EventType' type='INT'>
   <value enum='1' description='PUT'/>
   <value enum='2' description='CALL'/>
   <value enum='3' description='TENDER'/>
   <value enum='4' description='SINKING_FUND_CALL'/>
   <value enum='5' description='ACTIVATION'/>
   <value enum='6' description='INACTIVIATION'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='866' name='EventDate' type='LOCALMKTDATE'/>
  <field number='867' name='EventPx' type='PRICE'/>
  <field number='868' name='EventText' type='STRING'/>
  <field number='869' name='PctAtRisk' type='PERCENTAGE'/>
  <field number='870' name='NoInstrAttrib' type='NUMINGROUP'/>
  <field number='871' name='InstrAttribType' type='INT'>
   <value enum='1' description='FLAT'/>
   <value enum='2' description='ZERO_COUPON'/>
   <value enum='3' description='INTEREST_BEARING'/>
   <value enum='4' description='NO_PERIODIC_PAYMENTS'/>
   <value enum='5' description='VARIABLE_RATE'/>
   <value enum='6' description='LESS_FEE_FOR_PUT'/>
   <value enum='7' description='STEPPED_COUPON'/>
   <value enum='8' description='COUPON_PERIOD'/>
   <value enum='9' description='WHEN'/>
   <value enum='10' description='ORIGINAL_ISSUE_DISCOUNT'/>
   <value enum='11' description='CALLABLE'/>
   <value enum='12' description='ESCROWED_TO_MATURITY'/>
   <value enum='13' description='ESCROWED_TO_REDEMPTION_DATE'/>
   <value enum='14' description='PRE_REFUNDED'/>
   <value enum='15' description='IN_DEFAULT'/>
   <value enum='16' description='UNRATED'/>
   <value enum='17' description='TAXABLE'/>
   <value enum='18' description='INDEXED'/>
   <value enum='19' description='SUBJECT_TO_ALTERNATIVE_MINIMUM_TAX'/>
   <value enum='20' description='ORIGINAL_ISSUE_DISCOUNT_PRICE'/>
   <value enum='21' description='CALLABLE_BELOW_MATURITY_VALUE'/>
   <value enum='22' description='CALLABLE_WITHOUT_NOTICE'/>
   <value enum='99' description='TEXT'/>
  </field>
  <field number='872' name='InstrAttribValue' type='STRING'/>
  <field number='873' name='DatedDate' type='LOCALMKTDATE'/>
  <field number='874' name='InterestAccrualDate' type='LOCALMKTDATE'/>
  <field number='875' name='CPProgram' type='INT'>
   <value enum='1' description='PROGRAM3A3'/>
   <value enum='2' description='PROGRAM42'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='876' name='CPRegType' type='STRING'/>
  <field number='877' name='UnderlyingCPProgram' type='STRING'/>
  <field number='878' name='UnderlyingCPRegType' type='STRING'/>
  <field number='879' name='UnderlyingQty' type='QTY'/>
  <field number='880' name='TrdMatchID' type='STRING'/>
  <field number='881' name='SecondaryTradeReportRefID' type='STRING'/>
  <field number='882' name='UnderlyingDirtyPrice' type='PRICE'/>
  <field number='883' name='UnderlyingEndPrice' type='PRICE'/>
  <field number='884' name='UnderlyingStartValue' type='AMT'/>
  <field number='885' name='UnderlyingCurrentValue' type='AMT'/>
  <field number='886' name='UnderlyingEndValue' type='AMT'/>
  <field number='887' name='NoUnderlyingStips' type='NUMINGROUP'/>
  <field number='888' name='UnderlyingStipType' type='STRING'/>
  <field number='889' name='UnderlyingStipValue' type='STRING'/>
  <field number='890' name='MaturityNetMoney' type='AMT'/>
  <field number='891' name='MiscFeeBasis' type='INT'>
   <value enum='0' description='ABSOLUTE'/>
   <value enum='1' description='PER_UNIT'/>
   <value enum='2' description='PERCENTAGE'/>
  </field>
  <field number='892' name='TotNoAllocs' type='INT'/>
  <field number='893' name='LastFragment' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='894' name='CollReqID' type='STRING'/>
  <field number='895' name='CollAsgnReason' type='INT'>
   <value enum='0' description='INITIAL'/>
   <value enum='1' description='SCHEDULED'/>
   <value enum='2' description='TIME_WARNING'/>
   <value enum='3' description='MARGIN_DEFICIENCY'/>
   <value enum='4' description='MARGIN_EXCESS'/>
   <value enum='5' description='FORWARD_COLLATERAL_DEMAND'/>
   <value enum='6' description='EVENT_OF_DEFAULT'/>
   <value enum='7' description='ADVERSE_TAX_EVENT'/>
  </field>
  <field number='896' name='CollInquiryQualifier' type='INT'>
   <value enum='0' description='TRADE_DATE'/>
   <value enum='1' description='GC_INSTRUMENT'/>
   <value enum='2' description='COLLATERAL_INSTRUMENT'/>
   <value enum='3' description='SUBSTITUTION_ELIGIBLE'/>
   <value enum='4' description='NOT_ASSIGNED'/>
   <value enum='5' description='PARTIALLY_ASSIGNED'/>
   <value enum='6' description='FULLY_ASSIGNED'/>
   <value enum='7' description='OUTSTANDING_TRADES'/>
  </field>
  <field number='897' name='NoTrades' type='NUMINGROUP'/>
  <field number='898' name='MarginRatio' type='PERCENTAGE'/>
  <field number='899' name='MarginExcess' type='AMT'/>
  <field number='900' name='TotalNetValue' type='AMT'/>
  <field number='901' name='CashOutstanding' type='AMT'/>
  <field number='902' name='CollAsgnID' type='STRING'/>
  <field number='903' name='CollAsgnTransType' type='INT'>
   <value enum='0' description='NEW'/>
   <value enum='1' description='REPLACE'/>
   <value enum='2' description='CANCEL'/>
   <value enum='3' description='RELEASE'/>
   <value enum='4' description='REVERSE'/>
  </field>
  <field number='904' name='CollRespID' type='STRING'/>
  <field number='905' name='CollAsgnRespType' type='INT'>
   <value enum='0' description='RECEIVED'/>
   <value enum='1' description='ACCEPTED'/>
   <value enum='2' description='DECLINED'/>
   <value enum='3' description='REJECTED'/>
  </field>
  <field number='906' name='CollAsgnRejectReason' type='INT'>
   <value enum='0' description='UNKNOWN_DEAL'/>
   <value enum='1' description='UNKNOWN_OR_INVALID_INSTRUMENT'/>
   <value enum='2' description='UNAUTHORIZED_TRANSACTION'/>
   <value enum='3' description='INSUFFICIENT_COLLATERAL'/>
   <value enum='4' description='INVALID_TYPE_OF_COLLATERAL'/>
   <value enum='5' description='EXCESSIVE_SUBSTITUTION'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='907' name='CollAsgnRefID' type='STRING'/>
  <field number='908' name='CollRptID' type='STRING'/>
  <field number='909' name='CollInquiryID' type='STRING'/>
  <field number='910' name='CollStatus' type='INT'>
   <value enum='0' description='UNASSIGNED'/>
   <value enum='1' description='PARTIALLY_ASSIGNED'/>
   <value enum='2' description='ASSIGNMENT_PROPOSED'/>
   <value enum='3' description='ASSIGNED'/>
   <value enum='4' description='CHALLENGED'/>
  </field>
  <field number='911' name='TotNumReports' type='INT'/>
  <field number='912' name='LastRptRequested' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='913' name='AgreementDesc' type='STRING'/>
  <field number='914' name='AgreementID' type='STRING'/>
  <field number='915' name='AgreementDate' type='LOCALMKTDATE'/>
  <field number='916' name='StartDate' type='LOCALMKTDATE'/>
  <field number='917' name='EndDate' type='LOCALMKTDATE'/>
  <field number='918' name='AgreementCurrency' type='CURRENCY'/>
  <field number='919' name='DeliveryType' type='INT'>
   <value enum='0' description='VERSUS_PAYMENT'/>
   <value enum='1' description='FREE'/>
   <value enum='2' description='TRI_PARTY'/>
   <value enum='3' description='HOLD_IN_CUSTODY'/>
  </field>
  <field number='920' name='EndAccruedInterestAmt' type='AMT'/>
  <field number='921' name='StartCash' type='AMT'/>
  <field number='922' name='EndCash' type='AMT'/>
  <field number='923' name='UserRequestID' type='STRING'/>
  <field number='924' name='UserRequestType' type='INT'>
   <value enum='1' description='LOG_ON_USER'/>
   <value enum='2' description='LOG_OFF_USER'/>
   <value enum='3' description='CHANGE_PASSWORD_FOR_USER'/>
   <value enum='4' description='REQUEST_INDIVIDUAL_USER_STATUS'/>
  </field>
  <field number='925' name='NewPassword' type='STRING'/>
  <field number='926' name='UserStatus' type='INT'>
   <value enum='1' description='LOGGED_IN'/>
   <value enum='2' description='NOT_LOGGED_IN'/>
   <value enum='3' description='USER_NOT_RECOGNISED'/>
   <value enum='4' description='PASSWORD_INCORRECT'/>
   <value enum='5' description='PASSWORD_CHANGED'/>
   <value enum='6' description='OTHER'/>
  </field>
  <field number='927' name='UserStatusText' type='STRING'/>
  <field number='928' name='StatusValue' type='INT'>
   <value enum='1' description='CONNECTED'/>
   <value enum='2' description='NOT_CONNECTED_UNEXPECTED'/>
   <value enum='3' description='NOT_CONNECTED_EXPECTED'/>
   <value enum='4' description='IN_PROCESS'/>
  </field>
  <field number='929' name='StatusText' type='STRING'/>
  <field number='930' name='RefCompID' type='STRING'/>
  <field number='931' name='RefSubID' type='STRING'/>
  <field number='932' name='NetworkResponseID' type='STRING'/>
  <field number='933' name='NetworkRequestID' type='STRING'/>
  <field number='934' name='LastNetworkResponseID' type='STRING'/>
  <field number='935' name='NetworkRequestType' type='INT'>
   <value enum='1' description='SNAPSHOT'/>
   <value enum='2' description='SUBSCRIBE'/>
   <value enum='4' description='STOP_SUBSCRIBING'/>
   <value enum='8' description='LEVEL_OF_DETAIL'/>
  </field>
  <field number='936' name='NoCompIDs' type='NUMINGROUP'/>
  <field number='937' name='NetworkStatusResponseType' type='INT'>
   <value enum='1' description='FULL'/>
   <value enum='2' description='INCREMENTAL_UPDATE'/>
  </field>
  <field number='938' name='NoCollInquiryQualifier' type='NUMINGROUP'/>
  <field number='939' name='TrdRptStatus' type='INT'>
   <value enum='0' description='ACCEPTED'/>
   <value enum='1' description='REJECTED'/>
   <value enum='3' description='ACCEPTED_WITH_ERRORS'/>
  </field>
  <field number='940' name='AffirmStatus' type='INT'>
   <value enum='1' description='RECEIVED'/>
   <value enum='2' description='CONFIRM_REJECTED'/>
   <value enum='3' description='AFFIRMED'/>
  </field>
  <field number='941' name='UnderlyingStrikeCurrency' type='CURRENCY'/>
  <field number='942' name='LegStrikeCurrency' type='CURRENCY'/>
  <field number='943' name='TimeBracket' type='STRING'/>
  <field number='944' name='CollAction' type='INT'>
   <value enum='0' description='RETAIN'/>
   <value enum='1' description='ADD'/>
   <value enum='2' description='REMOVE'/>
  </field>
  <field number='945' name='CollInquiryStatus' type='INT'>
   <value enum='0' description='ACCEPTED'/>
   <value enum='1' description='ACCEPTED_WITH_WARNINGS'/>
   <value enum='2' description='COMPLETED'/>
   <value enum='3' description='COMPLETED_WITH_WARNINGS'/>
   <value enum='4' description='REJECTED'/>
  </field>
  <field number='946' name='CollInquiryResult' type='INT'>
   <value enum='0' description='SUCCESSFUL'/>
   <value enum='1' description='INVALID_OR_UNKNOWN_INSTRUMENT'/>
   <value enum='2' description='INVALID_OR_UNKNOWN_COLLATERAL_TYPE'/>
   <value enum='3' description='INVALID_PARTIES'/>
   <value enum='4' description='INVALID_TRANSPORT_TYPE_REQUESTED'/>
   <value enum='5' description='INVALID_DESTINATION_REQUESTED'/>
   <value enum='6' description='NO_COLLATERAL_FOUND_FOR_THE_TRADE_SPECIFIED'/>
   <value enum='7' description='NO_COLLATERAL_FOUND_FOR_THE_ORDER_SPECIFIED'/>
   <value enum='8' description='COLLATERAL_INQUIRY_TYPE_NOT_SUPPORTED'/>
   <value enum='9' description='UNAUTHORIZED_FOR_COLLATERAL_INQUIRY'/>
   <value enum='99' description='OTHER'/>
  </field>
  <field number='947' name='StrikeCurrency' type='CURRENCY'/>
  <field number='948' name='NoNested3PartyIDs' type='NUMINGROUP'/>
  <field number='949' name='Nested3PartyID' type='STRING'/>
  <field number='950' name='Nested3PartyIDSource' type='CHAR'/>
  <field number='951' name='Nested3PartyRole' type='INT'/>
  <field number='952' name='NoNested3PartySubIDs' type='NUMINGROUP'/>
  <field number='953' name='Nested3PartySubID' type='STRING'/>
  <field number='954' name='Nested3PartySubIDType' type='INT'/>
  <field number='955' name='LegContractSettlMonth' type='MONTHYEAR'/>
  <field number='956' name='LegInterestAccrualDate' type='LOCALMKTDATE'/>
  <field number='957' name='NoStrategyParameters' type='NUMINGROUP'/>
  <field number='958' name='StrategyParameterName' type='STRING'/>
  <field number='959' name='StrategyParameterType' type='INT'>
   <value enum='1' description='INT'/>
   <value enum='2' description='LENGTH'/>
   <value enum='3' description='NUM_IN_GROUP'/>
   <value enum='4' description='SEQ_NUM'/>
   <value enum='5' description='TAG_NUM'/>
   <value enum='6' description='FLOAT'/>
   <value enum='7' description='QTY'/>
   <value enum='8' description='PRICE'/>
   <value enum='9' description='PRICE_OFFSET'/>
   <value enum='10' description='AMT'/>
   <value enum='11' description='PERCENTAGE'/>
   <value enum='12' description='CHAR'/>
   <value enum='13' description='BOOLEAN'/>
   <value enum='14' description='STRING'/>
   <value enum='15' description='MULTIPLE_CHAR_VALUE'/>
   <value enum='16' description='CURRENCY'/>
   <value enum='17' description='EXCHANGE'/>
   <value enum='18' description='MONTH_YEAR'/>
   <value enum='19' description='UTC_TIMESTAMP'/>
   <value enum='20' description='UTC_TIME_ONLY'/>
   <value enum='21' description='LOCAL_MKT_DATE'/>
   <value enum='22' description='UTC_DATE_ONLY'/>
   <value enum='23' description='DATA'/>
   <value enum='24' description='MULTIPLE_STRING_VALUE'/>
  </field>
  <field number='960' name='StrategyParameterValue' type='STRING'/>
  <field number='961' name='HostCrossID' type='STRING'/>
  <field number='962' name='SideTimeInForce' type='UTCTIMESTAMP'/>
  <field number='963' name='MDReportID' type='INT'/>
  <field number='964' name='SecurityReportID' type='INT'/>
  <field number='965' name='SecurityStatus' type='STRING'>
   <value enum='1' description='ACTIVE'/>
   <value enum='2' description='INACTIVE'/>
  </field>
  <field number='966' name='SettleOnOpenFlag' type='STRING'/>
  <field number='967' name='StrikeMultiplier' type='FLOAT'/>
  <field number='968' name='StrikeValue' type='FLOAT'/>
  <field number='969' name='MinPriceIncrement' type='FLOAT'/>
  <field number='970' name='PositionLimit' type='INT'/>
  <field number='971' name='NTPositionLimit' type='INT'/>
  <field number='972' name='UnderlyingAllocationPercent' type='PERCENTAGE'/>
  <field number='973' name='UnderlyingCashAmount' type='AMT'/>
  <field number='974' name='UnderlyingCashType' type='STRING'>
   <value enum='DIFF' description='DIFF'/>
   <value enum='FIXED' description='FIXED'/>
  </field>
  <field number='975' name='UnderlyingSettlementType' type='INT'>
   <value enum='2' description='T_PLUS1'/>
   <value enum='4' description='T_PLUS3'/>
   <value enum='5' description='T_PLUS4'/>
  </field>
  <field number='976' name='QuantityDate' type='LOCALMKTDATE'/>
  <field number='977' name='ContIntRptID' type='STRING'/>
  <field number='978' name='LateIndicator' type='BOOLEAN'/>
  <field number='979' name='InputSource' type='STRING'/>
  <field number='980' name='SecurityUpdateAction' type='CHAR'>
   <value enum='A' description='ADD'/>
   <value enum='D' description='DELETE'/>
   <value enum='M' description='MODIFY'/>
  </field>
  <field number='981' name='NoExpiration' type='NUMINGROUP'/>
  <field number='982' name='ExpType' type='INT'>
   <value enum='1' description='AUTO_EXERCISE'/>
   <value enum='2' description='NON_AUTO_EXERCISE'/>
   <value enum='3' description='FINAL_WILL_BE_EXERCISED'/>
   <value enum='4' description='CONTRARY_INTENTION'/>
   <value enum='5' description='DIFFERENCE'/>
  </field>
  <field number='983' name='ExpQty' type='QTY'/>
  <field number='984' name='NoUnderlyingAmounts' type='NUMINGROUP'/>
  <field number='985' name='UnderlyingPayAmount' type='AMT'/>
  <field number='986' name='UnderlyingCollectAmount' type='AMT'/>
  <field number='987' name='UnderlyingSettlementDate' type='LOCALMKTDATE'/>
  <field number='988' name='UnderlyingSettlementStatus' type='STRING'/>
  <field number='989' name='SecondaryIndividualAllocID' type='STRING'/>
  <field number='990' name='LegReportID' type='STRING'/>
  <field number='991' name='RndPx' type='PRICE'/>
  <field number='992' name='IndividualAllocType' type='INT'>
   <value enum='1' description='SUB_ALLOCATE'/>
   <value enum='2' description='THIRD_PARTY_ALLOCATION'/>
  </field>
  <field number='993' name='AllocCustomerCapacity' type='STRING'/>
  <field number='994' name='TierCode' type='STRING'/>
  <field number='996' name='UnitofMeasure' type='STRING'>
   <value enum='t' description='METRIC_TONS'/>
   <value enum='Bu' description='BUSHELS'/>
   <value enum='tn' description='TONS'/>
   <value enum='Bbl' description='BARRELS'/>
   <value enum='Bcf' description='BILLION_CUBIC_FEET'/>
   <value enum='Gal' description='GALLONS'/>
   <value enum='MWh' description='MEGAWATT_HOURS'/>
   <value enum='USD' description='US_DOLLARS'/>
   <value enum='lbs' description='POUNDS'/>
   <value enum='MMBtu' description='ONE_MILLION_BTU'/>
   <value enum='MMbbl' description='MILLION_BARRELS'/>
   <value enum='oz_tr' description='TROY_OUNCES'/>
  </field>
  <field number='997' name='TimeUnit' type='STRING'>
   <value enum='D' description='DAY'/>
   <value enum='H' description='HOUR'/>
   <value enum='S' description='SECOND'/>
   <value enum='Mo' description='MONTH'/>
   <value enum='Wk' description='WEEK'/>
   <value enum='Yr' description='YEAR'/>
   <value enum='Min' description='MINUTE'/>
  </field>
  <field number='998' name='UnderlyingUnitofMeasure' type='STRING'/>
  <field number='999' name='LegUnitofMeasure' type='STRING'/>
  <field number='1000' name='UnderlyingTimeUnit' type='STRING'/>
  <field number='1001' name='LegTimeUnit' type='STRING'/>
  <field number='1002' name='AllocMethod' type='INT'>
   <value enum='1' description='AUTOMATIC'/>
   <value enum='2' description='GUARANTOR'/>
   <value enum='3' description='MANUAL'/>
  </field>
  <field number='1003' name='TradeID' type='STRING'/>
  <field number='1005' name='SideTradeReportID' type='STRING'/>
  <field number='1006' name='SideFillStationCd' type='STRING'/>
  <field number='1007' name='SideReasonCd' type='STRING'/>
  <field number='1008' name='SideTrdSubTyp' type='INT'/>
  <field number='1009' name='SideQty' type='INT'/>
  <field number='1011' name='MessageEventSource' type='STRING'/>
  <field number='1012' name='SideTrdRegTimestamp' type='UTCTIMESTAMP'/>
  <field number='1013' name='SideTrdRegTimestampType' type='INT'/>
  <field number='1014' name='SideTrdRegTimestampSrc' type='STRING'/>
  <field number='1015' name='AsOfIndicator' type='CHAR'>
   <value enum='0' description='FALSE'/>
   <value enum='1' description='TRUE'/>
  </field>
  <field number='1016' name='NoSideTrdRegTS' type='NUMINGROUP'/>
  <field number='1017' name='LegOptionRatio' type='FLOAT'/>
  <field number='1018' name='NoInstrumentParties' type='NUMINGROUP'/>
  <field number='1019' name='InstrumentPartyID' type='STRING'/>
  <field number='1020' name='TradeVolume' type='QTY'/>
  <field number='1021' name='MDBookType' type='INT'>
   <value enum='1' description='TOP_OF_BOOK'/>
   <value enum='2' description='PRICE_DEPTH'/>
   <value enum='3' description='ORDER_DEPTH'/>
  </field>
  <field number='1022' name='MDFeedType' type='STRING'/>
  <field number='1023' name='MDPriceLevel' type='INT'/>
  <field number='1024' name='MDOriginType' type='INT'>
   <value enum='0' description='BOOK'/>
   <value enum='1' description='OFF_BOOK'/>
   <value enum='2' description='CROSS'/>
  </field>
  <field number='1025' name='FirstPx' type='PRICE'/>
  <field number='1026' name='MDEntrySpotRate' type='FLOAT'/>
  <field number='1027' name='MDEntryForwardPoints' type='PRICEOFFSET'/>
  <field number='1028' name='ManualOrderIndicator' type='BOOLEAN'/>
  <field number='1029' name='CustDirectedOrder' type='BOOLEAN'/>
  <field number='1030' name='ReceivedDeptID' type='STRING'/>
  <field number='1031' name='CustOrderHandlingInst' type='MULTIPLESTRINGVALUE'>
   <value enum='IO' description='IMBALANCE_ONLY'/>
   <value enum='NH' description='NOT_HELD'/>
   <value enum='TS' description='TRAILING_STOP'/>
   <value enum='ADD' description='ADD_ON_ORDER'/>
   <value enum='AON' description='ALL_OR_NONE'/>
   <value enum='CNH' description='CASH_NOT_HELD'/>
   <value enum='DIR' description='DIRECTED_ORDER'/>
   <value enum='E.W' description='EXCHANGE_FOR_PHYSICAL_TRANSACTION'/>
   <value enum='FOK' description='FILL_OR_KILL'/>
   <value enum='IOC' description='IMMEDIATE_OR_CANCEL'/>
   <value enum='LOC' description='LIMIT_ON_CLOSE'/>
   <value enum='LOO' description='LIMIT_ON_OPEN'/>
   <value enum='MAC' description='MARKET_AT_CLOSE'/>
   <value enum='MAO' description='MARKET_AT_OPEN'/>
   <value enum='MOC' description='MARKET_ON_CLOSE'/>
   <value enum='MOO' description='MARKET_ON_OPEN'/>
   <value enum='MQT' description='MINIMUM_QUANTITY'/>
   <value enum='OVD' description='OVER_THE_DAY'/>
   <value enum='PEG' description='PEGGED'/>
   <value enum='RSV' description='RESERVE_SIZE_ORDER'/>
   <value enum='S.W' description='STOP_STOCK_TRANSACTION'/>
   <value enum='SCL' description='SCALE'/>
   <value enum='TMO' description='TIME_ORDER'/>
   <value enum='WRK' description='WORK'/>
  </field>
  <field number='1032' name='OrderHandlingInstSource' type='INT'>
   <value enum='1' description='NASDOATS'/>
  </field>
  <field number='1033' name='DeskType' type='STRING'>
   <value enum='A' description='AGENCY'/>
   <value enum='D' description='DERIVATIVES'/>
   <value enum='O' description='OTHER'/>
   <value enum='S' description='SALES'/>
   <value enum='T' description='TRADING'/>
   <value enum='AR' description='ARBITRAGE'/>
   <value enum='IN' description='INTERNATIONAL'/>
   <value enum='IS' description='INSTITUTIONAL'/>
   <value enum='PF' description='PREFERRED_TRADING'/>
   <value enum='PR' description='PROPRIETARY'/>
   <value enum='PT' description='PROGRAM_TRADING'/>
  </field>
  <field number='1034' name='DeskTypeSource' type='INT'>
   <value enum='1' description='NASDOATS'/>
  </field>
  <field number='1035' name='DeskOrderHandlingInst' type='MULTIPLESTRINGVALUE'>
   <value enum='IO' description='IMBALANCE_ONLY'/>
   <value enum='NH' description='NOT_HELD'/>
   <value enum='TS' description='TRAILING_STOP'/>
   <value enum='ADD' description='ADD_ON_ORDER'/>
   <value enum='AON' description='ALL_OR_NONE'/>
   <value enum='CNH' description='CASH_NOT_HELD'/>
   <value enum='DIR' description='DIRECTED_ORDER'/>
   <value enum='E.W' description='EXCHANGE_FOR_PHYSICAL_TRANSACTION'/>
   <value enum='FOK' description='FILL_OR_KILL'/>
   <value enum='IOC' description='IMMEDIATE_OR_CANCEL'/>
   <value enum='LOC' description='LIMIT_ON_CLOSE'/>
   <value enum='LOO' description='LIMIT_ON_OPEN'/>
   <value enum='MAC' description='MARKET_AT_CLOSE'/>
   <value enum='MAO' description='MARKET_AT_OPEN'/>
   <value enum='MOC' description='MARKET_ON_CLOSE'/>
   <value enum='MOO' description='MARKET_ON_OPEN'/>
   <value enum='MQT' description='MINIMUM_QUANTITY'/>
   <value enum='OVD' description='OVER_THE_DAY'/>
   <value enum='PEG' description='PEGGED'/>
   <value enum='RSV' description='RESERVE_SIZE_ORDER'/>
   <value enum='S.W' description='STOP_STOCK_TRANSACTION'/>
   <value enum='SCL' description='SCALE'/>
   <value enum='TMO' description='TIME_ORDER'/>
   <value enum='WRK' description='WORK'/>
  </field>
  <field number='1036' name='ExecAckStatus' type='CHAR'>
   <value enum='0' description='RECEIVED'/>
   <value enum='1' description='ACCEPTED'/>
   <value enum='2' description='DON'/>
  </field>
  <field number='1037' name='UnderlyingDeliveryAmount' type='AMT'/>
  <field number='1038' name='UnderlyingCapValue' type='AMT'/>
  <field number='1039' name='UnderlyingSettlMethod' type='STRING'/>
  <field number='1040' name='SecondaryTradeID' type='STRING'/>
  <field number='1041' name='FirmTradeID' type='STRING'/>
  <field number='1042' name='SecondaryFirmTradeID' type='STRING'/>
  <field number='1043' name='CollApplType' type='INT'>
   <value enum='0' description='SPECIFIC_DEPOSIT'/>
   <value enum='1' description='GENERAL'/>
  </field>
  <field number='1044' name='UnderlyingAdjustedQuantity' type='QTY'/>
  <field number='1045' name='UnderlyingFXRate' type='FLOAT'/>
  <field number='1046' name='UnderlyingFXRateCalc' type='CHAR'>
   <value enum='D' description='DIVIDE'/>
   <value enum='M' description='MULTIPLY'/>
  </field>
  <field number='1047' name='AllocPositionEffect' type='CHAR'>
   <value enum='C' description='CLOSE'/>
   <value enum='F' description='FIFO'/>
   <value enum='O' description='OPEN'/>
   <value enum='R' description='ROLLED'/>
  </field>
  <field number='1048' name='DealingCapacity' type='PRICEOFFSET'/>
  <field number='1049' name='InstrmtAssignmentMethod' type='CHAR'>
   <value enum='P' description='PRO_RATA'/>
   <value enum='R' description='RANDOM'/>
  </field>
  <field number='1050' name='InstrumentPartyIDSource' type='CHAR'/>
  <field number='1051' name='InstrumentPartyRole' type='INT'/>
  <field number='1052' name='NoInstrumentPartySubIDs' type='NUMINGROUP'/>
  <field number='1053' name='InstrumentPartySubID' type='STRING'/>
  <field number='1054' name='InstrumentPartySubIDType' type='INT'/>
  <field number='1055' name='PositionCurrency' type='STRING'/>
  <field number='1056' name='CalculatedCcyLastQty' type='QTY'/>
  <field number='1057' name='AggressorIndicator' type='BOOLEAN'>
   <value enum='N' description='NO'/>
   <value enum='Y' description='YES'/>
  </field>
  <field number='1058' name='NoUndlyInstrumentParties' type='NUMINGROUP'/>
  <field number='1059' name='UndlyInstrumentPartyID' type='STRING'/>
  <field number='1060' name='UndlyInstrumentPartyIDSource' type='CHAR'/>
  <field number='1061' name='UndlyInstrumentPartyRole' type='INT'/>
  <field number='1062' name='NoUndlyInstrumentPartySubIDs' type='NUMINGROUP'/>
  <field number='1063' name='UndlyInstrumentPartySubID' type='STRING'/>
  <field number='1064' name='UndlyInstrumentPartySubIDType' type='INT'/>
  <field number='1065' name='BidSwapPoints' type='PRICEOFFSET'/>
  <field number='1066' name='OfferSwapPoints' type='PRICEOFFSET'/>
  <field number='1067' name='LegBidForwardPoints' type='PRICEOFFSET'/>
  <field number='1068' name='LegOfferForwardPoints' type='PRICEOFFSET'/>
  <field number='1069' name='SwapPoints' type='PRICEOFFSET'/>
  <field number='1070' name='MDQuoteType' type='INT'>
   <value enum='0' description='INDICATIVE'/>
   <value enum='1' description='TRADEABLE'/>
   <value enum='2' description='RESTRICTED_TRADEABLE'/>
   <value enum='3' description='COUNTER'/>
   <value enum='4' description='INDICATIVE_AND_TRADEABLE'/>
  </field>
  <field number='1071' name='LastSwapPoints' type='PRICEOFFSET'/>
  <field number='1072' name='SideGrossTradeAmt' type='AMT'/>
  <field number='1073' name='LegLastForwardPoints' type='PRICEOFFSET'/>
  <field number='1074' name='LegCalculatedCcyLastQty' type='QTY'/>
  <field number='1075' name='LegGrossTradeAmt' type='AMT'/>
  <field number='1079' name='MaturityTime' type='TZTIMEONLY'/>
  <field number='1080' name='RefOrderID' type='STRING'/>
  <field number='1081' name='RefOrderIDSource' type='CHAR'>
   <value enum='0' description='SECONDARY_ORDER_ID'/>
   <value enum='1' description='ORDER_ID'/>
   <value enum='2' description='MD_ENTRY_ID'/>
   <value enum='3' description='QUOTE_ENTRY_ID'/>
  </field>
  <field number='1082' name='SecondaryDisplayQty' type='QTY'/>
  <field number='1083' name='DisplayWhen' type='CHAR'>
   <value enum='1' description='IMMEDIATE'/>
   <value enum='2' description='EXHAUST'/>
  </field>
  <field number='1084' name='DisplayMethod' type='CHAR'>
   <value enum='1' description='INITIAL'/>
   <value enum='2' description='NEW'/>
   <value enum='3' description='RANDOM'/>
  </field>
  <field number='1085' name='DisplayLowQty' type='QTY'/>
  <field number='1086' name='DisplayHighQty' type='QTY'/>
  <field number='1087' name='DisplayMinIncr' type='QTY'/>
  <field number='1088' name='RefreshQty' type='QTY'/>
  <field number='1089' name='MatchIncrement' type='QTY'/>
  <field number='1090' name='MaxPriceLevels' type='INT'/>
  <field number='1091' name='PreTradeAnonymity' type='BOOLEAN'/>
  <field number='1092' name='PriceProtectionScope' type='CHAR'>
   <value enum='0' description='NONE'/>
   <value enum='1' description='LOCAL'/>
   <value enum='2' description='NATIONAL'/>
   <value enum='3' description='GLOBAL'/>
  </field>
  <field number='1093' name='LotType' type='CHAR'>
   <value enum='1' description='ODD_LOT'/>
   <value enum='2' description='ROUND_LOT'/>
   <value enum='3' description='BLOCK_LOT'/>
  </field>
  <field number='1094' name='PegPriceType' type='INT'>
   <value enum='1' description='LAST_PEG'/>
   <value enum='2' description='MID_PRICE_PEG'/>
   <value enum='3' description='OPENING_PEG'/>
   <value enum='4' description='MARKET_PEG'/>
   <value enum='5' description='PRIMARY_PEG'/>
   <value enum='6' description='FIXED_PEG_TO_LOCAL_BEST_BID_OR_OFFER_AT_TIME_OF_ORDER'/>
   <value enum='7' description='PEG_TO_VWAP'/>
   <value enum='8' description='TRAILING_STOP_PEG'/>
   <value enum='9' description='PEG_TO_LIMIT_PRICE'/>
  </field>
  <field number='1095' name='PeggedRefPrice' type='PRICE'/>
  <field number='1096' name='PegSecurityIDSource' type='STRING'/>
  <field number='1097' name='PegSecurityID' type='STRING'/>
  <field number='1098' name='PegSymbol' type='STRING'/>
  <field number='1099' name='PegSecurityDesc' type='STRING'/>
  <field number='1100' name='TriggerType' type='CHAR'>
   <value enum='1' description='PARTIAL_EXECUTION'/>
   <value enum='2' description='SPECIFIED_TRADING_SESSION'/>
   <value enum='3' description='NEXT_AUCTION'/>
   <value enum='4' description='PRICE_MOVEMENT'/>
  </field>
  <field number='1101' name='TriggerAction' type='CHAR'>
   <value enum='1' description='ACTIVATE'/>
   <value enum='2' description='MODIFY'/>
   <value enum='3' description='CANCEL'/>
  </field>
  <field number='1102' name='TriggerPrice' type='PRICE'/>
  <field number='1103' name='TriggerSymbol' type='STRING'/>
  <field number='1104' name='TriggerSecurityID' type='STRING'/>
  <field number='1105' name='TriggerSecurityIDSource' type='STRING'/>
  <field number='1106' name='TriggerSecurityDesc' type='STRING'/>
  <field number='1107' name='TriggerPriceType' type='CHAR'>
   <value enum='1' description='BEST_OFFER'/>
   <value enum='2' description='LAST_TRADE'/>
   <value enum='3' description='BEST_BID'/>
   <value enum='4' description='BEST_BID_OR_LAST_TRADE'/>
   <value enum='5' description='BEST_OFFER_OR_LAST_TRADE'/>
   <value enum='6' description='BEST_MID'/>
  </field>
  <field number='1108' name='TriggerPriceTypeScope' type='CHAR'>
   <value enum='0' description='NONE'/>
   <value enum='1' description='LOCAL'/>
   <value enum='2' description='NATIONAL'/>
   <value enum='3' description='GLOBAL'/>
  </field>
  <field number='1109' name='TriggerPriceDirection' type='CHAR'>
   <value enum='D' description='DOWN'/>
   <value enum='U' description='UP'/>
  </field>
  <field number='1110' name='TriggerNewPrice' type='PRICE'/>
  <field number='1111' name='TriggerOrderType' type='CHAR'>
   <value enum='1' description='MARKET'/>
   <value enum='2' description='LIMIT'/>
  </field>
  <field number='1112' name='TriggerNewQty' type='QTY'/>
  <field number='1113' name='TriggerTradingSessionID' type='STRING'/>
  <field number='1114' name='TriggerTradingSessionSubID' type='STRING'/>
  <field number='1115' name='OrderCategory' type='CHAR'>
   <value enum='1' description='ORDER'/>
   <value enum='2' description='QUOTE'/>
   <value enum='3' description='PRIVATELY_NEGOTIATED_TRADE'/>
   <value enum='4' description='MULTILEG_ORDER'/>
   <value enum='5' description='LINKED_ORDER'/>
   <value enum='6' description='QUOTE_REQUEST'/>
   <value enum='7' description='IMPLIED_ORDER'/>
   <value enum='8' description='CROSS_ORDER'/>
  </field>
  <field number='1116' name='NoRootPartyIDs' type='NUMINGROUP'/>
  <field number='1117' name='RootPartyID' type='STRING'/>
  <field number='1118' name='RootPartyIDSource' type='CHAR'/>
  <field number='1119' name='RootPartyRole' type='INT'/>
  <field number='1120' name='NoRootPartySubIDs' type='NUMINGROUP'/>
  <field number='1121' name='RootPartySubID' type='STRING'/>
  <field number='1122' name='RootPartySubIDType' type='INT'/>
  <field number='1123' name='TradeHandlingInstr' type='CHAR'>
   <value enum='0' description='TRADE_CONFIRMATION'/>
   <value enum='1' description='TWO_PARTY_REPORT'/>
   <value enum='2' description='ONE_PARTY_REPORT_FOR_MATCHING'/>
   <value enum='3' description='ONE_PARTY_REPORT_FOR_PASS_THROUGH'/>
   <value enum='4' description='AUTOMATED_FLOOR_ORDER_ROUTING'/>
  </field>
  <field number='1124' name='OrigTradeHandlingInstr' type='CHAR'/>
  <field number='1125' name='OrigTradeDate' type='LOCALMKTDATE'/>
  <field number='1126' name='OrigTradeID' type='STRING'/>
  <field number='1127' name='OrigSecondaryTradeID' type='STRING'/>
  <field number='1128' name='ApplVerID' type='STRING'>
   <value enum='0' description='FIX27'/>
   <value enum='1' description='FIX30'/>
   <value enum='2' description='FIX40'/>
   <value enum='3' description='FIX41'/>
   <value enum='4' description='FIX42'/>
   <value enum='5' description='FIX43'/>
   <value enum='6' description='FIX44'/>
   <value enum='7' description='FIX50'/>
   <value enum='8' description='FIX50_SP1'/>
   <value enum='9' description='FIX50_SP2'/>
  </field>
  <field number='1129' name='CstmApplVerID' type='STRING'/>
  <field number='1130' name='RefApplVerID' type='STRING'/>
  <field number='1131' name='RefCstmApplVerID' type='STRING'/>
  <field number='1132' name='TZTransactTime' type='TZTIMESTAMP'/>
  <field number='1133' name='ExDestinationIDSource' type='CHAR'>
   <value enum='B' description='BIC'/>
   <value enum='C' description='GENERAL_IDENTIFIER'/>
   <value enum='D' description='PROPRIETARY'/>
   <value enum='E' description='ISO_COUNTRY_CODE'/>
   <value enum='G' description='MIC'/>
  </field>
  <field number='1134' name='ReportedPxDiff' type='BOOLEAN'/>
  <field number='1135' name='RptSys' type='STRING'/>
  <field number='1136' name='AllocClearingFeeIndicator' type='STRING'/>
  <field number='1137' name='DefaultApplVerID' type='STRING'/>
  <field number='1138' name='DisplayQty' type='QTY'/>
  <field number='1139' name='ExchangeSpecialInstructions' type='STRING'/>
  <field number='1156' name='ApplExtID' type='INT'/>
  <field number='1400' name='EncryptedPasswordMethod' type='INT'/>
  <field number='1401' name='EncryptedPasswordLen' type='LENGTH'/>
  <field number='1402' name='EncryptedPassword' type='DATA'/>
  <field number='1403' name='EncryptedNewPasswordLen' type='LENGTH'/>
  <field number='1404' name='EncryptedNewPassword' type='DATA'/>
  <field number='1406' name='RefApplExtID' type='INT'/>
  <field number='1407' name='DefaultApplExtID' type='INT'/>
  <field number='1408' name='DefaultCstmApplVerID' type='STRING'/>
  <field number='1409' name='SessionStatus' type='INT'>
   <value enum='0' description='SESSION_ACTIVE'/>
   <value enum='1' description='SESSION_PASSWORD_CHANGED'/>
   <value enum='2' description='SESSION_PASSWORD_DUE_TO_EXPIRE'/>
   <value enum='3' description='NEW_SESSION_PASSWORD_DOES_NOT_COMPLY_WITH_POLICY'/>
   <value enum='4' description='SESSION_LOGOUT_COMPLETE'/>
   <value enum='5' description='INVALID_USERNAME_OR_PASSWORD'/>
   <value enum='6' description='ACCOUNT_LOCKED'/>
   <value enum='7' description='LOGONS_ARE_NOT_ALLOWED_AT_THIS_TIME'/>
   <value enum='8' description='PASSWORD_EXPIRED'/>
  </field>
 </fields>
</fix>
