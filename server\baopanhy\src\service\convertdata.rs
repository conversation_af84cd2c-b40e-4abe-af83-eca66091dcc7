use std::collections::HashMap;

use once_cell::sync::OnceCell;
use server_protoes::{ExecMsg, ExecType, MsgContent, MsgType as InnerMsgType, RouterMsg};
use tokio::sync::RwLock;
use tracing::info;

use crate::{
    entities::prelude::SysTradeChannel,
    service::{OrderStatus, ResponseType},
};

use super::{CancelOrder, Exchange, Handlinst, MsgType, OrdType, OrderEvent, Qfstate, TradingOrder};
use lazy_static::lazy_static;

pub static ACCOUNT: OnceCell<String> = OnceCell::new();
lazy_static! {
    pub static ref CHANNEL_INFO: RwLock<HashMap<i64, SysTradeChannel>> = RwLock::new(HashMap::new());
}

pub async fn init_channel_info(channel: Vec<SysTradeChannel>) {
    info!("初始化channel_info");
    let mut channel_info = CHANNEL_INFO.write().await;
    channel.iter().for_each(|x| {
        channel_info.insert(x.id, x.clone());
    });
}

pub async fn init_account(account: &str) {
    info!("初始化account");
    ACCOUNT.set(account.to_owned()).expect("初始化account失败");
}

pub async fn order_from_router_to_api(router_msg: &RouterMsg) -> (Option<TradingOrder>, Option<CancelOrder>) {
    //只处理Order消息
    if router_msg.msg_type() != InnerMsgType::Order {
        return (None, None);
    }
    let msg_content = match &router_msg.msg_content {
        Some(content) => content,
        None => return (None, None),
    };
    let order_msg = match &msg_content.order_msg {
        Some(msg) => msg,
        None => return (None, None),
    };

    match order_msg.order_type() {
        server_protoes::OrderType::Place => {
            // 下单
            let mut new_order = TradingOrder::default();
            new_order.msg_type = MsgType::NewOrder.to_string();
            new_order.account = match ACCOUNT.get() {
                Some(account) => account.to_string(),
                None => return (None, None),
            };
            new_order.cl_ord_id = format!("{}-{}", order_msg.channel_id, order_msg.order_id);
            new_order.side = order_msg.order_direction.to_string();
            new_order.ord_type = OrdType::LimitOrder.to_string();
            new_order.handl_inst = Handlinst::DMA.to_string();
            new_order.time_in_force = "0".to_string();
            new_order.order_qty = order_msg.order_qty as f64;
            new_order.currency = order_msg.currency_no.to_string();
            new_order.price = order_msg.order_price as f64;
            new_order.security_type = "CS".to_string();
            new_order.symbol = order_msg.stock_code.to_string();
            let channel_info_map = CHANNEL_INFO.read().await;
            let channel_info = {
                match channel_info_map.get(&order_msg.channel_id) {
                    Some(info) => info,
                    None => return (None, None),
                }
            };

            new_order.security_exchange = match (channel_info.qfstate == Qfstate::Yes as i8, order_msg.exchange_id) {
                (true, 101) => Exchange::XSHG.to_string(),
                (true, 102) => Exchange::XSHE.to_string(),
                (false, 101) => Exchange::SHSC.to_string(),
                (false, 102) => Exchange::SZSC.to_string(),
                _ => Exchange::XHKG.to_string(),
            };

            (Some(new_order), None)
        }
        server_protoes::OrderType::Cancel => {
            // 撤单
            let mut cancel_order = CancelOrder::default();
            cancel_order.msgtype = MsgType::CancelOrder.to_string();
            cancel_order.orderid = order_msg.brk_order_id.to_string();
            cancel_order.clcancelid = format!("{}-{}", order_msg.channel_id, order_msg.order_id);
            (None, Some(cancel_order))
        }
        _ => (None, None),
    }
}

pub async fn exec_from_api_to_router(api_exec: &OrderEvent) -> Option<RouterMsg> {
    // 只处理 Order 响应
    if api_exec.rsp_type != ResponseType::Order.to_string() {
        return None;
    }
    let mut resp = RouterMsg::default();
    let mut exec_msg = ExecMsg::default();
    let mut msg_content = MsgContent::default();

    // let (channel_id, order_id) = if let Some((ch, od)) = api_exec.cl_ord_id.split_once('-') {
    //     (ch.parse().unwrap_or_default(), od.parse().unwrap_or_default())
    // } else {
    //     (0, api_exec.cl_ord_id.parse().unwrap_or_default())
    // };
    let (channel_id, order_id) = api_exec
        .cl_ord_id
        .split_once('-')
        .and_then(|(ch, od)| Some((ch.parse().ok()?, od.parse().ok()?)))
        .unwrap_or((0, api_exec.cl_ord_id.parse().unwrap_or_default()));

    exec_msg.order_id = order_id;
    exec_msg.channel_id = channel_id;
    exec_msg.channel_type = 1;

    // match api_exec.rsp_type.as_str() {
    //     x if x == ResponseType::Order.to_string() =>
    match api_exec.ord_status.as_str() {
        y if y == OrderStatus::New.to_string() => {
            //确认
            exec_msg.exec_type = ExecType::Confirm as i32;
            exec_msg.brk_order_id = api_exec.order_id.clone();
        }
        y if y == OrderStatus::PartiallyFilled.to_string() || y == OrderStatus::Filled.to_string() => {
            //成交 部成
            exec_msg.exec_type = ExecType::Filled as i32;
            exec_msg.exec_id = api_exec.biz_id.to_owned();
            exec_msg.exec_price = api_exec.transact_px;
            exec_msg.exec_qty = api_exec.transact_qty as i32;
            exec_msg.exec_time = api_exec.transact_time.clone().unwrap_or_default();
            exec_msg.brk_order_id = api_exec.order_id.clone();
            exec_msg.order_direction = api_exec.side.parse().unwrap_or_default();
        }
        y if y == OrderStatus::Canceled.to_string() => {
            //撤单
            exec_msg.exec_type = ExecType::Canceled as i32;
            exec_msg.brk_order_id = api_exec.order_id.clone();
            exec_msg.exec_qty = (api_exec.order_qty - api_exec.cum_qty) as i32;
            exec_msg.order_direction = api_exec.side.parse().unwrap_or_default();
            exec_msg.memo = api_exec.return_message.clone().unwrap_or_default();
        }
        y if y == OrderStatus::Rejected.to_string() => {
            //拒绝
            exec_msg.exec_type = ExecType::Rejected as i32;
            exec_msg.brk_order_id = api_exec.order_id.clone();
            exec_msg.memo = api_exec.return_message.clone().unwrap_or_default();
        }
        _ => {}
    }
    //     x if x == ResponseType::Cancel.to_string() => return None,
    //     _ => return None,
    // }

    msg_content.exec_msg = Some(exec_msg);
    resp.msg_type = InnerMsgType::Exec as i32;
    resp.msg_content = Some(msg_content);
    Some(resp)
}

//废单/拒绝撤单
pub async fn rejected(req: &RouterMsg, msg: &str) -> RouterMsg {
    let mut resp = RouterMsg::default();
    let mut exec_msg = ExecMsg::default();
    let mut msg_content = MsgContent::default();
    let order_msg = req.msg_content.clone().unwrap_or_default().order_msg.unwrap_or_default();

    exec_msg.exec_type = ExecType::Rejected as i32;
    exec_msg.order_id = order_msg.order_id.clone();
    exec_msg.brk_order_id = order_msg.brk_order_id.clone();
    exec_msg.channel_id = order_msg.channel_id;
    exec_msg.channel_type = order_msg.channel_type;
    exec_msg.memo = msg.to_string();
    if order_msg.cancel_id != 0 {
        exec_msg.cancel_id = order_msg.cancel_id;
    }

    msg_content.exec_msg = Some(exec_msg);
    resp.msg_type = InnerMsgType::Exec as i32;
    resp.msg_content = Some(msg_content);
    resp
}
