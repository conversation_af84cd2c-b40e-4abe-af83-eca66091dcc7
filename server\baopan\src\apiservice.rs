use crate::{Settings, from_api_to_router, from_router_to_api, logon_api};
use async_channel::unbounded;
use futures::{SinkExt, StreamExt};
use orderrouterclient::OrderRouterClientV2;
use server_protoes::*;
use std::error::Error;
use std::sync::Arc;
use tokio::net::TcpStream;
use tokio_util::codec::{FramedRead, FramedWrite};
use tracing::*;

#[derive(Debu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct ApiService {
    setting: Arc<Settings>,
}

impl ApiService {
    pub async fn new(setting: &Settings) -> Self {
        ApiService {
            setting: Arc::new(setting.clone()),
        }
    }

    pub async fn run_service(&self) -> Result<(), Box<dyn Error>> {
        //路由中心 ------>
        let (tx_order_from_router, rx_order_from_router) = unbounded::<RouterMsg>();
        // ------> 路由中心
        let (tx_repay_to_router, rx_repay_to_router) = unbounded::<RouterMsg>();

        let mut orderrouterclient = OrderRouterClientV2::new(
            &self.setting.system.routerserver.to_string(),
            &self.setting.system.channels,
            tx_order_from_router.clone(),
            rx_repay_to_router.clone(),
        )
        .await;
        let mut retry_interval = tokio::time::interval(std::time::Duration::from_secs(3));
        let tx = tx_repay_to_router.clone();

        tokio::spawn(async move {
            retry_interval.tick().await;
            loop {
                tokio::select! {
                    _ = retry_interval.tick() => {
                        if let Err(err) = orderrouterclient.order_transfer(tx.clone()).await {
                            error!("{:?}", err);
                        }
                    }
                }
            }
        });

        // let _ = std::thread::sleep(std::time::Duration::from_secs(5));

        // // test
        // let mut time = tokio::time::interval(std::time::Duration::from_secs(6000));
        // tokio::spawn(async move {
        //     // time.tick().await;
        //     loop {
        //         tokio::select! {
        //             _ = time.tick() => {
        //                 let mut req = RouterMsg::default();
        //                 // let mut head = TaMessageHeader::default();
        //                 // head.message_type = MessageType::PlaceOrder as i32;
        //                 req.msg_type = MsgType::Order as i32;
        //                 let mut order = OrderMsg::default();
        //                 order.order_id = utils::current_timestamp();
        //                 order.stock_code = "300012".to_owned();
        //                 order.order_type = 1;
        //                 order.order_direction = 1;
        //                 order.order_price = 12.48;
        //                 order.price_type = 2;
        //                 order.order_qty = 700;
        //                 order.brk_order_id = 0.to_owned().to_string();
        //                 order.exchange_id = 102;
        //                 let mut msg_content = MsgContent::default();
        //                 msg_content.order_msg = Some(order);
        //                 req.msg_content = Some(msg_content);
        //                 if let Err(e) =  tx_order_from_router.send(req.clone()).await {
        //                     error!("{:?}", e);
        //                 }
        //             }
        //         }
        //     }
        // });

        let addr = format!("{}", self.setting.system.bpserver);
        info!("连接服务器：{}", addr);

        let stream = match TcpStream::connect(addr.clone()).await {
            Ok(stream) => stream,
            Err(e) => {
                return Err(Box::new(e));
            }
        };
        info!("服务连接成功");
        let (rd, wr) = stream.into_split();
        let mut frame_reader = FramedRead::new(rd, RespCodec::new());
        let mut frame_writer = FramedWrite::new(wr, ReqCodec::new());
        let account = self.setting.counter.fundaccount.parse().unwrap();

        let logon = logon_api(&self.setting.counter).await;
        info!("发送登录消息：{:?}", logon);
        let _ = frame_writer.send(logon).await;

        let channel = self.setting.system.channels[0];

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    // result = frame_reader.next() => match result {
                    //     Some(Ok(msg)) => {
                    //         info!("收到消息：{:?}", msg);
                    //         //将消息装换成路由消息
                    //         if let Some(router_msg) = from_api_to_router(&msg).await {
                    //             let _ = tx_repay_to_router.send(router_msg).await;
                    //         }
                    //     },
                    //     Some(Err(e)) => {
                    //         error!("读取消息失败: {:?}", e);
                    //     },
                    //     None => {
                    //         error!("连接已关闭");
                    //         // break;
                    //     },
                    // },
                    result = rx_order_from_router.recv() => match result {
                        Ok(order) => {
                            info!("收到订单：{:?}", order);
                            //转换成api需要的数据格式
                            if let Some(api_msg) = from_router_to_api(&order, account).await {
                                info!("发送订单：{:?}", api_msg);
                                let _ = frame_writer.send(api_msg).await;
                            }
                        },
                        Err(e) => {
                            error!("接收订单失败: {:?}", e);
                        },
                    },
                }
            }
        });

        loop {
            tokio::select! {
                result = frame_reader.next() => match result {
                    Some(Ok(msg)) => {
                        info!("收到回执消息：{:?}", msg);
                        //将消息装换成路由消息
                        if let Some(router_msg) = from_api_to_router(&msg, channel).await {
                            let _ = tx_repay_to_router.send(router_msg).await;
                        }
                    },
                    Some(Err(e)) => {
                        error!("读取消息失败: {:?}", e);
                        return Err(Box::new(e));
                    },
                    None => {
                        error!("连接已关闭");
                        return Err("连接已关闭".into());
                    },
                },
            }
        }
    }
}
