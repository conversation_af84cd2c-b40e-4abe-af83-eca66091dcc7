use std::sync::Arc;

use anyhow::{Result, anyhow};
use async_channel::{Receiver, Sender};
use server_protoes::{MsgContent, MsgType, RegisterReq, RouterMsg, order_router_service_client::OrderRouterServiceClient};
use std::sync::atomic::AtomicBool;
use std::sync::atomic::Ordering;
use tokio::sync::RwLock;
use tonic::transport::Channel;
use tracing::*;
#[derive(Clone)]
pub struct OrderRouterClient {
    uri: String,
    channel_ids: Vec<i64>,
    client: Arc<RwLock<Option<OrderRouterServiceClient<Channel>>>>, //Option<OrderRouterServiceClient<Channel>>,
    tx_order_from_router: Sender<RouterMsg>,                        //接收消息
    rx_repay_to_router: Receiver<RouterMsg>,                        //回复消息
}

impl OrderRouterClient {
    pub async fn new(uri: &String, channel_ids: &Vec<i64>, tx_order_from_router: Sender<RouterMsg>, rx_repay_to_router: Receiver<RouterMsg>) -> Self {
        let orderclient = Arc::new(RwLock::new(None::<OrderRouterServiceClient<Channel>>));
        let client = OrderRouterServiceClient::connect(uri.clone()).await;
        if client.is_ok() {
            info!("订单路由连接成功....");
            let mut wr = orderclient.write().await;
            *wr = Some(client.unwrap());
        }

        let client = OrderRouterClient {
            uri: uri.to_owned(),
            channel_ids: channel_ids.clone(),
            client: orderclient,
            tx_order_from_router,
            rx_repay_to_router,
        };
        client.start_reconnect_listen().await; // Start the reconnection loop

        client
    }
    pub async fn start_reconnect_listen(&self) {
        let orderclient_clone = Arc::clone(&self.client);
        let url = self.uri.clone();
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = tokio::time::sleep(std::time::Duration::from_secs(3)) => {
                        if orderclient_clone.read().await.is_none() {
                            info!("订单路由重新连接中......");
                            let client = OrderRouterServiceClient::connect(url.clone()).await;
                            if client.is_err() {
                                error!("不能连接到订单路由,3秒后重连");
                                // std::thread::sleep(std::time::Duration::from_secs(3));
                                // continue;
                            }else{
                                info!("订单路由连接成功......");
                                let mut wr = orderclient_clone.write().await;
                                *wr = Some(client.unwrap());
                            }
                        }
                    }
                }
            }
        });
    }

    pub async fn order_transfer(&self) -> Result<()> {
        if self.client.read().await.is_none() {
            error!("订单路由未连接");
            return Err(anyhow!("订单路由未连接"));
        }
        let tx_order_from_router = self.tx_order_from_router.clone();
        let rx_repay_to_router = self.rx_repay_to_router.clone();

        //发送消息
        let outbound = async_stream::stream! {
            loop {
                if let Ok(val) = rx_repay_to_router.recv().await {
                    info!("推送回执到路由: {:?}", &val);
                    yield val;
                }
            }
        };
        let mut wr = self.client.write().await; // self.client.write().await.as_mut().unwrap();
        let sub_ret = wr.as_mut().unwrap().order_transfer(outbound).await;
        // let sub_ret = client.order_transfer(outbound).await;
        if sub_ret.as_ref().is_err() {
            // log_error(&format!("风控中心 phoenix_risk_check error: {:?}", sub_ret.as_ref().err().unwrap())).await;
            // self.client = None;
            *wr = None;
            error!("{:?}", sub_ret.as_ref().err());
            return Err(anyhow!("{:?}", sub_ret.as_ref().err()));
        }
        let response = sub_ret.expect("resp error");
        let mut inbound = response.into_inner();

        //接收消息
        while let Ok(inbound_data) = inbound.message().await {
            if inbound_data.is_some() {
                let value = inbound_data.expect("data error");
                if value.msg_type == (MsgType::Order as i32) {
                    info!("订单：{:#?}", value);
                    if let Err(err) = tx_order_from_router.send(value).await {
                        error!("订单发送失败: {:?}", err);
                    }
                }
            }
            // else {
            //     info!("inbound data empty");
            // }
        }
        Ok(())
    }

    pub async fn register_router(self, tx_ready_to_router: Sender<RouterMsg>) {
        let register = RouterMsg {
            msg_type: MsgType::Register as i32,
            msg_content: Some(MsgContent {
                register_req: Some(RegisterReq {
                    channel_id: self.channel_ids.clone(),
                }),
                order_msg: None,
                exec_msg: None,
                resp: None,
            }),
            msg_id: utils::current_timestamp(),
            msg_time: utils::current_timestamp(),
        };
        let _ = tx_ready_to_router.send(register).await;
    }
}

#[derive(Clone)]
pub struct OrderRouterClientV2 {
    uri: String,
    channel_ids: Vec<i64>,
    client: Option<OrderRouterServiceClient<Channel>>, // ption<OrderRouterServiceClient<Channel>>,
    tx_order_from_router: Sender<RouterMsg>,           // 接收消息
    rx_repay_to_router: Receiver<RouterMsg>,           // 回复消息(转换为消费端)
    register_sent: Arc<AtomicBool>,                    // 注册标记
}

impl OrderRouterClientV2 {
    pub async fn new(uri: &String, channel_ids: &Vec<i64>, tx_order_from_router: Sender<RouterMsg>, rx_repay_to_router: Receiver<RouterMsg>) -> Self {
        let mut order_router_client = OrderRouterClientV2 {
            uri: uri.to_owned(),
            channel_ids: channel_ids.clone(),
            client: None,
            tx_order_from_router,
            rx_repay_to_router: rx_repay_to_router.clone(),
            register_sent: Arc::new(AtomicBool::new(false)),
        };

        order_router_client.client = match OrderRouterServiceClient::connect(uri.to_owned()).await {
            Ok(client) => {
                info!("订单路由连接成功....");
                Some(client)
            }
            Err(e) => {
                error!("路由中心连接失败..: {}, {:?}", uri, e);
                None
            }
        };

        order_router_client
    }

    pub async fn init_client(&mut self) -> Result<OrderRouterServiceClient<Channel>> {
        if self.client.is_some() {
            return Ok(self.client.clone().unwrap());
        } else {
            info!("订单路由重新连接中......");
            match OrderRouterServiceClient::connect(self.uri.to_owned()).await {
                Ok(client) => {
                    info!("订单路由连接成功....");
                    self.client = Some(client);
                    return Ok(self.client.clone().unwrap());
                }
                Err(e) => {
                    error!("路由中心连接失败..:{} {:?}", self.uri, e);
                    return Err(anyhow!(format!("路由中心连接失败..: {} {:?}", self.uri, e)));
                }
            }
        }
    }

    pub async fn order_transfer(&mut self, tx_ready_to_router: Sender<RouterMsg>) -> Result<()> {
        let mut client = match self.init_client().await {
            Ok(client) => client,
            Err(err) => {
                error!("{:?}", err);
                return Err(anyhow!("{:?}", err));
            }
        };

        // 连接成功后，如果还没发注册消息，则发一次
        if !self.register_sent.load(Ordering::SeqCst) {
            let register_msg = self.register_router().await;
            if let Err(e) = tx_ready_to_router.send(register_msg).await {
                error!("发送注册消息失败: {:?}", e);
            } else {
                self.register_sent.store(true, Ordering::SeqCst);
            }
        }
        let tx_order_from_router = self.tx_order_from_router.clone();
        let rx_repay_to_router = self.rx_repay_to_router.clone();

        //发送消息
        let outbound = async_stream::stream! {
            loop {
                if let Ok(val) = rx_repay_to_router.recv().await {
                    info!("推送回执到路由: {:?}", &val);
                    yield val;
                }
            }
        };

        let sub_ret = client.order_transfer(outbound).await;
        if sub_ret.as_ref().is_err() {
            self.client = None;
            self.register_sent.store(false, Ordering::SeqCst); // 断开时重置
            error!("{:#?}", sub_ret.as_ref().err());
            return Err(anyhow!("{:?}", sub_ret.as_ref().err()));
        }
        let response = sub_ret.expect("resp error");
        let mut inbound = response.into_inner();

        //接收消息
        while let Ok(inbound_data) = inbound.message().await {
            if inbound_data.is_some() {
                let value = inbound_data.expect("data error");
                if value.msg_type == (MsgType::Order as i32) {
                    info!("订单：{:#?}", value);
                    if let Err(err) = tx_order_from_router.send(value).await {
                        error!("订单发送失败: {:?}", err);
                    }
                }
            } else {
                info!("inbound data empty");
            }
        }

        //连接断开跳出循环结束
        self.register_sent.store(false, Ordering::SeqCst); // 断开时重置
        Ok(())
    }

    pub async fn register_router(&self) -> RouterMsg {
        let register = RouterMsg {
            msg_type: MsgType::Register as i32,
            msg_content: Some(MsgContent {
                register_req: Some(RegisterReq {
                    channel_id: self.channel_ids.clone(),
                }),
                order_msg: None,
                exec_msg: None,
                resp: None,
            }),
            msg_id: utils::current_timestamp(),
            msg_time: utils::current_timestamp(),
        };
        info!("注册路由中心: {:?}", register);
        register
    }
}
