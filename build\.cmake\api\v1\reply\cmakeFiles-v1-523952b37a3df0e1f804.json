{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/build/CMakeFiles/3.27.3/CMakeSystem.cmake"}, {"isExternal": true, "path": "C:/Users/<USER>/rainerix/vcpkg/scripts/buildsystems/vcpkg.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeDependentOption.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/build/CMakeFiles/3.27.3/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/build/CMakeFiles/3.27.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-GNU.cmake"}, {"isGenerated": true, "path": "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/build/CMakeFiles/3.27.3/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-GNU-C-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/Platform/Windows-GNU-CXX-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27/Modules/CMakeCommonLanguageInclude.cmake"}, {"path": "quickfix-bind/CMakeLists.txt"}, {"path": "examples/CMakeLists.txt"}], "kind": "cmakeFiles", "paths": {"build": "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/build", "source": "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/fix/quickfix-ffi"}, "version": {"major": 1, "minor": 0}}