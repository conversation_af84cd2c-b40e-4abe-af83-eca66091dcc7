[package]
name = "quickfix-msg-gen"
version = "0.1.6"
authors = ["Arthur LE MOIGNE"]
edition = "2021"
description = "FIX code generator from XML spec file"
repository = "https://github.com/arthurlm/quickfix-rs"
license = "MIT OR Apache-1.1"
keywords = ["quickfix", "fix-protocol", "finance", "code-generator"]
categories = ["development-tools::build-utils"]
rust-version = "1.70.0"

[dependencies]
quickfix-spec-parser = { path = "../quickfix-spec-parser", version = "0.1.0" }
quickfix = { path = "../quickfix", version = "0.1.0" }
convert_case = "0.7.1"
itertools = "0.14.0"
bytes = "1.9.0"
