[package]
name = "server-protoes"
version = "0.1.0"
edition = "2024"

[dependencies]
serde = { workspace = true }
# serde_json = { workspace = true }
prost = { workspace = true }
tonic = { workspace = true }

tokio = { workspace = true, features = ["full"] }
tokio-util = { workspace = true }
tracing = { workspace = true }
bincode = { workspace = true }
bytes = { workspace = true }
futures = { workspace = true }
futures-util = { workspace = true }
chrono = { workspace = true }
byteorder = { workspace = true }

[build-dependencies]
tonic-build.workspace = true
