# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe

# The command to remove a file.
RM = C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\fix\quickfix-ffi

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: quickfix-bind/all
all: examples/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: quickfix-bind/preinstall
preinstall: examples/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: quickfix-bind/clean
clean: examples/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory examples

# Recursive "all" directory target.
examples/all: examples/CMakeFiles/demo_basic_binding.dir/all
.PHONY : examples/all

# Recursive "preinstall" directory target.
examples/preinstall:
.PHONY : examples/preinstall

# Recursive "clean" directory target.
examples/clean: examples/CMakeFiles/demo_basic_binding.dir/clean
.PHONY : examples/clean

#=============================================================================
# Directory level rules for directory quickfix-bind

# Recursive "all" directory target.
quickfix-bind/all: quickfix-bind/CMakeFiles/quickfixbind.dir/all
.PHONY : quickfix-bind/all

# Recursive "preinstall" directory target.
quickfix-bind/preinstall:
.PHONY : quickfix-bind/preinstall

# Recursive "clean" directory target.
quickfix-bind/clean: quickfix-bind/CMakeFiles/quickfixbind.dir/clean
.PHONY : quickfix-bind/clean

#=============================================================================
# Target rules for target quickfix-bind/CMakeFiles/quickfixbind.dir

# All Build rule for target.
quickfix-bind/CMakeFiles/quickfixbind.dir/all:
	$(MAKE) $(MAKESILENT) -f quickfix-bind\CMakeFiles\quickfixbind.dir\build.make quickfix-bind/CMakeFiles/quickfixbind.dir/depend
	$(MAKE) $(MAKESILENT) -f quickfix-bind\CMakeFiles\quickfixbind.dir\build.make quickfix-bind/CMakeFiles/quickfixbind.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\CMakeFiles --progress-num=3,4 "Built target quickfixbind"
.PHONY : quickfix-bind/CMakeFiles/quickfixbind.dir/all

# Build rule for subdir invocation for target.
quickfix-bind/CMakeFiles/quickfixbind.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 quickfix-bind/CMakeFiles/quickfixbind.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\CMakeFiles 0
.PHONY : quickfix-bind/CMakeFiles/quickfixbind.dir/rule

# Convenience name for target.
quickfixbind: quickfix-bind/CMakeFiles/quickfixbind.dir/rule
.PHONY : quickfixbind

# clean rule for target.
quickfix-bind/CMakeFiles/quickfixbind.dir/clean:
	$(MAKE) $(MAKESILENT) -f quickfix-bind\CMakeFiles\quickfixbind.dir\build.make quickfix-bind/CMakeFiles/quickfixbind.dir/clean
.PHONY : quickfix-bind/CMakeFiles/quickfixbind.dir/clean

#=============================================================================
# Target rules for target examples/CMakeFiles/demo_basic_binding.dir

# All Build rule for target.
examples/CMakeFiles/demo_basic_binding.dir/all: quickfix-bind/CMakeFiles/quickfixbind.dir/all
	$(MAKE) $(MAKESILENT) -f examples\CMakeFiles\demo_basic_binding.dir\build.make examples/CMakeFiles/demo_basic_binding.dir/depend
	$(MAKE) $(MAKESILENT) -f examples\CMakeFiles\demo_basic_binding.dir\build.make examples/CMakeFiles/demo_basic_binding.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\CMakeFiles --progress-num=1,2 "Built target demo_basic_binding"
.PHONY : examples/CMakeFiles/demo_basic_binding.dir/all

# Build rule for subdir invocation for target.
examples/CMakeFiles/demo_basic_binding.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 examples/CMakeFiles/demo_basic_binding.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\CMakeFiles 0
.PHONY : examples/CMakeFiles/demo_basic_binding.dir/rule

# Convenience name for target.
demo_basic_binding: examples/CMakeFiles/demo_basic_binding.dir/rule
.PHONY : demo_basic_binding

# clean rule for target.
examples/CMakeFiles/demo_basic_binding.dir/clean:
	$(MAKE) $(MAKESILENT) -f examples\CMakeFiles\demo_basic_binding.dir\build.make examples/CMakeFiles/demo_basic_binding.dir/clean
.PHONY : examples/CMakeFiles/demo_basic_binding.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

