[package]
name = "bin"
version = "0.1.0"
edition = "2024"

# [[bin]]
# name = "tradeapi"
# path = "src/tradeapi.rs"

# [[bin]]
# name = "phoenixbaopan"
# path = "src/baopan.rs"

[[bin]]
name = "hybaopan"
path = "src/hybaopan.rs"

[[bin]]
name = "client"
path = "src/client.rs"

[[bin]]
name = "executor"
path = "src/executor.rs"

[[bin]]
name = "fixorder"
path = "src/fixorder.rs"

[[bin]]
name = "fixclient"
path = "src/fixtest.rs"


[dependencies]
server-initialize = { workspace = true }
server-protoes = { workspace = true }
server-global = { workspace = true }
server-constant = { workspace = true }
mqclient = { workspace = true }
logclient = { workspace = true }
utils = { workspace = true }

# server-api = { workspace = true }
server-hybaopan = { workspace = true }
# server-baopan = { workspace = true }
server-fixbaopan = { workspace = true }

quickfix = { workspace = true }
quickfix-ffi = { workspace = true }
quickfix-msg40 = { workspace = true }
quickfix-msg41 = { workspace = true }
quickfix-msg42 = { workspace = true }
quickfix-msg44 = { workspace = true }
quickfix-msg50 = { workspace = true }

anyhow = { workspace = true }
thiserror = { workspace = true }
chrono = { workspace = true }
parking_lot = { workspace = true }
uuid = { workspace = true }


axum = { workspace = true }

tokio = { workspace = true }
tokio-util = { workspace = true }
tracing = { workspace = true }
bincode = { workspace = true }
bytes = { workspace = true }
futures = { workspace = true }
futures-util = { workspace = true }
prost = { workspace = true }
serde = { workspace = true }

ulid = { workspace = true }
