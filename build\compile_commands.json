[{"directory": "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/build/quickfix-bind", "command": "C:\\msys64\\mingw64\\bin\\g++.exe  @CMakeFiles/quickfixbind.dir/includes_CXX.rsp -g -std=gnu++17 -o CMakeFiles\\quickfixbind.dir\\src\\quickfix_bind.cpp.obj -c C:\\Users\\<USER>\\rainerix\\lanlian\\phoenix_tradeapi\\fix\\quickfix-ffi\\quickfix-bind\\src\\quickfix_bind.cpp", "file": "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/fix/quickfix-ffi/quickfix-bind/src/quickfix_bind.cpp", "output": "quickfix-bind/CMakeFiles/quickfixbind.dir/src/quickfix_bind.cpp.obj"}, {"directory": "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/build/examples", "command": "C:\\msys64\\mingw64\\bin\\gcc.exe  @CMakeFiles/demo_basic_binding.dir/includes_C.rsp -g -o CMakeFiles\\demo_basic_binding.dir\\demo_basic_binding.c.obj -c C:\\Users\\<USER>\\rainerix\\lanlian\\phoenix_tradeapi\\fix\\quickfix-ffi\\examples\\demo_basic_binding.c", "file": "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/fix/quickfix-ffi/examples/demo_basic_binding.c", "output": "examples/CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.obj"}]