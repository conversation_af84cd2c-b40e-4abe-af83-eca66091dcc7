{"archive": {}, "artifacts": [{"path": "quickfix-bind/libquickfixbind.a"}], "backtrace": 2, "backtraceGraph": {"commands": ["_add_library", "add_library", "install", "include_directories"], "files": ["C:/Users/<USER>/rainerix/vcpkg/scripts/buildsystems/vcpkg.cmake", "quickfix-bind/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 4, "parent": 0}, {"command": 0, "file": 0, "line": 639, "parent": 1}, {"command": 2, "file": 1, "line": 26, "parent": 0}, {"command": 3, "file": 1, "line": 1, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17"}], "includes": [{"backtrace": 4, "path": "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/fix/quickfix-ffi/quickfix-bind/include"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "17"}, "sourceIndexes": [0]}], "id": "quickfixbind::@4cf7d16ebaee8de20a4a", "install": {"destinations": [{"backtrace": 3, "path": "lib"}], "prefix": {"path": "C:/Program Files (x86)/QuickFixBind"}}, "name": "quickfixbind", "nameOnDisk": "libquickfixbind.a", "paths": {"build": "quickfix-bind", "source": "quickfix-bind"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "quickfix-bind/src/quickfix_bind.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}