# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe

# The command to remove a file.
RM = C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\fix\quickfix-ffi

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake-gui.exe -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build && $(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\CMakeFiles C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\examples\\CMakeFiles\progress.marks
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 examples/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 examples/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 examples/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 examples/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
examples/CMakeFiles/demo_basic_binding.dir/rule:
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 examples/CMakeFiles/demo_basic_binding.dir/rule
.PHONY : examples/CMakeFiles/demo_basic_binding.dir/rule

# Convenience name for target.
demo_basic_binding: examples/CMakeFiles/demo_basic_binding.dir/rule
.PHONY : demo_basic_binding

# fast build rule for target.
demo_basic_binding/fast:
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build && $(MAKE) $(MAKESILENT) -f examples\CMakeFiles\demo_basic_binding.dir\build.make examples/CMakeFiles/demo_basic_binding.dir/build
.PHONY : demo_basic_binding/fast

demo_basic_binding.obj: demo_basic_binding.c.obj
.PHONY : demo_basic_binding.obj

# target to build an object file
demo_basic_binding.c.obj:
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build && $(MAKE) $(MAKESILENT) -f examples\CMakeFiles\demo_basic_binding.dir\build.make examples/CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.obj
.PHONY : demo_basic_binding.c.obj

demo_basic_binding.i: demo_basic_binding.c.i
.PHONY : demo_basic_binding.i

# target to preprocess a source file
demo_basic_binding.c.i:
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build && $(MAKE) $(MAKESILENT) -f examples\CMakeFiles\demo_basic_binding.dir\build.make examples/CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.i
.PHONY : demo_basic_binding.c.i

demo_basic_binding.s: demo_basic_binding.c.s
.PHONY : demo_basic_binding.s

# target to generate assembly for a file
demo_basic_binding.c.s:
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build && $(MAKE) $(MAKESILENT) -f examples\CMakeFiles\demo_basic_binding.dir\build.make examples/CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.s
.PHONY : demo_basic_binding.c.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... install
	@echo ... install/local
	@echo ... install/strip
	@echo ... list_install_components
	@echo ... rebuild_cache
	@echo ... demo_basic_binding
	@echo ... demo_basic_binding.obj
	@echo ... demo_basic_binding.i
	@echo ... demo_basic_binding.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

