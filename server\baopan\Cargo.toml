[package]
name = "phoenix_baopan"
version = "0.1.0"
edition = "2024"

[dependencies]
server-initialize = { workspace = true }
server-protoes = { workspace = true }
orderrouterclient = { workspace = true }
utils = { workspace = true }


serde = { workspace = true }

tokio = { workspace = true }
tonic = { workspace = true }
tokio-util = { workspace = true }
futures = { workspace = true }
tracing = { workspace = true }
config = { workspace = true }
anyhow = { workspace = true }
async-channel = { workspace = true }
async-stream = { workspace = true }
