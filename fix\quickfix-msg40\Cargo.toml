[package]
name = "quickfix-msg40"
version = "0.1.6"
authors = ["Arthur LE MOIGNE"]
edition = "2021"
description = "FIX 4.0 messages generated from official XML spec file"
repository = "https://github.com/arthurlm/quickfix-rs"
license = "MIT OR Apache-1.1"
keywords = ["quickfix", "fix-protocol", "finance", "auto-generated"]
categories = ["encoding"]
rust-version = "1.70.0"

[dependencies]
quickfix = { path = "../quickfix", version = "0.1.0" }

[build-dependencies]
quickfix-msg-gen = { path = "../quickfix-msg-gen", version = "0.1.0" }
