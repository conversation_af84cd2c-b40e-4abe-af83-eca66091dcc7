{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2], "hasInstallRule": true, "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": "."}, {"build": "quickfix-bind", "hasInstallRule": true, "jsonFile": "directory-quickfix-bind-Debug-f021d8ca9cd639e725a0.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 0, "projectIndex": 0, "source": "quickfix-bind", "targetIndexes": [1]}, {"build": "examples", "jsonFile": "directory-examples-Debug-2e8964349ffea94e1580.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 0, "projectIndex": 0, "source": "examples", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2], "name": "QuickFixBind", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 2, "id": "demo_basic_binding::@714eac15f71a94114fdf", "jsonFile": "target-demo_basic_binding-Debug-c40b22ba2f6ee8b2afa9.json", "name": "demo_basic_binding", "projectIndex": 0}, {"directoryIndex": 1, "id": "quickfixbind::@4cf7d16ebaee8de20a4a", "jsonFile": "target-quickfixbind-Debug-7440a41c4f669d3e7222.json", "name": "quickfixbind", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/build", "source": "C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/fix/quickfix-ffi"}, "version": {"major": 2, "minor": 6}}