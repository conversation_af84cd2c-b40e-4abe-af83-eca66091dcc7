[workspace]
resolver = "2"
members = ["fix/*", "server/*"]

[profile.release]
opt-level = 3     # 最高级别的优化，以获得最佳性能
lto = "fat"       # 启用全程序链接时优化，可能会显著提高性能，但会增加编译时间
codegen-units = 1 # 使用单一代码生成单元，允许更多的跨模块优化
panic = 'abort'   # 在 panic 时直接终止程序，减小二进制大小并略微提高性能
strip = true      # 去除符号信息，进一步减小二进制大小
debug = false     # 不包含调试信息，减小二进制大小

[profile.dev]
split-debuginfo = "unpacked"

[workspace.package]
authors = ["Hongze"]
version = "0.1.0"
edition = "2024"

[workspace.dependencies]
utils = { path = "./server/utils" }
reqclient = { path = "./server/reqclient" }
redisclient = { path = "./server/redisclient" }
orderrouterclient = { path = "./server/orderrouterclient" }
mqclient = { path = "./server/mqclient" }
logclient = { path = "./server/logclient" }
server-initialize = { path = "./server/initialize" }
server-global = { path = "./server/global" }
server-protoes = { path = "./server/protoes" }
server-events = { path = "./server/events" }
server-constant = { path = "./server/constant" }

# server-api = { path = "./server/api" }
# server-baopan = { path = "./server/baopan" }
server-hybaopan = { path = "./server/baopanhy" }
server-fixbaopan = { path = "./server/baopanfix" }

quickfix = { version = "0.1.6" }
quickfix-ffi = { version = "0.1.6" }
quickfix-msg40 = { version = "0.1.6" }
quickfix-msg41 = { version = "0.1.6" }
quickfix-msg42 = { version = "0.1.6" }
quickfix-msg43 = { version = "0.1.6" }
quickfix-msg44 = { version = "0.1.6" }
quickfix-msg50 = { version = "0.1.6" }

# quickfix-ffi = { path = "./fix/quickfix-ffi" }
# quickfix = { path = "./fix/quickfix" }
# quickfix-msg40 = { path = "./fix/quickfix-msg40" }
# quickfix-msg41 = { path = "./fix/quickfix-msg41" }
# quickfix-msg42 = { path = "./fix/quickfix-msg42" }
# quickfix-msg43 = { path = "./fix/quickfix-msg43" }
# quickfix-msg44 = { path = "./fix/quickfix-msg44" }
# quickfix-msg50 = { path = "./fix/quickfix-msg50" }


parking_lot = "0.12.4"
uuid = { version = "1.17.0", features = ["v4"] }

# =========================================
# 基础异步运行时和工具库（底层依赖）
# =========================================
tokio = { version = "1.45.1", features = ["full"] }    # 异步运行时库
tokio-util = { version = "0.7", features = ["codec"] }
async-trait = { version = "0.1.87" }                   # 异步 trait 支持
async-channel = "2.3.1"                                # 异步通道
async-stream = "0.3.6"                                 # 异步流
futures = "0.3"                                        # futures 库，用于异步编程
futures-util = { version = "0.3.31" }
futures-lite = "2.6"
# async-std = { version = "1.13", default-features = false }  # 另一个异步库
bytes = "1.10.1" # 字节处理库
# bincode = { version = "1.3.3" }
# bincode = { version = "2.0.1", default-features= false, features = ["serde"] }
bincode = { version = "2.0.1" }

axum = { version = "0.8.4", features = ["tokio", "original-uri"] }
axum-extra = { version = "0.10.1", features = ["typed-header"] }
tower = "0.5.2"                                                    # 用于构建和管理异步服务的库

once_cell = { version = "1.21.3" }
lazy_static = "1.5.0"
# lazy_static = { version = "1.5.0" }
# =========================================
# 数据库 ORM 和适配器（底层数据库操作）
# =========================================
# sea-orm = { version = "1.1.5" } # SeaORM，Rust 的 ORM 框架
redis = { version = "0.32.0", features = ["r2d2", "cluster"] }
r2d2 = { version = "0.8" }

#reqwest
# reqwest = { version = "0.12.15" }
reqwest = { version = "0.12.20", default-features = false, features = [
    "json",
    "rustls-tls",
    "stream",
] }
# =========================================
# 序列化和反序列化库（中层）
# =========================================
serde = { version = "1.0", features = ["derive"] } # 序列化和反序列化基础库
# serde_yaml = "0.9" # YAML 序列化和反序列化
serde_json = "1.0.140" # JSON 序列化和反序列化

# =========================================
# 实用工具库（辅助工具）
# =========================================
tracing-appender = "0.2.3"
tracing-log = { version = "0.2.0" }
tracing = "0.1.41"
env_logger = "0.11.8"
tracing-subscriber = { version = "0.3", features = [
    "env-filter",
    "fmt",
    "time",
] } # tracing 库的订阅者实现
tracing-error = "0.2" # 用于错误跟踪
file-rotate = "0.8.0"


thiserror = "2.0.12" # 用于简化错误处理的库
chrono = "0.4"       # 时间和日期处理库

ulid = "1.2.1" # 用于生成 ULID 的库

# 线程安全的锁
moka = { version = "0.12", features = ["future"] } # 基于 LRU 的缓存库，支持同步

config = "0.15.8" #配置文件处理库

strum = { version = "0.27.1", features = [
    "derive",
] } # 枚举工具库# =========================================
# 枚举和类型扩展（上层工具库）
# =========================================
strum_macros = "0.27.1" # strum 宏库，用于派生 trait

# =========================================
# other
# =========================================
anyhow = "1.0.95" # 错误处理库，提供 Result 类型和错误转换
# askama = "0.12"        # 类型安全的编译时模板引擎
# askama_derive = "0.12" # askama 的派生宏支持
# convert_case = "0.7.1" # 字符串命名风格转换工具

#tonic protos
tonic-build = { version = "0.13.1", features = ["prost"] }
tonic = { version = "0.13.1" }
prost = "0.13.5"
prost-types = "0.13.5"
#sea-orm = { version = "1.1.5" }
# mq
deadpool-lapin = "0.12.1"
lapin = "2.5.1"

# 加密和解密
ring = "0.17.14"           # 加密库
hex = "0.4"                # 二进制转换库
md-5 = "0.10"              # MD5 加密库
aes = "0.8"
hex-literal = "1.0.0"
cbc = "0.1"
base64 = "0.22"
aead = { version = "0.5" }
aes-gcm = "0.10"
rand = "0.8.5"
# rsa库
rsa = { version = "0.9.8", features = ["sha2"] }

md5 = "0.7"
byteorder = "1.5.0"
# 数据库
sea-orm = { version = "1.1.12", features = [
    "sqlx-mysql",
    "runtime-tokio",
    "macros",
] }
