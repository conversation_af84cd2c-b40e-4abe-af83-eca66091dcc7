# quickfix with rust

## quick fix build requiredments

Following package must be install to build the library:

cmake
a C++ compiler (with C++17 support)
rustup / rustc / cargo (obviously 😉)
rustfmt for auto generated messages from spec.

## how to run example

cargo run --bin executor // server
cargo run --bin fixorder // client

## how to run under windows

Setting up MinGW-w64 on Windows for C++ development (including C++17 support with GCC) is straightforward, especially using MSYS2 for a robust and updated environment. Below is a step-by-step guide to install and configure MinGW-w64, ensuring it works for compiling C++ code with GCC.
Step 1: Install MSYS2
MSYS2 provides a convenient way to install and manage MinGW-w64, along with a Linux-like terminal environment.
Download MSYS2:
Go to the MSYS2 official website.

Download the installer for your system (e.g., msys2-x86_64-<latest>.exe for 64-bit Windows).

Run the Installer:
Execute the installer and follow the prompts.

Default installation path: C:\msys64 (recommended to avoid spaces in the path).

Complete the installation.

Update MSYS2:
Open the MSYS2 terminal (e.g., C:\msys64\msys2_shell.cmd or the "MSYS2 MSYS" shortcut).

Update the package database and core packages:
bash

pacman -Syu

If prompted to close the terminal, do so, then reopen it and run:
bash

pacman -Su

Step 2: Install MinGW-w64 and GCC
MinGW-w64 provides the GCC compiler and tools for 64-bit (and 32-bit) Windows development.
Install the MinGW-w64 Toolchain:
In the MSYS2 terminal, install the 64-bit GCC toolchain:
bash

pacman -S mingw-w64-x86_64-gcc

This installs gcc, g++ (for C++), and related tools.

Optionally, install additional tools like make and gdb:
bash

pacman -S mingw-w64-x86_64-make mingw-w64-x86_64-gdb

Verify GCC Installation:
Switch to the MinGW64 environment (if not already in it):
Open the "MSYS2 MinGW 64-bit" terminal (via C:\msys64\msys2_shell.cmd -mingw64 or the shortcut).

Check the GCC version:
bash

g++ --version

Expected output (as of May 2025, version may vary):

g++ (MinGW-W64) 13.2.0

Confirm C++17 support (GCC 7+ supports C++17):
bash

g++ -std=c++17 --version

Step 3: Configure Environment Variables
To use MinGW-w64’s g++ from any command prompt (e.g., cmd, PowerShell, or IDEs like VS Code), add it to the system PATH.
Add MinGW-w64 to PATH:
Right-click "This PC" → Properties → Advanced system settings → Environment Variables.

Under "System variables" (or "User variables" for single-user access), find or create Path.

Add the MinGW-w64 bin directory: C:\msys64\mingw64\bin.

Click OK to save.

Verify PATH Configuration:
Open a new Command Prompt (cmd) or PowerShell.

Run:
bash

g++ --version

If the command is recognized, the PATH is set correctly. If not, double-check the PATH entry.

Step 4: Test Compilation
Test MinGW-w64 by compiling a C++17 program.
Create a Test File:
Create a file named hello.cpp:
cpp

# include <iostream>

# include <optional> // C++17 feature

int main() {
std::optional<int> opt = 42;
if (opt.has_value()) {
std::cout << "Value: " << opt.value() << std::endl;
}
return 0;
}

Compile and Run:
In the MSYS2 MinGW64 terminal or cmd (if PATH is set):
bash

g++ -std=c++17 hello.cpp -o hello.exe
./hello.exe

Expected output:

Value: 42

Step 5: Integrate with an IDE (Optional)
To enhance development, integrate MinGW-w64 with an IDE like VS Code or Code::Blocks.
VS Code Setup:
Install VS Code and the C/C++ extension by Microsoft.

Configure the compiler path:
Open VS Code, go to File → Preferences → Settings.

Search for C_Cpp.default.compilerPath and set it to C:\msys64\mingw64\bin\g++.exe.

Create a tasks.json for compilation:
Press Ctrl+Shift+B to build, and select "Configure Task."

Use this template for .vscode/tasks.json:
json

{
"version": "2.0.0",
"tasks": [
{
"label": "build",
"type": "shell",
"command": "g++",
"args": ["-std=c++17", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"],
"group": {
"kind": "build",
"isDefault": true
}
}
]
}

Debug with launch.json:
Set up debugging by creating .vscode/launch.json:
json

{
"version": "0.2.0",
"configurations": [
{
"name": "Debug",
"type": "cppdbg",
"request": "launch",
"program": "${fileDirname}\\${fileBasenameNoExtension}.exe",
"args": [],
"stopAtEntry": false,
"cwd": "${workspaceFolder}",
"environment": [],
"externalConsole": false,
"MIMode": "gdb",
"miDebuggerPath": "C:\\msys64\\mingw64\\bin\\gdb.exe",
"setupCommands": [
{
"description": "Enable pretty-printing for gdb",
"text": "-enable-pretty-printing",
"ignoreFailures": true
}
],
"preLaunchTask": "build"
}
]
}
