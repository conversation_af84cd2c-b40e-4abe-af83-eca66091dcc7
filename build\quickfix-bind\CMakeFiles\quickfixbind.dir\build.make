# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe

# The command to remove a file.
RM = C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\fix\quickfix-ffi

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build

# Include any dependencies generated for this target.
include quickfix-bind/CMakeFiles/quickfixbind.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include quickfix-bind/CMakeFiles/quickfixbind.dir/compiler_depend.make

# Include the progress variables for this target.
include quickfix-bind/CMakeFiles/quickfixbind.dir/progress.make

# Include the compile flags for this target's objects.
include quickfix-bind/CMakeFiles/quickfixbind.dir/flags.make

quickfix-bind/CMakeFiles/quickfixbind.dir/src/quickfix_bind.cpp.obj: quickfix-bind/CMakeFiles/quickfixbind.dir/flags.make
quickfix-bind/CMakeFiles/quickfixbind.dir/src/quickfix_bind.cpp.obj: quickfix-bind/CMakeFiles/quickfixbind.dir/includes_CXX.rsp
quickfix-bind/CMakeFiles/quickfixbind.dir/src/quickfix_bind.cpp.obj: C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/fix/quickfix-ffi/quickfix-bind/src/quickfix_bind.cpp
quickfix-bind/CMakeFiles/quickfixbind.dir/src/quickfix_bind.cpp.obj: quickfix-bind/CMakeFiles/quickfixbind.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object quickfix-bind/CMakeFiles/quickfixbind.dir/src/quickfix_bind.cpp.obj"
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\quickfix-bind && C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT quickfix-bind/CMakeFiles/quickfixbind.dir/src/quickfix_bind.cpp.obj -MF CMakeFiles\quickfixbind.dir\src\quickfix_bind.cpp.obj.d -o CMakeFiles\quickfixbind.dir\src\quickfix_bind.cpp.obj -c C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\fix\quickfix-ffi\quickfix-bind\src\quickfix_bind.cpp

quickfix-bind/CMakeFiles/quickfixbind.dir/src/quickfix_bind.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/quickfixbind.dir/src/quickfix_bind.cpp.i"
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\quickfix-bind && C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\fix\quickfix-ffi\quickfix-bind\src\quickfix_bind.cpp > CMakeFiles\quickfixbind.dir\src\quickfix_bind.cpp.i

quickfix-bind/CMakeFiles/quickfixbind.dir/src/quickfix_bind.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/quickfixbind.dir/src/quickfix_bind.cpp.s"
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\quickfix-bind && C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\fix\quickfix-ffi\quickfix-bind\src\quickfix_bind.cpp -o CMakeFiles\quickfixbind.dir\src\quickfix_bind.cpp.s

# Object files for target quickfixbind
quickfixbind_OBJECTS = \
"CMakeFiles/quickfixbind.dir/src/quickfix_bind.cpp.obj"

# External object files for target quickfixbind
quickfixbind_EXTERNAL_OBJECTS =

quickfix-bind/libquickfixbind.a: quickfix-bind/CMakeFiles/quickfixbind.dir/src/quickfix_bind.cpp.obj
quickfix-bind/libquickfixbind.a: quickfix-bind/CMakeFiles/quickfixbind.dir/build.make
quickfix-bind/libquickfixbind.a: quickfix-bind/CMakeFiles/quickfixbind.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libquickfixbind.a"
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\quickfix-bind && $(CMAKE_COMMAND) -P CMakeFiles\quickfixbind.dir\cmake_clean_target.cmake
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\quickfix-bind && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\quickfixbind.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
quickfix-bind/CMakeFiles/quickfixbind.dir/build: quickfix-bind/libquickfixbind.a
.PHONY : quickfix-bind/CMakeFiles/quickfixbind.dir/build

quickfix-bind/CMakeFiles/quickfixbind.dir/clean:
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\quickfix-bind && $(CMAKE_COMMAND) -P CMakeFiles\quickfixbind.dir\cmake_clean.cmake
.PHONY : quickfix-bind/CMakeFiles/quickfixbind.dir/clean

quickfix-bind/CMakeFiles/quickfixbind.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\fix\quickfix-ffi C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\fix\quickfix-ffi\quickfix-bind C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\quickfix-bind C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\quickfix-bind\CMakeFiles\quickfixbind.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : quickfix-bind/CMakeFiles/quickfixbind.dir/depend

