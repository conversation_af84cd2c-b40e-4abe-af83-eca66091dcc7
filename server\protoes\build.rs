fn build_grpc() {
    tonic_build::configure()
        .protoc_arg("--experimental_allow_proto3_optional")
        .out_dir("src/protoes")
        .type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize,bincode::Encode, bincode::Decode)]")
        .compile_protos(
            &[
                "protofiles/ordercenter.proto",
                "protofiles/tradeapi.proto",
                "protofiles/notification.proto",
                "protofiles/orderrouter.proto",
                "protofiles/ordermsg.proto",
                "protofiles/logcenter.proto",
            ],
            &["protofiles"],
        )
        .unwrap();
}

fn main() {
    build_grpc();
}
