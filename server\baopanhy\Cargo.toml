[package]
name = "server-hybaopan"
authors.workspace = true
version.workspace = true
edition.workspace = true

[dependencies]
orderrouterclient = { workspace = true }
server-protoes = { workspace = true }
reqclient = { workspace = true }
utils = { workspace = true }

config = { workspace = true }     #"0.15.4"
tokio = { workspace = true }      #"1"
axum = { workspace = true }       #{ version = "0.8.1", features = ["multipart", "tokio", "original-uri"] }
axum-extra = { workspace = true } #{ version = "0.10.0", features = ["typed-header"] }
tower = { workspace = true }      #"1"
serde = { workspace = true }      #{ version = "1", features = ["derive"] }
serde_json = { workspace = true } #"1"

anyhow = { workspace = true }
strum = { workspace = true }
strum_macros = { workspace = true }
tracing = { workspace = true }
async-channel = { workspace = true }
once_cell = { workspace = true }
sea-orm = { workspace = true }
lazy_static = { workspace = true }
