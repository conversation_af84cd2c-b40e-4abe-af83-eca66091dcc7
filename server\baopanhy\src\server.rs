use std::sync::Arc;

use crate::{
    Settings,
    dbsetup::DbConnection,
    entities::prelude::SysTradeChannel,
    initialize_event_channel,
    service::{HyHandler, OrderEvent, exec_from_api_to_router, init_account, init_channel_info, order_from_router_to_api, rejected},
};
use async_channel::unbounded;
use orderrouterclient::OrderRouterClientV2;
use server_protoes::RouterMsg;
use tokio::sync::RwLock;
use tracing::*;

#[derive(Clone, Debug)]
pub struct HyService {
    setting: Settings,
    token: Arc<RwLock<String>>,
    expire: Arc<RwLock<i64>>,
}

impl HyService {
    pub async fn init(setting: Settings) -> Result<Self, Box<dyn std::error::Error>> {
        let db = DbConnection::new(&setting.dbconfig.financedb).await;

        let channel = SysTradeChannel::query_many(&setting.application.channels, &db).await;
        info!("channel info: {:?}", &channel);
        init_channel_info(channel).await;
        init_account(&setting.hyserver.account).await;

        let ret = HyHandler::login(&setting.hyserver.loginname, &setting.hyserver.password, &setting.hyserver.server).await;
        if ret.as_ref().is_err() {
            error!("login error:{}", ret.as_ref().unwrap_err().to_string());
            return Err(ret.unwrap_err().into());
        }
        let loginresp = ret.unwrap();
        info!("login response:{:?}", &loginresp);

        // let loginresp: (String, i64) = ("".to_string(), 0);
        let token = Arc::new(RwLock::new(loginresp.0));
        let expire = Arc::new(RwLock::new(loginresp.1));
        Ok(Self { setting, token, expire })
    }

    pub async fn run(&self) -> Result<(), Box<dyn std::error::Error>> {
        // 创建订单事件通道
        let (tx_order, mut rx_order) = tokio::sync::mpsc::unbounded_channel::<OrderEvent>();
        initialize_event_channel(Arc::new(tx_order)).await;

        // //路由中心 ------>
        // let (tx_order_from_router, rx_order_from_router) = unbounded::<RouterMsg>();
        // // ------> 路由中心
        // let (tx_repay_to_router, rx_repay_to_router) = unbounded::<RouterMsg>();

        // let mut orderrouterclient = OrderRouterClient::new(
        //     &self.setting.servers.routerserver.to_string(),
        //     &self.setting.application.channels,
        //     tx_order_from_router.clone(),
        //     rx_repay_to_router,
        // )
        // .await;
        // let orderrouterclient_clone = orderrouterclient.clone();
        // tokio::spawn(async move { orderrouterclient.order_transfer().await });
        // let tx = tx_repay_to_router.clone();
        // orderrouterclient_clone.register_router(tx).await;
        //路由中心 ------>
        let (tx_order_from_router, rx_order_from_router) = unbounded::<RouterMsg>(); //消息保持在channel中知道被消费
        // ------> 路由中心
        let (tx_repay_to_router, rx_repay_to_router) = unbounded::<RouterMsg>();

        let mut orderrouterclient = OrderRouterClientV2::new(
            &self.setting.servers.routerserver.to_string(),
            &self.setting.application.channels,
            tx_order_from_router.clone(),
            rx_repay_to_router.clone(),
        )
        .await;
        let mut retry_interval = tokio::time::interval(std::time::Duration::from_secs(3));
        let tx = tx_repay_to_router.clone();

        tokio::spawn(async move {
            retry_interval.tick().await;
            loop {
                tokio::select! {
                    _ = retry_interval.tick() => {
                        if let Err(err) = orderrouterclient.order_transfer(tx.clone()).await {
                            error!("{:?}", err);
                        }
                    }
                }
            }
        });

        let self_clone = self.clone();
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = tokio::time::sleep(std::time::Duration::from_secs(60)) => {
                        //每60秒检查token是否即将过期，如果距离过期少于100秒，则重新登录并获取token
                        let current_timestamp = utils::current_timestamp();
                        let expires_timestamp = *self_clone.expire.read().await;
                        if expires_timestamp-current_timestamp <= 100 {
                            info!("token即将过期,重新登录获取token");
                            let ret = HyHandler::login(&self_clone.setting.hyserver.loginname, &self_clone.setting.hyserver.password, &self_clone.setting.hyserver.server).await;
                            if ret.as_ref().is_err() {  //如果登录失败，则继续等待
                                error!("login error:{}", ret.as_ref().unwrap_err().to_string());
                                continue;
                            }
                            let loginresp = ret.unwrap();
                            let mut token_wr = self_clone.token.write().await;
                            *token_wr = loginresp.0.clone();
                            let mut expire_wr = self_clone.expire.write().await;
                            *expire_wr = loginresp.1;
                            // info!("login response:{:?}", &loginresp);
                        }
                    }
                    order = rx_order.recv() => { //收到hy的订单回调信息
                        if let Some(order) = order {
                            info!("收到的订单回调信息:{:?},需要将订单发送到订单路由", &order);
                            if let Some(routemsg) = exec_from_api_to_router(&order).await {
                                if let Err(err) = tx_repay_to_router.send(routemsg).await {
                                    error!("发送订单失败: {:?}", err);
                                }
                            }
                        }
                    }
                    //收到路由中心的订单时,转换订单并发送到hy
                    result = rx_order_from_router.recv() => {
                        if let Ok(order) = result {
                            info!("收到订单：{:?}", order);
                            //转换成api需要的数据格式
                            let (new_order, cancel_order) = order_from_router_to_api(&order).await;
                            if let Some(val) = new_order {
                                info!("下单：{:?}", val);
                                if let Err(err) = HyHandler::place_order(&val, &self_clone.setting.hyserver.server, &self_clone.token.read().await).await {
                                    error!("下单err: {:?}", err);
                                    //直接拒绝
                                    let resp = rejected(&order, &err.to_string()).await;
                                    if let Err(err) = tx_repay_to_router.send(resp).await {
                                        error!("发送订单失败: {:?}", err);
                                    }
                                }
                            }
                            if let Some(val) = cancel_order {
                                info!("撤单: {:?}", val);
                                if let Err(err) = HyHandler::cancel_order(&val, &self_clone.setting.hyserver.server, &self_clone.token.read().await).await {
                                    error!("撤单err: {:?}", err);
                                    let resp = rejected(&order, &err.to_string()).await;
                                    if let Err(err) = tx_repay_to_router.send(resp).await {
                                        error!("发送订单失败: {:?}", err);
                                    }
                                }

                            }
                        }
                    },
                }
            }
        });
        Ok(())
    }
}
