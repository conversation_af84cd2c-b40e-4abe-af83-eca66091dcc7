C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe -E rm -f CMakeFiles\demo_basic_binding.dir/objects.a
C:\msys64\mingw64\bin\ar.exe qc CMakeFiles\demo_basic_binding.dir/objects.a @CMakeFiles\demo_basic_binding.dir\objects1.rsp
C:\msys64\mingw64\bin\g++.exe -g -Wl,--whole-archive CMakeFiles\demo_basic_binding.dir/objects.a -Wl,--no-whole-archive -o demo_basic_binding.exe -Wl,--out-implib,libdemo_basic_binding.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\demo_basic_binding.dir\linkLibs.rsp
