use strum_macros::{AsRefStr, Display, EnumString};

pub const BEGIN_STRING: &str = "v1.0";

/// 系统事件类型枚举
#[derive(Debug, Clone, PartialEq, Eq, AsRefStr, Display, EnumString)]
#[strum(serialize_all = "snake_case")]
pub enum SystemEvent {
    /// 用户认证登录事件
    SysAuthLoggedInEvent,
    /// 系统操作日志事件
    SysAuditOperationLoggedEvent,
    /// API密钥验证事件
    SysAuthApiKeyValidatedEvent,
    /// 更新缓存时间
    SysUpdateCache,
}
//1:USER(用户直连) 2:AGENT(代理托管)
#[derive(Debug, C<PERSON>, Copy, PartialEq, Eq)]
pub enum TradeMode {
    Unknown = 0, //为止
    User = 1,    //用户直连
    Agent = 2,   //代理托管
}

//委托类型 1:app下单  2:跟单  3:风控止盈止损平仓单,4:风控总资产预警平仓单 5:pc客户端单 6:结算平仓单 7:管理端强平仓单,8:app清仓,9:pc清仓,10,管理员平仓,11,合约到期日强平,12,算法单
