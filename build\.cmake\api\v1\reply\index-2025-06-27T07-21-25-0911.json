{"cmake": {"generator": {"multiConfig": false, "name": "MinGW Makefiles"}, "paths": {"cmake": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/bin/cmake.exe", "cpack": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/bin/cpack.exe", "ctest": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/bin/ctest.exe", "root": "C:/Users/<USER>/rainerix/development/cmake-3.27.3/share/cmake-3.27"}, "version": {"isDirty": false, "major": 3, "minor": 27, "patch": 3, "string": "3.27.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-2991d6a79a38960c2266.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "cache-v2-6d7bf33c73a1067a7c6d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-523952b37a3df0e1f804.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-b2cff1bac026e074d2d8.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-6d7bf33c73a1067a7c6d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-2991d6a79a38960c2266.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "toolchains-v1-b2cff1bac026e074d2d8.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-523952b37a3df0e1f804.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}