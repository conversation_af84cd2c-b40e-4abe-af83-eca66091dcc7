use server_fixbaopan::settings::Settings;
use std::error::Error;
use tracing::info;

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    let prefix = "fixclient";
    let dir = "./logs";
    let _guard = server_initialize::initialize_tracing(prefix, dir);

    let setting = Settings::init("config/fix.toml")?;
    info!("config: {:#?}", setting);

    server_fixbaopan::ServerHandler::run(&setting).await;

    Ok(())
}
