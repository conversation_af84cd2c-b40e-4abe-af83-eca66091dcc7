use anyhow::{anyhow, Result};
use r2d2::Pool;
use redis::{cluster::ClusterClient, cluster::ClusterClientBuilder, ConnectionLike, RedisError, RedisResult, ToRedisArgs};
use std::collections::BTreeMap;
use std::time::Duration;
use tracing::*;

#[derive(Debug, <PERSON>lone)]
pub struct RedisInstance {
    pub urls: Vec<String>,
    // pub password: String,
}

impl Default for RedisInstance {
    fn default() -> Self {
        Self {
            urls: vec![],
            // password: "".to_string(),
        }
    }
}

impl RedisInstance {
    fn new(cfg: &RedisConfig) -> RedisInstance {
        let urls: Vec<String> = cfg.urls.split(",").map(|s| s.to_string()).collect();
        RedisInstance {
            urls,
            // password: todo!(),
        }
    }

    pub fn get_client(&self) -> RedisResult<ClusterClient> {
        let cb = ClusterClientBuilder::new(self.urls.clone());
        // if !self.password.is_empty() {
        //     cb = cb.password(self.password.clone());
        // }
        cb.build()
    }
}

type RedisConnection = Box<redis::cluster::ClusterConnection>;

// impl RedisConnection {
//   pub fn is_open(&self) -> bool {
//     self.is_open()
//   }

//   pub fn query<T: FromRedisValue>(&mut self, cmd: &redis::Cmd) -> RedisResult<T> {
//     return match self.req_command(cmd) {
//       Ok(val) => from_redis_value(&val),
//       Err(e) => Err(e),
//     };
//   }
// }

#[derive(Clone)]
pub struct RedisConnectionManager {
    pub redis_client: ClusterClient,
}

// ToDo 实现 broken 函数
impl r2d2::ManageConnection for RedisConnectionManager {
    type Connection = RedisConnection;
    type Error = RedisError;

    fn connect(&self) -> Result<RedisConnection, Self::Error> {
        let conn = self.redis_client.get_connection();

        match conn {
            Ok(conn) => Ok(RedisConnection::new(conn)),
            Err(err) => Err(err),
        }
    }

    fn is_valid(&self, conn: &mut RedisConnection) -> Result<(), Self::Error> {
        let r: RedisResult<redis::Value> = redis::cmd("PING").query(conn);
        if r.as_ref().is_err() {
            let err = r.unwrap_err();
            info!("Redis PING error->{}", err);
            return Err(err);
        }

        Ok(())
    }

    fn has_broken(&self, conn: &mut RedisConnection) -> bool {
        !conn.is_open()
    }
}

#[derive(Debug, Clone)]
pub struct RedisConfig {
    pub urls: String,
    pub prefix: String,
    pub max_size: usize,
    pub min_idle: usize,
    pub connection_timeout: usize,
}

impl Default for RedisConfig {
    fn default() -> Self {
        Self {
            urls: "".to_string(),
            prefix: "".to_string(),
            max_size: 4,
            min_idle: 1,
            connection_timeout: 30,
        }
    }
}

#[derive(Clone)]
pub struct RedisClient {
    pub prefix: String,
    pub pool: Pool<RedisConnectionManager>,
}

impl RedisClient {
    pub fn new(rediscfg: &RedisConfig) -> Result<RedisClient> {
        let redis_client = RedisInstance::new(&rediscfg).get_client();
        if redis_client.as_ref().is_err() {
            return Err(anyhow!("redis client init error"));
        }
        let redis_client = redis_client.unwrap();
        let manager = RedisConnectionManager { redis_client };
        let pool = r2d2::Pool::builder()
            .max_size(rediscfg.max_size as u32)
            .min_idle(Some(rediscfg.min_idle as u32))
            .connection_timeout(Duration::from_secs(rediscfg.connection_timeout as u64))
            .build(manager);
        if pool.as_ref().is_err() {
            return Err(anyhow!("redis pool init error"));
        }
        let pool = pool.unwrap();
        Ok(RedisClient {
            prefix: rediscfg.prefix.clone(),
            pool,
        })
    }

    pub async fn get_value_by_hgetall(&self, key: &str) -> BTreeMap<String, String> {
        let key = format!("{}_{}", self.prefix, key);
        let mut ret: BTreeMap<String, String> = BTreeMap::new();
        if let Ok(mut conn) = self.pool.get() {
            // if let Ok(mut conn) = self.pool.get_connection() {
            let query: Result<BTreeMap<String, String>, RedisError> = redis::cmd("HGETALL").arg(key).query(&mut conn);

            if let Ok(query_ret) = query {
                ret = query_ret;
            }
        } else {
            error!("redis pool get connection error");
        }

        ret
    }

    pub async fn get_value_by_get(&self, key: &str) -> String {
        let key = format!("{}_{}", self.prefix, key);
        let ret: String = String::from(""); //

        if let Ok(mut conn) = self.pool.get() {
            let query: RedisResult<redis::Value> = redis::cmd("GET").arg(key.clone()).query(&mut conn);
            if let Ok(value) = query {
                match value {
                    redis::Value::Nil => return ret,
                    _ => return redis::from_redis_value(&value).unwrap(),
                };
            } else {
                info!("get key: {}", key);
                error!("query redis error: {:?}", query.unwrap_err());
            }
        } else {
            error!("redis pool get connection error");
        }

        ret
    }
    //   pub async fn get_value_by_get(&mut self, key: &str) -> String {
    //     let mut ret: String = String::from(""); //

    //     if let Ok(mut conn) = self.pool.get() {
    //       let query = conn.query::<String>(&redis::cmd("GET").arg(key));
    //       if let Ok(query_ret) = query {
    //         ret = query_ret;
    //       } else {
    //         error!("query redis error: {:?}", query.unwrap_err());
    //       }
    //     }
    //     // }
    //     ret
    //   }

    pub async fn get_value_by_hget(&self, key: &str, field: &str) -> String {
        let key = format!("{}_{}", self.prefix, key);
        let ret: String = String::from(""); // = BTreeMap::new();
                                            // if let Ok(mut conn) = self.pool.get_connection() {
        if let Ok(mut conn) = self.pool.get() {
            let query: RedisResult<redis::Value> = redis::cmd("HGET").arg(key).arg(field).query(&mut conn);
            if let Ok(value) = query {
                match value {
                    redis::Value::Nil => return ret,
                    _ => return redis::from_redis_value(&value).unwrap(),
                };
            }
        } else {
            error!("redis pool get connection error");
        }

        ret
    }

    pub async fn get_values_by_lrange(&self, key: &str) -> Result<Vec<String>> {
        let key = format!("{}_{}", self.prefix, key);
        if let Ok(mut conn) = self.pool.get() {
            let query: Result<Vec<String>, RedisError> = redis::cmd("lrange").arg(key).arg(0).arg(-1).query(&mut conn);
            match query {
                Ok(ret) => Ok(ret),
                Err(e) => Err(anyhow!("Error: {:?}", e)),
            }
        } else {
            error!("redis pool get connection error");
            Err(anyhow!("redis pool get connection error"))
        }
    }

    pub async fn pop_value_in_list(&self, key: &str) -> Result<String> {
        let key = format!("{}_{}", self.prefix, key);
        if let Ok(mut conn) = self.pool.get() {
            let query: Result<String, RedisError> = redis::cmd("LPOP").arg(key).query(&mut conn);
            match query {
                Ok(ret) => Ok(ret),
                Err(e) => Err(anyhow!("Error: {:?}", e)),
            }
        } else {
            error!("redis pool get connection error");
            Err(anyhow!("redis pool get connection error"))
        }
    }

    pub async fn get_keys_from_hash(&self, key: &str) -> Result<Vec<String>> {
        let key = format!("{}_{}", self.prefix, key);
        // info!("get_keys_from_hash key:{}", key);
        if let Ok(mut conn) = self.pool.get() {
            let query: Result<Vec<String>, RedisError> = redis::cmd("hkeys").arg(key).query(&mut conn);
            match query {
                Ok(ret) => Ok(ret),
                Err(e) => Err(anyhow!("Error: {:?}", e)),
            }
        } else {
            error!("redis pool get connection error");
            Err(anyhow!("redis pool get connection error"))
        }
    }

    pub async fn get_key_is_exists(&self, key: &str) -> bool {
        let key = format!("{}_{}", self.prefix, key);
        info!("get_key_is_exists key:{}", key);
        let mut flag = false;
        if let Ok(mut conn) = self.pool.get() {
            let query: Result<bool, RedisError> = redis::cmd("exists").arg(key).query(&mut conn);
            if let Ok(query_ret) = query {
                flag = query_ret;
            }
        } else {
            error!("redis pool get connection error");
        }

        flag
    }

    pub async fn get_len(&self, key: &str) -> Result<i64> {
        let key = format!("{}_{}", self.prefix, key);
        // info!("get_len key:{}", key);
        let mut ret = 0;
        if let Ok(mut conn) = self.pool.get() {
            let query: Result<i64, RedisError> = redis::cmd("llen").arg(key).query(&mut conn);
            if let Ok(query_ret) = query {
                ret = query_ret;
            }
        } else {
            error!("redis pool get connection error");
        }

        Ok(ret)
    }

    pub async fn set_str_value(&self, key: &str, timeout: i64, value: &str) -> Result<()> {
        let key = format!("{}_{}", self.prefix, key);
        // info!("set_str_value key:{}", key);
        if let Ok(mut conn) = self.pool.get() {
            let query: Result<String, RedisError> = redis::cmd("SETEX").arg(key).arg(timeout).arg(value).query(&mut conn);
            if let Ok(_r) = query {
            } else {
                info!("set_str_value失败,{:?}", query);
                return Err(anyhow!("redis写入失败"));
            }
        } else {
            error!("redis pool get connection error");
        }

        return Result::Ok(());
    }

    pub async fn push_value_to_list<T: ToRedisArgs>(&self, key: &str, value: T) -> Result<()> {
        let key = format!("{}_{}", self.prefix, key);
        // info!("push_value_to_list key:{}", key);
        if let Ok(mut conn) = self.pool.get() {
            let query: Result<String, RedisError> = redis::cmd("RPUSH").arg(key).arg(value).query(&mut conn);
            match query {
                Ok(_) => Ok(()),
                Err(e) => Err(anyhow!("Error: {:?}", e)),
            }
        } else {
            error!("redis pool get connection error");
            Err(anyhow!("redis pool get connection error"))
        }
    }

    pub async fn set_hash_value<T: ToRedisArgs>(&self, key: &str, value: T) -> Result<()> {
        let key = format!("{}_{}", self.prefix, key);
        // info!("set_hash_value key:{}", key);
        if let Ok(mut conn) = self.pool.get() {
            let query: Result<String, RedisError> = redis::cmd("HMSET").arg(key).arg(value).query(&mut conn);
            if let Ok(_r) = query {
            } else {
                info!("set_hash_value失败,{:?}", query);
                return Err(anyhow!("redis写入失败"));
            }
        } else {
            error!("redis pool get connection error");
        }

        return Ok(());
    }

    pub async fn lock(&self, key: &str, ex: i32) -> i32 {
        let key = format!("{}_{}", self.prefix, key);
        // info!("lock by key:{}", key);
        let mut ret: i32 = 0;

        if let Ok(mut conn) = self.pool.get() {
            let query: Result<String, RedisError> = redis::cmd("SET").arg(key.clone()).arg(1).arg("ex").arg(ex).arg("nx").query(&mut conn);
            if let Ok(r) = query {
                info!("加锁成功: {} {}", key, r);
                ret = 1;
            } else {
                info!("redis加锁失败,订单: {}, {:?},{}", key, query, utils::current_timestamp());
            }
        } else {
            error!("redis pool get connection error");
        }

        ret
    }

    pub async fn delele(&self, key: &str) {
        let key = format!("{}_{}", self.prefix, key);
        // info!("delete by key:{}", key);
        // println!("del {}", key);

        if let Ok(mut conn) = self.pool.get() {
            let query: Result<(), RedisError> = redis::cmd("DEL").arg(key).query(&mut conn);
            if let Ok(_r) = query {
            } else {
                info!("redis删除失败,{:?}", query);
            }
        } else {
            error!("redis pool get connection error");
        }
    }
    pub async fn get_values_by_lindex(&self, key: &str, index: i32) -> String {
        let key = format!("{}_{}", self.prefix, key);
        // info!("get_values_by_lindex key:{}", key);
        let mut ret: String = String::from("");

        if let Ok(mut conn) = self.pool.get() {
            let query: Result<String, RedisError> = redis::cmd("LINDEX").arg(key).arg(index).query(&mut conn);
            if let Ok(query_ret) = query {
                ret = query_ret;
            }
        } else {
            error!("redis pool get connection error");
        }

        ret
    }
}
