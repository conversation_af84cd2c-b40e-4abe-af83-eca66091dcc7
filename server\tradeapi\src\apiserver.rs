// use crate::ConnectionManager;
use crate::dataservice::entities::prelude::*;
// use crate::{ConnectionManager, OrderCenterClient, Settings, dbsetup::DbConnection};
use crate::connectionmanager::*;
use crate::dataservice::dbsetup::DbConnection;
use crate::ordercenterclient::*;
use crate::svrconfig::*;
// use crate::{OrderCenterClient, Settings, dbsetup::DbConnection};
use anyhow::{Result, anyhow};
use chrono::Local;
use moka::future::Cache;
use server_constant::*;
use server_protoes::*;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::*;
use utils::md5;
// let clients: Arc<Mutex<HashMap<u32, mpsc::Sender<String>>>> = Arc::new(Mutex::new(HashMap::new()));
// type ClientMap = Arc<RwLock<HashMap<u32, mpsc::Sender<TaMessageRespPack>>>>;
// pub static GLOBAL_CLIENTS_CACHE: Lazy<Cache<String, FramedWrite<TaMessageRespPack, RespCodec>>> = Lazy::new(|| Cache::builder().time_to_live(std::time::Duration::from_secs(5 * 60)).build());
// pub static GLOBAL_CLIENTS_CACHE: Lazy<ClientMap> = Lazy::new(|| Arc::new(RwLock::new(HashMap::new())));

// 用于通道传输的结构体
#[derive(Debug)]
pub struct Task {
    pub request: TaMessageReqPack,
    pub sender: mpsc::Sender<TaMessageRespPack>,
    pub client_addr: String, // 记录发起请求的客户端地址
}

#[derive(Debug, Clone)]
#[allow(dead_code)]
pub struct ApiServer {
    setting: Settings,
    orderclient: OrderCenterClient,
    stockdb: Arc<DbConnection>,
    financedb: Arc<DbConnection>,
    customerdb: Arc<DbConnection>,
    stock_cache: Arc<HashMap<String, SyscommodityCache>>,
    order_cache: Cache<i64, String>,
}

#[derive(Debug, Clone)]
pub struct SyscommodityCache {
    pub stock_id: i64,
    pub code: String,
    pub name: String,
    pub exchange_id: i64,
}

impl ApiServer {
    pub async fn new(setting: Settings /*,logontx:tokio::sync::mpsc::Sender<TaLogonReq>,resptx:tokio::sync::mpsc::Sender<TaMessageRespPack> */) -> Self {
        let orderclient = OrderCenterClient::init(setting.servers.ordercenter.to_string()).await;
        //let orderclient = OrderCenterClient::default();

        let stockdb_url = &setting.dbconfig.stockdb;
        info!(stockdb_url, "开始连接 stockdb");
        let stockdb = Arc::new(DbConnection::new(stockdb_url).await);
        let financedb_url = &setting.dbconfig.financedb;
        info!(financedb_url, "开始连接 financedb");
        let financedb = Arc::new(DbConnection::new(financedb_url).await);
        let customerdb_url = &setting.dbconfig.customerdb;
        info!(customerdb_url, "开始连接 customerdb");
        let customerdb = Arc::new(DbConnection::new(customerdb_url).await);
        let stock_vec = Syscommodity::query_all(&financedb).await;
        let mut stock_hash = HashMap::new();
        if let Ok(data) = stock_vec {
            for item in data {
                stock_hash.insert(
                    item.inner_code.clone(),
                    SyscommodityCache {
                        stock_id: item.id,
                        code: item.code,
                        name: item.name,
                        exchange_id: item.market_id,
                    },
                );
            }
        }
        let stock_cache = Arc::new(stock_hash);
        info!("缓存数据初始化成功!");
        let order_cache = Cache::builder().build();
        ApiServer {
            setting,
            orderclient,
            stockdb,
            financedb,
            customerdb,
            stock_cache,
            order_cache,
        }
    }

    #[instrument(name = "apiserver_run", skip_all)]
    pub async fn run(&self, mut rx: mpsc::Receiver<Task>, conn_manager: Arc<ConnectionManager>) {
        while let Some(task) = rx.recv().await {
            info!("处理业务逻辑 request from {} with:{:?}", /*task.request.header, */ task.client_addr, task);

            let request = task.request;
            let header = request.header.clone();
            if header.is_none() {
                let header = TaMessageHeader {
                    begin_string: BEGIN_STRING.to_string(),
                    message_type: MessageType::Undefined as i32,
                    sender_compid: "".to_string(),
                    sending_time: utils::current_timestamp(),
                    client_id: "".to_string(),
                };
                let resp = TaMessageResp {
                    error_code: RetCode::Error as i32,
                    error_msg: "报文错误，没有报文头".to_string(),
                };
                let response = TaMessageRespPack {
                    header: Some(header),
                    orderexec: None,
                    resp: Some(resp),
                };
                // 发送响应给特定客户端
                //send_to_client_id 该函数是通过clientid来寻找IP地址并发送
                if let Err(e) = conn_manager.send_to_client(&task.client_addr, response).await {
                    error!("Failed to send response to {}: {}", task.client_addr, e);
                }
                return;
            }
            //这里处理具体的业务逻辑
            // info!("我在努力处理业务逻辑...(handle_order_req)...");
            let mut resp = self.handle_order_req(&request, &task.client_addr, &conn_manager).await;
            info!("业务处理完成，本次数据将发送给 {}", &task.client_addr);

            resp.header = header;

            // 发送响应给特定客户端
            //send_to_client_id 该函数是通过clientid来寻找IP地址并发送
            if let Err(e) = conn_manager.send_to_client(&task.client_addr, resp).await {
                error!("Failed to send response to {}: {}", task.client_addr, e);
            }
        }
    }

    async fn handle_order_req(&self, msg: &TaMessageReqPack, addr: &str, conn_manager: &Arc<ConnectionManager>) -> TaMessageRespPack {
        // info!("处理中......:{:?}", msg);

        let mut resp = TaMessageRespPack::default();

        let mut rp = TaMessageResp {
            error_code: RetCode::Ok as i32,
            error_msg: "".to_string(),
        };

        if let Some(header) = &msg.header {
            resp.header = Some(header.to_owned());

            //if header.message_type != MessageType::PlaceOrder as i32 {}

            match header.message_type {
                x if x == MessageType::Logon as i32 => {
                    info!("logon with logon info:{:?}", msg.logon);
                    match &msg.logon {
                        Some(v) => match self.handle_logon(v).await {
                            Ok(clientid) => {
                                conn_manager.update_client(addr.to_string(), &clientid).await;
                            }
                            Err(err) => {
                                error!("log on error");
                                rp.error_code = RetCode::Error as i32;
                                rp.error_msg = err.to_string();
                                resp.resp = Some(rp);
                            }
                        },
                        None => {
                            rp.error_code = RetCode::Error as i32;
                            rp.error_msg = format!("invalid logon message");
                            resp.resp = Some(rp);
                        }
                    }
                }
                x if x == MessageType::PlaceOrder as i32 => {
                    info!("place order:{:?}", &msg);

                    //判断clientid 是否存在
                    let order = msg.to_owned().order;
                    if order.is_some() {
                        let order = order.unwrap();
                        if !conn_manager.exists_client(order.account.to_string().as_str()).await {
                            rp.error_code = RetCode::Error as i32;
                            rp.error_msg = "Please log in".to_string();
                            resp.resp = Some(rp);
                        } else {
                            let ret = self.handle_place_order(&order).await;
                            match ret {
                                Err(err) => {
                                    rp.error_code = RetCode::Error as i32;
                                    rp.error_msg = err;
                                    resp.resp = Some(rp);
                                }
                                Ok(data) => {
                                    if data.is_some() {
                                        resp.orderexec = data;
                                    } else {
                                        resp.resp = Some(rp);
                                    }
                                }
                            }
                        }
                    } else {
                        rp.error_code = RetCode::Error as i32;
                        rp.error_msg = "parameter error".to_string();
                        resp.resp = Some(rp);
                    }
                }
                x if x == MessageType::CancelOrder as i32 => {
                    info!("cancel order:{:?}", &msg);
                    let ret = self.handle_cancel_order(msg.to_owned()).await;
                    match ret {
                        Err(err) => {
                            rp.error_code = RetCode::Error as i32;
                            rp.error_msg = err;
                            resp.resp = Some(rp);
                        }
                        Ok(_) => {}
                    }
                }
                // x if x == MessageType::OrderExec as i32 => error!("message type error"),
                // x if x == MessageType::Undefined as i32 => error!("undefined message"),
                // x if x == MessageType::Resp as i32 => error!("message type error with resp"),
                _ => {
                    error!("error message type:{}", header.message_type);
                    rp.error_code = RetCode::Error as i32;
                    rp.error_msg = format!("error message type:{}", header.message_type);
                    resp.resp = Some(rp);
                }
            }
        } else {
            rp.error_code = RetCode::Error as i32;
            rp.error_msg = format!("没有消息头");
            resp.resp = Some(rp);
        }
        resp
    }

    async fn handle_logon(&self, logon: &TaLogonReq) -> Result<String> {
        let ret = UsersTradeAccount::query_account(&self.customerdb, &logon.username).await;
        if let Err(_) = ret {
            return Err(anyhow!("login error"));
        }
        let ret = ret.unwrap();
        if ret.is_none() {
            return Err(anyhow!("login username is not exists"));
        }
        let ret = ret.unwrap();
        let pwd = md5(&logon.password);

        if !pwd.eq(&ret.pwd) {
            return Err(anyhow!("login password is error"));
        }
        let clientid = logon.client_id.clone();
        //处理登录的逻辑，暂时不处理
        Ok(clientid)
    }

    async fn handle_place_order(&self, order: &TaEntrustOrderReq) -> Result<Option<TaOrderExecResp>, String> {
        // let order = msg.order;
        // if order.is_none() {
        //     return Err("Order parameters are empty".to_string());
        // }
        // //----------- 仅用于测试 --------------------------
        // // let order_req = OrderReq::default();
        // // let ret = self.orderclient.send_order(&order_req).await;
        // // info!("result is:{:?}", &ret);
        // //-------------------------------------
        // let order = order.unwrap();
        let mut rp = TaOrderExecResp {
            cl_ord_id: order.cl_ord_id.clone(),
            account: order.account.to_string(),
            ..Default::default()
        };

        let order_price = order.price.parse::<f64>().unwrap_or_default();
        let timenow = Local::now().timestamp();
        //查询品种
        let mut stock_key = String::from("");
        if order.security_exchange == SecurityExchange::Xshg as i32 {
            stock_key = format!("{}_XSHG", order.symbol);
        } else if order.security_exchange == SecurityExchange::Xshe as i32 {
            stock_key = format!("{}_XSHE", order.symbol);
        } else if order.security_exchange == SecurityExchange::Xhkg as i32 {
            stock_key = format!("{}_XHKG", order.symbol);
        }

        if !self.stock_cache.contains_key(&stock_key) {
            rp.order_status = OrderExecStatus::Refuse as i32;
            rp.mark = "symbol is not exists".to_string();
            return Ok(Some(rp));
        }
        let stockid = self.stock_cache.get(&stock_key).clone().unwrap().stock_id;
        let order_req = OrderReq {
            msg_id: timenow,
            unit_id: order.account as i64,
            stock_id: stockid,
            order_direction: order.side,
            order_qty: order.order_qty,
            order_type: 1,
            order_price: order_price,
            operator_no: order.account as i64,
            price_type: order.ord_type,
            trade_mode: TradeMode::User as i32,
            user_id: order.account as i64,
            ..Default::default()
        };

        let ret = self.orderclient.send_order(&order_req).await;
        if let Err(err) = ret {
            rp.order_status = OrderExecStatus::Refuse as i32;
            rp.mark = err;
            return Ok(Some(rp));
        }
        let ret = ret.unwrap();

        //关联关系保存到数据库
        let order_ext = PhoenixOrdStockorderExt {
            order_id: ret,
            unit_id: order.account as i64,
            ext_order_id: order.cl_ord_id.clone(),
            create_date: timenow,
            clientid: order.account as i64,
            ..Default::default()
        };
        //缓存order_no对应关系，回报时使用
        self.order_cache.insert(ret, order.cl_ord_id.clone()).await;

        let ret = PhoenixOrdStockorderExt::insert_data(order_ext, &self.stockdb).await;
        if let Err(err) = ret {
            return Err(err);
        }

        Ok(None)
    }

    async fn handle_cancel_order(&self, msg: TaMessageReqPack) -> Result<i32, String> {
        let order = msg.cancelorder;
        if order.is_none() {
            return Err("cancel order parameters are empty".to_string());
        }

        let order = order.unwrap();
        let cl_ord_id = order.orig_cl_ord_id.parse::<i64>().unwrap_or_default();
        let timenow = Local::now().timestamp();
        let cancel_order_req = CancelReq {
            msg_id: timenow,
            unit_id: order.account as i64,
            order_id: cl_ord_id,
            operator_no: order.account as i64,
            cancel_type: 1,
            trade_mode: 1,
            user_id: order.account as i64,
            ..Default::default()
        };
        self.orderclient.cancel_order(&cancel_order_req).await
    }

    pub async fn query_orderno(&self, orderno: i64) -> String {
        if self.order_cache.contains_key(&orderno) {
            let cl_order_no = self.order_cache.get(&orderno).await.clone().unwrap();
            return cl_order_no;
        }

        let ret = PhoenixOrdStockorderExt::query(orderno, &self.stockdb).await;
        if let Err(_) = ret {
            return "".to_string();
        }

        let ret = ret.unwrap();
        if ret.is_none() {
            return "".to_string();
        } else {
            let ret = ret.unwrap().ext_order_id;
            self.order_cache.insert(orderno, ret.clone()).await;
            ret
        }
    }
}
