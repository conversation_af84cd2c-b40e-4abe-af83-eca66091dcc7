use crate::protoes::*;
// use bincode::config::{self, BigEndian};
use byteorder::{BigEndian, ByteOrder};
use bytes::{BufMut, BytesMut};
use prost::Message;
use tokio_util::codec::{Decoder, Encoder};

/// 为请求类型Req做的Codec解析
pub struct ReqCodec {
    // config: config::Configuration<BigEndian>,
}
impl ReqCodec {
    /// 最多传送1G数据
    const MAX_SIZE: usize = 1024 * 1024 * 1024 * 8;
    pub fn new() -> Self {
        // let config = config::standard().with_big_endian().with_variable_int_encoding();
        // ReqCodec { config }
        ReqCodec {}
    }
}

impl Encoder<TaMessageReqPack> for ReqCodec {
    // type Error = bincode::Error;
    type Error = std::io::Error;
    // 使用bincode将msg转换为&[u8]，也可以使用serde_json::to_vec()，前者效率更高一些
    fn encode(&mut self, item: TaMessageReqPack, dst: &mut BytesMut) -> Result<(), Self::Error> {
        // // let config = config::standard().with_big_endian().with_variable_int_encoding();
        // let data = bincode::encode_to_vec(&item, self.config).map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e))?;
        // let data = data.as_slice();

        // // 要传输的实际数据的长度
        // let data_len = data.len();
        // if data_len > Self::MAX_SIZE {
        //     return Err(std::io::Error::new(std::io::ErrorKind::Other, "frame is too large".to_string()));
        // }

        // // 最大传输u32的数据(可最多512G)，
        // // 表示数据长度的u32数值占用4个字节
        // dst.reserve(data_len + 4);

        // // 先将长度值写入dst，即帧首，
        // // 写入的字节序是大端的u32，读取时也要大端格式读取，
        // // 也有小端的方法`put_u32_le()`，读取时也得小端读取
        // dst.put_u32(data_len as u32);

        // // 再将实际数据放入帧尾
        // dst.extend_from_slice(data);

        let mut payload = prost::bytes::BytesMut::new();
        let _ = item.encode(&mut payload).unwrap();
        // 要传输的实际数据的长度
        let payload_len = payload.len();
        if payload_len > Self::MAX_SIZE {
            return Err(std::io::Error::new(std::io::ErrorKind::Other, "frame is too large".to_string()));
        }

        dst.reserve(payload_len + 4); //重置len
        dst.put_u32(payload_len as u32); //按大端字节顺序将一个无符号的32位整数写入self。
        dst.put(payload); //追加消息
        Ok(())
    }
}

impl Decoder for ReqCodec {
    type Item = TaMessageReqPack;
    type Error = std::io::Error;
    // 从不断被填充的Bytes buf中读取数据，并将其转换到目标类型
    fn decode(&mut self, src: &mut BytesMut) -> Result<Option<Self::Item>, Self::Error> {
        // // let config = config::standard().with_big_endian().with_variable_int_encoding();
        // let buf_len = src.len();

        // // 如果buf中的数据量连长度声明的大小都不足，则先跳过等待后面更多数据的到来
        // if buf_len < 4 {
        //     return Ok(None);
        // }

        // // 先读取帧首，获得声明的帧中实际数据大小
        // let mut length_bytes = [0u8; 4];
        // length_bytes.copy_from_slice(&src[..4]);
        // let data_len = u32::from_be_bytes(length_bytes) as usize;
        // if data_len > Self::MAX_SIZE {
        //     return Err(std::io::Error::new(
        //         std::io::ErrorKind::InvalidData,
        //         format!("Frame of length {} is too large.", data_len),
        //     ));
        // }

        // // 帧的总长度为 4 + frame_len
        // let frame_len = data_len + 4;

        // // buf中数据量不够，跳过，并预先申请足够的空闲空间来存放该帧后续到来的数据
        // if buf_len < frame_len {
        //     src.reserve(frame_len - buf_len);
        //     return Ok(None);
        // }

        // // 数据量足够了，从buf中取出数据转编成帧，并转换为指定类型后返回
        // // 需同时将buf截断(split_to会截断)
        // let frame_bytes = src.split_to(frame_len);
        // match bincode::decode_from_slice(&frame_bytes[4..], self.config) {
        //     Ok(frame) => Ok(Some(frame.0)),
        //     Err(e) => Err(std::io::Error::new(std::io::ErrorKind::InvalidData, e)),
        // }

        let size = {
            //获取消息长度
            if src.len() < 4 {
                return Ok(None);
            }
            BigEndian::read_u32(src.as_ref()) as usize //4个字节
        };

        if size > Self::MAX_SIZE {
            return Err(std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                format!("Frame of length {} is too large.", size),
            ));
        }

        if src.len() >= size + 4 {
            let _ = src.split_to(4); //在给定索引处将缓冲区一分为二。然后self包含元素[at，len），返回的BytesMut包含元素[0，at）。
            let buf = src.split_to(size); //截取消息
            Ok(Some(TaMessageReqPack::decode(buf)?)) //解析
        } else {
            Ok(None)
        }
    }
}
