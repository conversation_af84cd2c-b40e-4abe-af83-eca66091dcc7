# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe

# The command to remove a file.
RM = C:\Users\<USER>\rainerix\development\cmake-3.27.3\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\fix\quickfix-ffi

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build

# Include any dependencies generated for this target.
include examples/CMakeFiles/demo_basic_binding.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include examples/CMakeFiles/demo_basic_binding.dir/compiler_depend.make

# Include the progress variables for this target.
include examples/CMakeFiles/demo_basic_binding.dir/progress.make

# Include the compile flags for this target's objects.
include examples/CMakeFiles/demo_basic_binding.dir/flags.make

examples/CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.obj: examples/CMakeFiles/demo_basic_binding.dir/flags.make
examples/CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.obj: examples/CMakeFiles/demo_basic_binding.dir/includes_C.rsp
examples/CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.obj: C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/fix/quickfix-ffi/examples/demo_basic_binding.c
examples/CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.obj: examples/CMakeFiles/demo_basic_binding.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object examples/CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.obj"
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\examples && C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT examples/CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.obj -MF CMakeFiles\demo_basic_binding.dir\demo_basic_binding.c.obj.d -o CMakeFiles\demo_basic_binding.dir\demo_basic_binding.c.obj -c C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\fix\quickfix-ffi\examples\demo_basic_binding.c

examples/CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.i"
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\examples && C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\fix\quickfix-ffi\examples\demo_basic_binding.c > CMakeFiles\demo_basic_binding.dir\demo_basic_binding.c.i

examples/CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.s"
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\examples && C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\fix\quickfix-ffi\examples\demo_basic_binding.c -o CMakeFiles\demo_basic_binding.dir\demo_basic_binding.c.s

# Object files for target demo_basic_binding
demo_basic_binding_OBJECTS = \
"CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.obj"

# External object files for target demo_basic_binding
demo_basic_binding_EXTERNAL_OBJECTS =

examples/demo_basic_binding.exe: examples/CMakeFiles/demo_basic_binding.dir/demo_basic_binding.c.obj
examples/demo_basic_binding.exe: examples/CMakeFiles/demo_basic_binding.dir/build.make
examples/demo_basic_binding.exe: quickfix-bind/libquickfixbind.a
examples/demo_basic_binding.exe: examples/CMakeFiles/demo_basic_binding.dir/linkLibs.rsp
examples/demo_basic_binding.exe: examples/CMakeFiles/demo_basic_binding.dir/objects1.rsp
examples/demo_basic_binding.exe: examples/CMakeFiles/demo_basic_binding.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable demo_basic_binding.exe"
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\examples && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\demo_basic_binding.dir\link.txt --verbose=$(VERBOSE)
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\examples && C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/Users/<USER>/rainerix/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary C:/Users/<USER>/rainerix/lanlian/phoenix_tradeapi/build/examples/demo_basic_binding.exe -installedDir C:/Users/<USER>/rainerix/vcpkg/installed/x86-windows/debug/bin -OutVariable out

# Rule to build all files generated by this target.
examples/CMakeFiles/demo_basic_binding.dir/build: examples/demo_basic_binding.exe
.PHONY : examples/CMakeFiles/demo_basic_binding.dir/build

examples/CMakeFiles/demo_basic_binding.dir/clean:
	cd /d C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\examples && $(CMAKE_COMMAND) -P CMakeFiles\demo_basic_binding.dir\cmake_clean.cmake
.PHONY : examples/CMakeFiles/demo_basic_binding.dir/clean

examples/CMakeFiles/demo_basic_binding.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\fix\quickfix-ffi C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\fix\quickfix-ffi\examples C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\examples C:\Users\<USER>\rainerix\lanlian\phoenix_tradeapi\build\examples\CMakeFiles\demo_basic_binding.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : examples/CMakeFiles/demo_basic_binding.dir/depend

